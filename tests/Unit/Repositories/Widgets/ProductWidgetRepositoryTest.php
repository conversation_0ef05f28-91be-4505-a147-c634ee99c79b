<?php

namespace Tests\Unit\Repositories\Widgets;

use App\Models\Collection;
use App\Models\Product;
use App\Models\Widget;
use App\Repositories\Widgets\ProductWidgetRepository;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductWidgetRepositoryTest extends TenantTestCase
{
    #[Test]
    public function it_selects_the_expected_columns(): void
    {
        $collection = Collection::factory()->create();

        Product::factory()
            ->times(2)
            ->create()
            ->each(fn (Product $product) => $product->collections()->save($collection));

        $widget = Widget::factory()->create();

        $this->assertEquals([
            'title', 'type_id', 'slug', 'products.id', 'sale', 'cover_photo',
            'weight', 'unit_of_issue', 'unit_price', 'sale_unit_price',
            'unit_description', 'sort_order', 'is_bundle', 'track_inventory', 'settings', 'fulfillment_id',
            'inventory', 'oos_threshold_inventory', 'back_order', 'other_inventory',
        ], (new ProductWidgetRepository($widget))->select);
    }

    #[Test]
    public function it_can_fetch_products_belonging_to_a_collection(): void
    {
        $collection = Collection::factory()->create();

        Product::factory()
            ->times(2)
            ->create()
            ->each(fn (Product $product) => $product->collections()->attach($collection->id));

        $widget = Widget::factory()->create(['settings' => ['collection' => $collection->id]]);

        $widget_products = (new ProductWidgetRepository($widget))->get();

        $this->assertCount(2, $widget_products);
    }
}