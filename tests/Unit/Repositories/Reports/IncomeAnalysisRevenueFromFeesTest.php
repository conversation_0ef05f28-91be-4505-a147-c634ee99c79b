<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Order;
use App\Models\Payment;
use App\Repositories\Reports\IncomeAnalysisRevenueFromFees;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class IncomeAnalysisRevenueFromFeesTest extends TenantTestCase
{
    #[Test]
    public function it_calculates_fee_totals(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544]);

        $results = (new IncomeAnalysisRevenueFromFees())->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_filters_out_non_confirmed_orders_by_default(): void
    {
        Order::factory()->create(['confirmed' => false, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444]);
        Order::factory()->create(['confirmed' => false, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_filters_out_canceled_orders_by_default(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => true, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444]);
        Order::factory()->create(['confirmed' => true, 'canceled' => true, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'status_id' => OrderStatus::confirmed()]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'status_id' => OrderStatus::confirmed()]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['order_status' => [OrderStatus::confirmed()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['order_status' => [OrderStatus::canceled()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_can_filter_by_payment_date(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'payment_date' => today()->addHour()]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'payment_date' => today()->addHour()]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'payment_date', 'date_range' => ['end' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->format('M jS Y'), 'end' => today()->format('M jS Y')]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'payment_date', 'date_range' => ['start' => today()->addDay()->format('M jS Y')]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'payment_date', 'date_range' => ['end' => today()->subDay()->format('M jS Y')]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'pickup_date' => today()]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'pickup_date' => today()]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'pickup_date', 'date_range' => ['start' => today()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'pickup_date', 'date_range' => ['end' => today()]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'pickup_date', 'date_range' => ['start' => today()->addDay()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['date_type' => 'pickup_date', 'date_range' => ['end' => today()->subDay()]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_export_status(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'exported' => true]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'exported' => true]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['exported' => true]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['exported' => false]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_can_filter_by_sales_channel(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'type_id' => 1]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'type_id' => 1]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['order_type_id' => [1]]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['order_type_id' => [2]]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_can_filter_by_paid_status(): void
    {
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'paid' => 1]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'paid' => 1]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['paid' => true]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['paid' => false]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }

    #[Test]
    public function it_can_filter_by_payment_method(): void
    {
        $payment = Payment::factory()->create();

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 123, 'fees_subtotal' => 222, 'tax' => 444, 'payment_id' => $payment->id]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'delivery_fee' => 223, 'fees_subtotal' => 333, 'tax' => 544, 'payment_id' => $payment->id]);

        $results = (new IncomeAnalysisRevenueFromFees)->handle([]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['payment_id' => $payment->id]);

        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->contains(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));

        $results = (new IncomeAnalysisRevenueFromFees)->handle(['payment_id' => 123]);

        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Fee income' && $result->total === 555));
        $this->assertTrue($results->doesntContain(fn($result) => $result->accounting_class === 'Sales tax' && $result->total === 988));
    }
}
