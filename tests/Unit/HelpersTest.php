<?php

namespace Tests\Unit;

use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class HelpersTest extends TestCase
{
    #[Test]
    public function it_can_format_an_int_to_money_without_a_separator(): void
    {
        $this->assertEquals('1,000.23', money(100023));
    }

    #[Test]
    public function it_can_format_an_int_to_money_with_a_separator(): void
    {
        $this->assertEquals('1.000.23', money(100023, '.'));
    }

    #[Test]
    public function it_can_format_a_float_to_money_without_a_separator(): void
    {
        $this->assertEquals('1,000.23', money(100023.23));
    }

    #[Test]
    public function it_can_format_a_float_to_money_with_a_separator(): void
    {
        $this->assertEquals('1.000.23', money(100023.23, '.'));
    }

    #[Test]
    public function it_can_create_a_cloudfront_asset_url(): void
    {
        config(['services.cloudfront.url' => 'https://cloudfront.com/path/to/']);

        $this->assertEquals(
            'https://cloudfront.com/path/to/nested/image.jpg',
            cloudfrontAsset('/nested/image.jpg')
        );
    }
}
