<?php

namespace Tests\Unit\Actions\Subscription;

use App\Actions\Subscription\SyncItemToCurrentOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\User;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncItemToCurrentOrderTest extends TenantTestCase
{
    #[Test]
    public function it_does_not_sync_item_when_there_is_not_a_current_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $past_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->subDay()]);

        $subscription_item = RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseMissing(OrderItem::class, [
            'order_id' => $past_order->id,
            'product_id' => $subscription_item->product_id
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_addon_order_item_when_not_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 2,
            'subtotal' => 2000
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_existing_addon_order_item_when_product_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
            'fulfilled_qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseMissing(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $order_item->product_id,
            'type' => 'addon',
            'qty' => 3,
            'fulfilled_qty' => 3,
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'product_id' => $order_item->product_id,
            'type' => 'addon',
            'qty' => 2,
            'fulfilled_qty' => 2,
            'subtotal' => 200
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_addon_order_item_when_product_already_on_order_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'standard',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 2,
            'subtotal' => 2000
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 3,
            'subtotal' => 300
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_standard_order_item_when_not_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 2,
            'subtotal' => 2000
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_updates_existing_standard_order_item_when_product_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'standard',
            'qty' => 3,
            'fulfilled_qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseMissing(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 3,
            'fulfilled_qty' => 3,
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 2,
            'fulfilled_qty' => 2,
            'subtotal' => 200
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_standard_order_item_when_product_already_on_order_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 2,
            'subtotal' => 2000
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 3,
            'subtotal' => 300
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_promo_order_item_when_not_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'promo',
            'qty' => 1,
            'subtotal' => 1000
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_replaces_existing_promo_order_item_when_product_already_on_order(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'promo',
            'qty' => 1,
        ]);

        $this->assertDatabaseMissing(OrderItem::class, [
            'id' => $order_item->id,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_adds_new_promo_order_item_when_product_already_on_order_but_type_doesnt_match(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create();
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'pickup_date' => today()->addDay()]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        $order_item = OrderItem::factory()->create([
            'order_id' => $current_order->id,
            'product_id' => $product->id,
            'type' => 'addon',
            'qty' => 3,
            'unit_price' => 100
        ]);

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'promo',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'promo',
            'qty' => 1,
            'subtotal' => 1000
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'addon',
            'qty' => 3,
            'subtotal' => 300
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_syncs_item_with_subscription_customer_pricing_group(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create();
        $delivery_method = Pickup::factory()->create();

        $subscription = RecurringOrder::factory()->create(['customer_id' => $user->id, 'fulfillment_id' => $delivery_method->id]);
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'customer_id' => $user->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $group_one = ProductPriceGroup::factory()->create(['type' => ProductPriceGroup::PERCENTAGE_INCREASE, 'amount' => 10]);
        $group_two = ProductPriceGroup::factory()->create(['type' => ProductPriceGroup::PERCENTAGE_INCREASE, 'amount' => 20]);

        ProductPrice::factory()->create([
            'group_id' => $group_one->id,
            'product_id' => $product->id,
            'unit_price' => 1100,
            'sale_unit_price' => 1100,
            'unit_of_issue' => 'package'
        ]);

        ProductPrice::factory()->create([
            'group_id' => $group_two->id,
            'product_id' => $product->id,
            'unit_price' => 1200,
            'sale_unit_price' => 1200,
            'unit_of_issue' => 'package'
        ]);

        $delivery_method->pricing_group_id = $group_one->id;
        $delivery_method->save();

        $user->pricing_group_id = $group_two->id;
        $user->save();

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 2,
            'subtotal' => 2400
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_syncs_item_with_subscription_delivery_method_pricing_group(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create();
        $delivery_method = Pickup::factory()->create();

        $subscription = RecurringOrder::factory()->create(['customer_id' => $user->id, 'fulfillment_id' => $delivery_method->id]);
        $current_order = Order::factory()->create(['blueprint_id' => $subscription->id, 'customer_id' => $user->id, 'pickup_date' => today()->addDay()]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        $group_one = ProductPriceGroup::factory()->create(['type' => ProductPriceGroup::PERCENTAGE_INCREASE, 'amount' => 10]);
        $group_two = ProductPriceGroup::factory()->create(['type' => ProductPriceGroup::PERCENTAGE_INCREASE, 'amount' => 20]);

        ProductPrice::factory()->create([
            'group_id' => $group_two->id,
            'product_id' => $product->id,
            'unit_price' => 1200,
            'sale_unit_price' => 1200,
            'unit_of_issue' => 'package'
        ]);

        ProductPrice::factory()->create([
            'group_id' => $group_one->id,
            'product_id' => $product->id,
            'unit_price' => 1100,
            'sale_unit_price' => 1100,
            'unit_of_issue' => 'package'
        ]);

        $delivery_method->pricing_group_id = $group_one->id;
        $delivery_method->save();

        $subscription_item = RecurringOrderItem::factory()->create([
            'order_id' => $subscription->id,
            'product_id' => $product->id,
            'type' => 'recurring',
            'qty' => 2
        ]);

        (new SyncItemToCurrentOrder)->handle($subscription_item);

        $this->assertDatabaseHas(OrderItem::class, [
            'order_id' => $current_order->id,
            'product_id' => $subscription_item->product_id,
            'type' => 'standard',
            'qty' => 2,
            'subtotal' => 2200
        ]);

        Carbon::setTestNow();
    }
}
