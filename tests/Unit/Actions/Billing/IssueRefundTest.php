<?php

namespace Tests\Unit\Actions\Billing;

use App\Actions\Billing\IssueRefund;
use App\Billing\Gateway\Transaction;
use App\Contracts\Billing;
use App\Models\Card;
use App\Models\OrderPayment;
use App\Models\Refund;
use App\Models\User;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class IssueRefundTest extends TenantTestCase
{
    #[Test]
    public function it_can_issue_a_refund_without_an_amount_or_description(): void
    {
        $card = Card::factory()->create();
        $payment = OrderPayment::factory()->create(['amount' => 5000, 'source_id' => $card->source_id]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    null,
                    null,
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });

       $refund = app(IssueRefund::class)->handle($admin, $payment);

        $this->assertDatabaseHas(Refund::class, [
            'id' => $refund->id,
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }

    #[Test]
    public function it_can_issue_a_refund_with_an_amount(): void
    {
        $card = Card::factory()->create();
        $payment = OrderPayment::factory()->create(['amount' => 5000, 'source_id' => $card->source_id]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    1234,
                    null,
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: null
                ));
        });

        $refund = app(IssueRefund::class)->handle($admin, $payment, 1234);

        $this->assertDatabaseHas(Refund::class, [
            'id' => $refund->id,
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => null
        ]);
    }

    #[Test]
    public function it_can_issue_a_refund_with_a_description(): void
    {
        $card = Card::factory()->create();
        $payment = OrderPayment::factory()->create(['amount' => 5000, 'source_id' => $card->source_id]);
        $admin = User::factory()->admin()->create();

        $this->mock(Billing::class, function (MockInterface $mock) use ($payment, $admin) {
            $mock->shouldReceive('refundPayment')
                ->once()
                ->with(
                    \Mockery::on(fn(OrderPayment $arg) => $arg->id === $payment->id),
                    null,
                    'test',
                )->andReturn(new Transaction(
                    type: Transaction::TYPE_REFUND,
                    id: 'some_id',
                    amount: 1234,
                    success: true,
                    source_id: $payment->source_id,
                    payment_type: $payment->payment_type,
                    description: 'test'
                ));
        });

        $refund = app(IssueRefund::class)->handle($admin, $payment, null, 'test');

        $this->assertDatabaseHas(Refund::class, [
            'id' => $refund->id,
            'payment_id' => $payment->id,
            'admin_id' => $admin->id,
            'refund_id' => 'some_id',
            'amount' => 1234,
            'status' => 'succeeded',
            'reason' => 'test'
        ]);
    }
}
