<?php

namespace Tests\Unit\Actions\Billing;

use App\Actions\Billing\AddPaymentMethodToCustomer;
use App\Billing\Gateway\PaymentMethod;
use App\Contracts\Billing;
use App\Models\Card;
use App\Models\User;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddPaymentMethodToCustomerTest extends TenantTestCase
{
    #[Test]
    public function it_can_add_a_payment_method_as_default_to_a_new_customer(): void
    {
        $user = User::factory()->create(['customer_id' => '']);

        $this->mock(Billing::class, function (MockInterface $mock) use ($user) {
            $mock->shouldReceive('setDefaultCard')->once();
        });

        $payment_method = new PaymentMethod(
            id: 'src_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'Test Tester',
            exp_month: '05',
            exp_year: '2028',
            brand: 'some brand',
            last_four: '0022',
        );

        app(AddPaymentMethodToCustomer::class)->handle($user, $payment_method, true);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'customer_id' => 'cus_1234'
        ]);

        $this->assertDatabaseHas(Card::class, [
            'user_id' => $user->id,
            'source_id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'default' => true,
        ]);
    }

    #[Test]
    public function it_can_add_a_payment_method_without_default_to_a_new_customer(): void
    {
        $user = User::factory()->create(['customer_id' => '']);

        $this->mock(Billing::class, function (MockInterface $mock) use ($user) {
            $mock->shouldNotReceive('setDefaultCard');
        });

        $payment_method = new PaymentMethod(
            id: 'src_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'Test Tester',
            exp_month: '05',
            exp_year: '2028',
            brand: 'some brand',
            last_four: '0022',
        );

        app(AddPaymentMethodToCustomer::class)->handle($user, $payment_method);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'customer_id' => 'cus_1234'
        ]);

        $this->assertDatabaseHas(Card::class, [
            'user_id' => $user->id,
            'source_id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'default' => false,
        ]);
    }

    #[Test]
    public function it_can_add_a_payment_method_as_default_to_an_existing_customer(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_4321']);

        $this->mock(Billing::class, function (MockInterface $mock) use ($user) {
            $mock->shouldReceive('setDefaultCard')->once();
        });

        $payment_method = new PaymentMethod(
            id: 'src_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'Test Tester',
            exp_month: '05',
            exp_year: '2028',
            brand: 'some brand',
            last_four: '0022',
        );

        app(AddPaymentMethodToCustomer::class)->handle($user, $payment_method, true);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'customer_id' => 'cus_4321'
        ]);

        $this->assertDatabaseHas(Card::class, [
            'user_id' => $user->id,
            'source_id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'default' => true,
        ]);
    }
}
