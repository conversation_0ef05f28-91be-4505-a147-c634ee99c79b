<?php

namespace Tests\Unit\Actions;

use App\Actions\CreateRecurringOrderBlueprint;
use App\Events\Subscription\CustomerSubscribed;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\User;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CreateRecurringOrderBlueprintTest extends TenantTestCase
{
    #[Test]
    public function it_can_create_a_blueprint_for_a_customer_without_a_blueprint(): void
    {
        Event::fake([CustomerSubscribed::class]);

        $schedule = Schedule::factory()->create();
        $date_one = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_end_date' => today()->addDays(7),
            'pickup_date' => today()->addDays(9),
        ]);
        $expected_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_end_date' => today()->addDays(16),
            'pickup_date' => today()->addDays(18),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create();

        /** @var Order $order */
        $order = Order::factory()->create([
            'is_recurring' => null,
            'blueprint_id' => null,
            'customer_id' => $customer->id,
            'schedule_id' => $schedule->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(2)
        ]);

        $item_one = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2]);
        $item_two = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1]);

        $promo_item = Product::factory()->create();

        $this->partialMock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(3);
        });

        $this->partialMock(SettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('deadlineHour')->andReturn(8);
        });

        $blueprint = (new CreateRecurringOrderBlueprint)->execute($order, 14, $promo_item->id);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 14,
            'ready_at' => $expected_date->pickup_date->format('Y-m-d \0\0\:\0\0\:\0\0'),
            'generate_at' => $expected_date->order_end_date->copy()->subDays(3)->setTime(8,0,0)->format('Y-m-d H:i:s'),
            'updated_by' => null
        ]);

        $expected_blueprint = RecurringOrder::firstWhere([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 14,
            'ready_at' => $expected_date->pickup_date->format('Y-m-d \0\0\:\0\0\:\0\0'),
            'generate_at' => $expected_date->order_end_date->copy()->subDays(3)->setTime(8,0,0)->format('Y-m-d H:i:s'),
            'updated_by' => null,
        ]);

        $this->assertEquals($expected_blueprint->id, $blueprint->id);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $expected_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $item_one->product_id,
            'qty' => 2,
            'type' => 'recurring',
            'unit_price_override' => null
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $expected_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $item_two->product_id,
            'qty' => 1,
            'type' => 'recurring',
            'unit_price_override' => null
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $expected_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $promo_item->id,
            'qty' => 1,
            'type' => 'promo',
            'unit_price_override' => null
        ]);

        Event::assertDispatched(
            CustomerSubscribed::class,
            fn(CustomerSubscribed $event) => $event->subscription->customer_id === $customer->id
        );
    }

    #[Test]
    public function it_can_create_a_blueprint_for_a_customer_with_an_existing_blueprint(): void
    {
        Event::fake([CustomerSubscribed::class]);

        $schedule = Schedule::factory()->create();
        $date_one = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_end_date' => today()->addDays(7),
            'pickup_date' => today()->addDays(9),
        ]);
        $expected_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_end_date' => today()->addDays(16),
            'pickup_date' => today()->addDays(18),
        ]);

        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create();

        $existing_blueprint = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => Pickup::factory(),
            'schedule_id' => Schedule::factory(),
            'reorder_frequency' => 7,
            'updated_by' => $customer->id
        ]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'is_recurring' => null,
            'blueprint_id' => null,
            'customer_id' => $customer->id,
            'schedule_id' => $schedule->id,
            'pickup_id' => $pickup->id,
            'pickup_date' => today()->addDays(2)
        ]);

        $item_one = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 2]);
        $item_two = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1]);

        $promo_item = Product::factory()->create();

        $this->partialMock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('inventoryManagementDayCount')->andReturn(3);
        });

        $this->partialMock(SettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('deadlineHour')->andReturn(8);
        });

        $blueprint = (new CreateRecurringOrderBlueprint)->execute($order, 14, $promo_item->id);

        $this->assertDatabaseHas(RecurringOrder::class, [
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 14,
            'ready_at' => $expected_date->pickup_date->format('Y-m-d \0\0\:\0\0\:\0\0'),
            'generate_at' => $expected_date->order_end_date->copy()->subDays(3)->setTime(8,0,0)->format('Y-m-d H:i:s'),
            'updated_by' => null
        ]);

        $this->assertEquals($existing_blueprint->id, $blueprint->id);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $existing_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $item_one->product_id,
            'qty' => 2,
            'type' => 'recurring',
            'unit_price_override' => null
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $existing_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $item_two->product_id,
            'qty' => 1,
            'type' => 'recurring',
            'unit_price_override' => null
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $existing_blueprint->id,
            'customer_id' => $customer->id,
            'product_id' => $promo_item->id,
            'qty' => 1,
            'type' => 'promo',
            'unit_price_override' => null
        ]);

        Event::assertNotDispatched(CustomerSubscribed::class);
    }
}