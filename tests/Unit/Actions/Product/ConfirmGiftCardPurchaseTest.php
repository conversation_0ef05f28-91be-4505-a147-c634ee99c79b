<?php

namespace Tests\Unit\Actions\Product;

use App\Actions\Product\ConfirmGiftCardPurchase;
use App\Billing\Gateway\Transaction;
use App\Contracts\Billing;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Mail\GiftCardIssued;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\ProductType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmGiftCardPurchaseTest extends TenantTestCase
{
    #[Test]
    public function it_handles_creating_order_from_attributes(): void
    {
        Mail::fake();
        
        Carbon::setTestNow(now());

        $customer = User::factory()->create();
        $attributes = $this->cartAttributes($customer);

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $expected_order_attributes = [
            'customer_id' => $customer->id,
            'pickup_id' => Pickup::forGiftCards()->id,
            'status_id' => OrderStatus::completed(),
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'paid' => true,
            'payment_date' => now()->format('Y-m-d H:i:s'),
            'pickup_date' => today(),
            'original_pickup_date' => today(),
            'deadline_date' => today(),
            'schedule_id' => null,
            'delivery_rate' => 0,
            'delivery_fee_type' => 0,
            'accounting_id' => $customer->accounting_id,
            'confirmed' => true,
            'confirmed_date' => today(),
            'created_year' => today()->year,
            'created_month' => today()->month,
            'created_day' => today()->day,
        ];

        $this->assertDatabaseHas(Order::class, $expected_order_attributes);

        $order = Order::where($expected_order_attributes)->first();

        foreach ($attributes['items'] as $item) {
            $product = Product::find($item['product_id']);

            $this->assertDatabaseHas(OrderItem::class, [
                'order_id' => $order->id,
                'product_id' => $product->id,
                'qty' => 1,
                'weight' =>  $product->weight
            ]);

            $this->assertEquals(2, OrderItem::where([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'qty' => 1,
                'weight' =>  $product->weight
            ])->count());
        }

        $this->assertDatabaseHas(OrderPayment::class, [
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' => 'brand_123',
            'customer_id' => $customer->id,
            'admin_id' => 0,
            'amount' => 10000,
            'payment_id' => 'chg_123',
            'source_id' => 'src_123',
            'description' => 'Gift card purchase: ' . $customer->email
        ]);

        Mail::assertQueued(GiftCardIssued::class);

        Carbon::setTestNow();
    }

    private function cartAttributes(User $user): array
    {
        $payment = Payment::where(['key' => 'card'])->firstOrNew();
        $payment->fillable(['key']);
        $payment->fill(Payment::factory()->make(['key' => 'card'])->toArray());
        $payment->save();

        return [
            'location_id' => null,
            'date_id' => null,
            'notes' => 'some new notes',
            'customer' => [
                'first_name' => 'new first',
                'last_name' => 'new last',
                'phone' => '************',
                'email' => '<EMAIL>',
                'save_for_later' => false,
                'opt_in_to_sms' => true
            ],
            'shipping' => [
                'street' => '123 new st',
                'street_2' => 'apt new',
                'city' => 'new',
                'state' => 'NE',
                'zip' => '45678',
                'country' => 'US',
                'save_for_later' => false
            ],
            'billing' => [
                'method' => 'card',
                'source_id' => Card::factory()->create(['user_id' => $user->id])->source_id,
                'save_for_later' => false
            ],
            'items' => [
                [
                    'product_id' => Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value, 'unit_price' => '50.00'])->id,
                    'quantity' => 2
                ]
            ]
        ];

        Mail::assertQueued(GiftCardIssued::class);

    }

    #[Test]
    public function it_handles_customer_attributes_from_attributes(): void
    {
        Mail::fake();

        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $attributes = $this->cartAttributes($customer);

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'customer_first_name' => 'new first',
            'customer_last_name' => 'new last',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Mail::assertQueued(GiftCardIssued::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_handles_saving_customer_attributes_for_later(): void
    {
        Mail::fake();

        Carbon::setTestNow(now());

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
        ]);

        $attributes = $this->cartAttributes($customer);
        $attributes['customer']['save_for_later'] = true;


        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'first_name' => 'new first',
            'last_name' => 'new last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'subscribed_to_sms_marketing_at' => now()
        ]);

        Event::assertDispatched(
            UserSubscribedToSmsMarketing::class,
            function (UserSubscribedToSmsMarketing $event) use ($customer) {
                return $event->user->id === $customer->id
                    && $event->opt_in_location = 'during_checkout';
            }
        );

        Mail::assertQueued(GiftCardIssued::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_not_subscribing(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => null]);

        $attributes = $this->cartAttributes($customer);
        $attributes['customer']['opt_in_to_sms'] = false;

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);

        Mail::assertQueued(GiftCardIssued::class);

    }

    #[Test]
    public function it_doesnt_fire_user_subscribed_event_when_already_subscribed(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['subscribed_to_sms_marketing_at' => now()]);

        $attributes = $this->cartAttributes($customer);
        $attributes['customer']['opt_in_to_sms'] = true;

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
    }

    #[Test]
    public function it_handles_shipping_information_from_the_cart(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);

        $attributes = $this->cartAttributes($customer);

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);

        Mail::assertQueued(GiftCardIssued::class);

    }

    #[Test]
    public function it_handles_saving_shipping_information_for_later(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create([
            'first_name' => 'first',
            'last_name' => 'last',
            'phone' => '************',
            'email' => '<EMAIL>',
            'street' => '123 old st',
            'street_2' => 'old apt',
            'city' => 'old',
            'state' => 'OL',
            'zip' => '12345',
            'country' => 'CA',
        ]);

        $attributes = $this->cartAttributes($customer);
        $attributes['shipping']['save_for_later'] = true;

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'shipping_street' => '123 new st',
            'shipping_street_2' => 'apt new',
            'shipping_city' => 'new',
            'shipping_state' => 'NE',
            'shipping_zip' => '45678',
            'shipping_country' => 'US',
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'street' => '123 new st',
            'street_2' => 'apt new',
            'city' => 'new',
            'state' => 'NE',
            'zip' => '45678',
            'country' => 'US',
        ]);

        Mail::assertQueued(GiftCardIssued::class);

    }

    #[Test]
    public function it_handles_billing_information_from_the_cart(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['checkout_card_id' => 0]);

        $attributes = $this->cartAttributes($customer);

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $expected_payment = Payment::where('key', $attributes['billing']['method'])->first();

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => $card->id,
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'checkout_card_id' => 0,
        ]);

        Mail::assertQueued(GiftCardIssued::class);

    }

    #[Test]
    public function it_handles_saving_billing_information_for_later(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create(['checkout_card_id' => 0]);

        $attributes = $this->cartAttributes($customer);

        $attributes['billing']['save_for_later'] = true;
        $attributes['billing']['method'] = 'card';

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $expected_payment = Payment::where(['key' => 'card'])->first();

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'payment_id' => $expected_payment->id,
            'payment_source_id' => $card->id,
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $customer->id,
            'checkout_card_id' => $card->id,
        ]);

        Mail::assertQueued(GiftCardIssued::class);
    }

    #[Test]
    public function it_handles_saving_customer_notes(): void
    {
        Mail::fake();

        Event::fake([UserSubscribedToSmsMarketing::class]);

        $customer = User::factory()->create();

        $attributes = $this->cartAttributes($customer);

        $card = Card::where(['source_id' => $attributes['billing']['source_id']])->first();

        $this->mock(Billing::class, function (MockInterface $mock) use ($card, $customer) {
            $mock->shouldReceive('chargeAmountWithCard')->once()
                ->with(
                    \Mockery::on(fn(Card $arg) => $arg->id === $card->id),
                    10000,
                    'Gift card purchase: ' . $customer->email
                )
                ->andReturn(new Transaction(
                    type: Transaction::TYPE_CHARGE,
                    id: 'chg_123',
                    amount: 10000,
                    success: true,
                    source_id: 'src_123',
                    payment_type: 'brand_123',
                    description: 'Gift card purchase: ' . $customer->email
                ));
        });

        (new ConfirmGiftCardPurchase)->handle($customer, $attributes);

        $this->assertDatabaseHas(Order::class, [
            'customer_id' => $customer->id,
            'customer_notes' => 'some new notes',
        ]);

        Mail::assertQueued(GiftCardIssued::class);
    }
}
