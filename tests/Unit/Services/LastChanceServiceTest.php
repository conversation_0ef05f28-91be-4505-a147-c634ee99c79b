<?php

namespace Tests\Unit\Services;

use App\Mailers\MailgunMailer;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Models\Template;
use App\Models\User;
use App\Services\LastChanceService;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class LastChanceServiceTest extends TenantTestCase
{
    #[Test]
    public function it_can_send_an_email_to_associated_pickups_and_their_customers(): void
    {
        $schedule = Schedule::factory()->hasDates()->create();
        $template = Template::factory()->create();

        $pickup_one = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        User::factory()->create(['pickup_point' => $pickup_one->id]);
        $pickup_two = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        User::factory()->create(['pickup_point' => $pickup_two->id]);

        $this->partialMock(MailgunMailer::class, function (MockInterface $mock) use ($template) {
            $mock->shouldReceive('send')->twice()->andReturnUndefined();
        });

        (new LastChanceService)->sendEmail($schedule, $template->id);
    }

    #[Test]
    public function it_does_not_send_email_to_associated_pickups_without_any_customers(): void
    {
        $schedule = Schedule::factory()->hasDates()->create();
        $template = Template::factory()->create();

        $pickup_one = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        User::factory()->create(['pickup_point' => $pickup_one->id]);
        $pickup_two = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $this->partialMock(MailgunMailer::class, function (MockInterface $mock) use ($template) {
            $mock->shouldReceive('send')->once()->andReturnUndefined();
        });

        (new LastChanceService)->sendEmail($schedule, $template->id);
    }
}