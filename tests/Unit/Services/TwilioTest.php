<?php

namespace Tests\Unit\Services;

use App\Services\Twilio;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TwilioTest extends TenantTestCase
{
    #[Test]
    public function it_can_lookup_a_phone_number(): void
    {
        config([
            'services.twilio.connections.twilio.sid' => 'foo',
            'services.twilio.connections.twilio.token' => 'bar',
        ]);

        $expected_response = [
            'valid' => true,
            'line_type_intelligence' => [
                'type' => 'mobile',
            ],
        ];

        Http::fake([
            'https://lookups.twilio.com/v2/PhoneNumbers/+12025550147?Fields=line_type_intelligence' => Http::response($expected_response),
        ]);

        $twilio = new Twilio;

        $this->assertEquals($expected_response, $twilio->lookup('+12025550147'));

        Http::assertSent(function (\Illuminate\Http\Client\Request $request) {
            return $request->hasHeader('Authorization', 'Basic ' . base64_encode('foo:bar'));
        });
    }
}
