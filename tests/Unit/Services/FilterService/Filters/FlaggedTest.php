<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\Flagged;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class FlaggedTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_flagged(): void
    {
        $collection = (new Flagged)->handle([$this->createRequest(['flagged' => 'true']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('flagged'));

        /** @var Flagged $filter */
        $filter = $collection->get('flagged');

        $this->assertEquals('Flagged', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}