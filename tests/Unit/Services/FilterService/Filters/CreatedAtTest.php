<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\CreatedAt;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CreatedAtTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_one_date(): void
    {
        $collection = (new CreatedAt)->handle([$this->createRequest(['created_at' => '2020-12-20']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('created_at'));

        /** @var CreatedAt $filter */
        $filter = $collection->get('created_at');

        $this->assertEquals('Created:', $filter->label());
        $this->assertEquals('2020-12-20', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_an_array_of_dates(): void
    {
        $collection = (new CreatedAt)->handle([$this->createRequest(['created_at' => ['2020-12-20', '2020-12-25']]), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('created_at'));

        /** @var CreatedAt $filter */
        $filter = $collection->get('created_at');

        $this->assertEquals('Created:', $filter->label());
        $this->assertEquals('2020-12-20 - 2020-12-25', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}
