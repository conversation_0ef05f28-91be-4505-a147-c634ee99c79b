<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\WasReferred;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class WasReferredTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_referral_user_id(): void
    {
        $collection = (new WasReferred)->handle([$this->createRequest(['referral_user_id' => 'true']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('referral_user_id'));

        /** @var WasReferred $filter */
        $filter = $collection->get('referral_user_id');

        $this->assertEquals('Was Referred', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}