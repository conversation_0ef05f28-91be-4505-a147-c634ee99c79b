<?php

namespace Tests\Unit\Integrations\Drip;

use App\Events\Cart\CartCreated;
use App\Events\Checkout\CheckoutInitiated;
use App\Events\Order\OrderStatusWasUpdated;
use App\Events\Order\OrderWasCanceled;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\RecurringOrderCanceled;
use App\Events\Subscription\RecurringOrderCreated;
use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Events\Subscription\SubscriptionFrequencyWasUpdated;
use App\Events\Subscription\SubscriptionWasSkipped;
use App\Events\User\CreditIssued;
use App\Events\User\LeadCreated;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserUnsubscribedFromNewsletter;
use App\Events\User\UserUpdated;
use App\Events\User\UserWasDeleted;
use App\Events\User\UserWasRegistered;
use App\Integrations\Drip\DripIntegration;
use App\Integrations\Drip\Listeners\CreateUser;
use App\Integrations\Drip\Listeners\CreditIssuedListener;
use App\Integrations\Drip\Listeners\ListenForRecurringOrderCanceled;
use App\Integrations\Drip\Listeners\ListenForRecurringOrderCreated;
use App\Integrations\Drip\Listeners\ListenForSubscriptionFrequencyWasUpdated;
use App\Integrations\Drip\Listeners\RecordCanceledOrder;
use App\Integrations\Drip\Listeners\RecordCartCreated;
use App\Integrations\Drip\Listeners\RecordFirstOrderReceived;
use App\Integrations\Drip\Listeners\RecordInitiatedCheckout;
use App\Integrations\Drip\Listeners\RecordPurchase;
use App\Integrations\Drip\Listeners\RecordSubscriptionWasSkipped;
use App\Integrations\Drip\Listeners\SubscribeLead;
use App\Integrations\Drip\Listeners\SubscribeUser;
use App\Integrations\Drip\Listeners\UnsubscribeUser;
use App\Integrations\Drip\Listeners\UpdateSubscriber;
use App\Integrations\Drip\Listeners\UserDeleted;
use App\Models\Integration;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DripIntegrationTest extends TenantTestCase
{
    #[Test]
    public function it_can_determine_if_an_api_key_is_valid(): void
    {
        $i = Integration::factory()->make();
        $this->assertFalse((new DripIntegration($i))->apiKeyIsValid());

        $i = Integration::factory()->make(['settings' => []]);
        $this->assertFalse((new DripIntegration($i))->apiKeyIsValid());

        $i = Integration::factory()->make(['settings' => ['api_key' => '', 'account_id' => 'abc123']]);
        $this->assertFalse((new DripIntegration($i))->apiKeyIsValid());

        $i = Integration::factory()->make(['settings' => ['api_key' => 'abc123', 'account_id' => '']]);
        $this->assertFalse((new DripIntegration($i))->apiKeyIsValid());

        $i = Integration::factory()->make(['settings' => ['api_key' => 'abc123', 'account_id' => 'abc123']]);
        $this->assertTrue((new DripIntegration($i))->apiKeyIsValid());
    }

    #[Test]
    public function it_registers_event_listeners_on_boot_when_api_key_is_valid(): void
    {
        Event::fake([
            LeadCreated::class,
            UserUpdated::class,
            UserWasRegistered::class,
            UserWasDeleted::class,
            CreditIssued::class,
            OrderWasConfirmed::class,
            OrderWasCanceled::class,
            CheckoutInitiated::class,
            UserSubscribedToNewsletter::class,
            UserUnsubscribedFromNewsletter::class,
            OrderStatusWasUpdated::class,
            CartCreated::class,
            RecurringOrderCreated::class,
            SubscriptionWasSkipped::class,
            RecurringOrderCanceled::class,
            RecurringOrderWasConfirmed::class,
            SubscriptionFrequencyWasUpdated::class
        ]);

        $i = Integration::factory()->make(['settings' => ['api_key' => 'abc123', 'account_id' => 'abc123']]);

        (new DripIntegration($i))->boot();

        Event::assertListening(LeadCreated::class, SubscribeLead::class);
        Event::assertListening(UserUpdated::class, UpdateSubscriber::class);
        Event::assertListening(UserWasRegistered::class, CreateUser::class);
        Event::assertListening(UserWasDeleted::class, UserDeleted::class);
        Event::assertListening(CreditIssued::class, CreditIssuedListener::class);
        Event::assertListening(OrderWasConfirmed::class, RecordPurchase::class);
        Event::assertListening(OrderWasCanceled::class, RecordCanceledOrder::class);
        Event::assertListening(CheckoutInitiated::class, RecordInitiatedCheckout::class);
        Event::assertListening(UserSubscribedToNewsletter::class, SubscribeUser::class);
        Event::assertListening(UserUnsubscribedFromNewsletter::class, UnsubscribeUser::class);
        Event::assertListening(OrderStatusWasUpdated::class, RecordFirstOrderReceived::class);
        Event::assertListening(CartCreated::class, RecordCartCreated::class);

        Event::assertListening(RecurringOrderCreated::class, ListenForRecurringOrderCreated::class);
        Event::assertListening(SubscriptionWasSkipped::class, RecordSubscriptionWasSkipped::class);
        Event::assertListening(RecurringOrderCanceled::class, ListenForRecurringOrderCanceled::class);
        Event::assertListening(SubscriptionFrequencyWasUpdated::class, ListenForSubscriptionFrequencyWasUpdated::class);
    }
}
