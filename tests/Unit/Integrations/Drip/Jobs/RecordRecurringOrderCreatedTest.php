<?php

namespace Tests\Unit\Integrations\Drip\Jobs;

use App\Integrations\Drip\Drip;
use App\Integrations\Drip\Jobs\RecordRecurringOrderCreated;
use App\Models\Integration;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Support\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordRecurringOrderCreatedTest extends TenantTestCase
{
    #[Test]
    public function it_records_expected_event_in_drip(): void
    {
        Carbon::setTestNow(now());

        Integration::factory()->create([
            'name' => 'drip',
            'settings' => [
                'api_key' => 'abc123',
                'account_id' => 'def456'
            ]
        ]);

        $customer = User::factory()->create(['recurring_order_count' => 5]);

        /** @var RecurringOrder $recurring_order */
        $recurring_order = RecurringOrder::factory()->create(['customer_id' => $customer->id]);

        $this->mock(Drip::class, function (MockInterface $mock) use ($customer, $recurring_order) {
            return $mock->shouldReceive('configure')->once()->with('abc123', 'def456')->andReturnSelf()
                ->shouldReceive('recordEvent')->once()->with(
                    \Mockery::on(fn ($arg) => $arg === $customer->email),
                    'New Subscription',
                    [
                        'reorder_frequency' => $recurring_order->reorder_frequency,
                        'next_deadline' => $recurring_order->next_deadline,
                        'next_delivery' => $recurring_order->next_delivery,
                        'schedule_id' => $recurring_order->schedule_id,
                        'fulfillment_id' => $recurring_order->fulfillment_id,
                    ]
                )->andReturn(true)
                ->shouldReceive('patchSubscriber')->once()->with(
                    \Mockery::on(fn (User $arg) => $arg->id === $customer->id),
                    [
                        'subscription_status' => 'Active',
                        'subscription_order_count' => 5
                    ]
                )->andReturn([]);
        });

        (new RecordRecurringOrderCreated($recurring_order->id))->handle();

        Carbon::setTestNow();
    }
}