<?php

namespace Tests\Unit\Billing\Stripe;

use App\Billing\Gateway\Transaction;
use App\Billing\Stripe\Stripe;
use App\Billing\Stripe\StripeStandardAccount;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Stripe\Charge;
use Stripe\Collection;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Refund;
use Stripe\SetupIntent;
use Tests\TenantTestCase;

class StripeStandardAccountTest extends TenantTestCase
{
    #[Test]
    public function it_returns_true_when_determining_if_connected(): void
    {
        $this->assertTrue((new StripeStandardAccount)->isConnected());
    }

    #[Test]
    public function it_can_charge_an_order_with_a_payment_source(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'some customer id']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'card_123']);
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $source = \Stripe\Card::constructFrom(['id' => 'card_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($order, $card, $source) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerSource')->once()
                ->with('some customer id', 'card_123')->andReturn($source);

            $mock->shouldReceive('createCharge')
                ->once()
                ->with([
                    'customer' => 'some customer id',
                    'source' => $source,
                    'amount' => 500,
                    'currency' =>  'USD',
                    'application_fee' => null,
                    'description' => 'some desc',
                    'receipt_email' => $order->customer_email,
                    'metadata' => [
                        'order #' => $order->id,
                        'customer' => $order->customer_full_name,
                        'email' => $order->customer_email,
                        'phone' => $order->customer_phone,
                    ],
                ], [
                    'idempotency_key' => $order->idempotencyKey(500)
                ])
                ->andReturn(Charge::constructFrom([
                    'id' => 'charge_id',
                    'amount' => 500,
                    'status' => 'succeeded',
                    'source' => [
                        'id' => 'some_source_id',
                        'brand' => 'some_source_brand'
                    ],
                ]));
        });

        $charge_result = (new StripeStandardAccount)->chargeOrderWithCard($order, $card, 500, 'some desc');

        $this->assertEquals(Transaction::TYPE_CHARGE, $charge_result->type);
        $this->assertEquals('charge_id', $charge_result->id);
        $this->assertEquals(500, $charge_result->amount);
        $this->assertTrue($charge_result->success);
        $this->assertEquals('some_source_brand', $charge_result->payment_type);
        $this->assertEquals( 'some desc', $charge_result->description);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_order_with_a_payment_method(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'some customer id']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'pm_123']);
        $order = Order::factory()->create(['customer_id' => $user->id]);
        $payment_method = PaymentMethod::constructFrom(['id' => 'pm_1234', 'customer' => 'cus_1234']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($order, $card, $payment_method) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrievePaymentMethod')->once()->with('pm_123')->andReturn($payment_method);

            $mock->shouldReceive('createPaymentIntent')
                ->once()
                ->with([
                    'amount' => 500,
                    'application_fee_amount' => null,
                    'currency' =>  'USD',
                    'confirm' => true,
                    'description' => 'some desc',
                    'off_session' => true,
                    'customer' => 'cus_1234',
                    'payment_method' => $payment_method,
                    'receipt_email' => $order->customer_email,
                    'metadata' => [
                        'order #' => $order->id,
                        'customer' => $order->customer_full_name,
                        'email' => $order->customer_email,
                        'phone' => $order->customer_phone,
                    ],
                ])
                ->andReturn(PaymentIntent::constructFrom([
                    'id' => 'pi_123',
                    'charges' => Collection::constructFrom(['data' => [
                        Charge::constructFrom([
                            'id' => 'charge_id',
                            'amount' => 500,
                            'status' => 'succeeded',
                            'payment_method' => 'some_other_source_id',
                            'payment_method_details' => ['card' => ['brand' => 'some_source_brand']],
                        ])
                    ]])
                ]));
        });

        $charge_result = (new StripeStandardAccount)->chargeOrderWithCard($order, $card, 500, 'some desc');

        $this->assertEquals(Transaction::TYPE_CHARGE, $charge_result->type);
        $this->assertEquals('charge_id', $charge_result->id);
        $this->assertEquals(500, $charge_result->amount);
        $this->assertTrue($charge_result->success);
        $this->assertEquals('some_source_brand', $charge_result->payment_type);
        $this->assertEquals( 'some desc', $charge_result->description);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_order_with_shipping_information(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'some customer id']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'card_123']);
        $order = Order::factory()->create([
            'customer_id' => $user->id,
            'total' => 500,
            'customer_first_name' => 'some first name',
            'customer_last_name' => 'some last name',
            'customer_phone' => '************',
            'tracking_id' => 'some tracking number',
            'shipping_street' => 'some street 1',
            'shipping_street_2' => 'some street 2',
            'shipping_city' => 'some city',
            'shipping_state' => 'some state',
            'shipping_country' => 'some country',
            'shipping_zip' => 'some zip'
        ]);

        $source = \Stripe\Card::constructFrom(['id' => 'card_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($order, $card, $source) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerSource')->once()
                ->with('some customer id', 'card_123')->andReturn($source);

            $mock->shouldReceive('createCharge')
                ->once()
                ->with([
                    'customer' => 'some customer id',
                    'source' => $source,
                    'amount' => 500,
                    'currency' =>  'USD',
                    'application_fee' => null,
                    'description' => 'some desc',
                    'receipt_email' => $order->customer_email,
                    'metadata' => [
                        'order #' => $order->id,
                        'customer' => $order->customer_full_name,
                        'email' => $order->customer_email,
                        'phone' => $order->customer_phone,
                    ],
                    'shipping' => [
                        'name' => 'some first name some last name',
                        'phone' => '************',
                        'tracking_number' => 'some tracking number',
                        'address' => [
                            'line1' => 'some street 1',
                            'line2' => 'some street 2',
                            'city' => 'some city',
                            'state' => 'some state',
                            'country' => 'some country',
                            'postal_code' => 'some zip',
                        ],
                    ]
                ], [
                    'idempotency_key' => $order->idempotencyKey(500),
                ])
                ->andReturn(Charge::constructFrom([
                    'id' => 'charge_id',
                    'amount' => 500,
                    'status' => 'succeeded',
                    'source' => [
                        'id' => 'some_source_id',
                        'brand' => 'some_source_brand'
                    ],
                ]));
        });

        $charge_result = (new StripeStandardAccount)->chargeOrderWithCard($order, $card, 500, 'some desc');

        $this->assertEquals(Transaction::TYPE_CHARGE, $charge_result->type);
        $this->assertEquals('charge_id', $charge_result->id);
        $this->assertEquals(500, $charge_result->amount);
        $this->assertTrue($charge_result->success);
        $this->assertEquals('some_source_brand', $charge_result->payment_type);
        $this->assertEquals( 'some desc', $charge_result->description);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_amount_with_a_payment_source(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'some customer id']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'card_123']);
        $source = \Stripe\Card::constructFrom(['id' => 'card_123', 'customer' => 'cus_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $source) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerSource')->once()
                ->with('some customer id', 'card_123')->andReturn($source);

            $mock->shouldReceive('createCharge')
                ->once()
                ->with([
                    'customer' => 'cus_123',
                    'source' => $source,
                    'amount' => 500,
                    'currency' =>  'USD',
                    'application_fee' => null,
                    'description' => 'some desc',
                ], [
                    'idempotency_key' => $card->idempotencyKey(500)
                ])
                ->andReturn(Charge::constructFrom([
                    'id' => 'charge_id',
                    'amount' => 500,
                    'status' => 'succeeded',
                    'source' => [
                        'id' => 'some_source_id',
                        'brand' => 'some_source_brand'
                    ],
                ]));
        });

        $charge_result = (new StripeStandardAccount)->chargeAmountWithCard($card, 500, 'some desc');

        $this->assertEquals(Transaction::TYPE_CHARGE, $charge_result->type);
        $this->assertEquals('charge_id', $charge_result->id);
        $this->assertEquals(500, $charge_result->amount);
        $this->assertTrue($charge_result->success);
        $this->assertEquals('some_source_brand', $charge_result->payment_type);
        $this->assertEquals( 'some desc', $charge_result->description);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_charge_an_amount_with_a_payment_method(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create(['customer_id' => 'some customer id']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'pm_123']);
        $payment_method = PaymentMethod::constructFrom(['id' => 'pm_1234', 'customer' => 'cus_1234']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $payment_method) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrievePaymentMethod')->once()->with('pm_123')->andReturn($payment_method);

            $mock->shouldReceive('createPaymentIntent')
                ->once()
                ->with([
                    'amount' => 500,
                    'application_fee_amount' => null,
                    'currency' =>  'USD',
                    'confirm' => true,
                    'description' => 'some desc',
                    'off_session' => true,
                    'customer' => 'cus_1234',
                    'payment_method' => $payment_method,
                ])
                ->andReturn(PaymentIntent::constructFrom([
                    'id' => 'pi_123',
                    'charges' => Collection::constructFrom(['data' => [
                        Charge::constructFrom([
                            'id' => 'charge_id',
                            'amount' => 500,
                            'status' => 'succeeded',
                            'payment_method' => 'some_other_source_id',
                            'payment_method_details' => ['card' => ['brand' => 'some_source_brand']],
                        ])
                    ]])
                ]));
        });

        $charge_result = (new StripeStandardAccount)->chargeAmountWithCard($card, 500, 'some desc');

        $this->assertEquals(Transaction::TYPE_CHARGE, $charge_result->type);
        $this->assertEquals('charge_id', $charge_result->id);
        $this->assertEquals(500, $charge_result->amount);
        $this->assertTrue($charge_result->success);
        $this->assertEquals('some_source_brand', $charge_result->payment_type);
        $this->assertEquals( 'some desc', $charge_result->description);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_refund_a_order_payment_without_an_amount(): void
    {
        $payment = OrderPayment::factory()->create(['payment_id' => 'some_payment_id']);

        $expected_stripe_refund = Refund::constructFrom([
            'id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'amount' => 500,
            'status' => 'succeeded'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_stripe_refund) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createRefund')
                ->once()
                ->with(
                    [
                        'charge' => 'some_payment_id',
                        'amount' => null,
                        'metadata' => ['description' => null]
                    ]
                )
                ->andReturn($expected_stripe_refund);
        });

        $result = (new StripeStandardAccount)->refundPayment($payment);

        $this->assertEquals(Transaction::TYPE_REFUND, $result->type);
        $this->assertEquals('src_1JroTYC1Poh057RbV7LbT4iS', $result->id);
        $this->assertEquals(500, $result->amount);
        $this->assertTrue($result->success);
        $this->assertEquals($payment->payment_type, $result->payment_type);
        $this->assertEquals( null, $result->description);
    }

    #[Test]
    public function it_can_refund_a_order_payment_with_an_amount(): void
    {
        $payment = OrderPayment::factory()->create(['payment_id' => 'some_payment_id']);

        $expected_stripe_refund = Refund::constructFrom([
            'id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'amount' => 500,
            'status' => 'succeeded'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_stripe_refund) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createRefund')
                ->once()
                ->with(
                    [
                        'charge' => 'some_payment_id',
                        'amount' => 500,
                        'metadata' => ['description' => null]
                    ]
                )
                ->andReturn($expected_stripe_refund);
        });

        $result = (new StripeStandardAccount)->refundPayment($payment, 500);

        $this->assertEquals(Transaction::TYPE_REFUND, $result->type);
        $this->assertEquals('src_1JroTYC1Poh057RbV7LbT4iS', $result->id);
        $this->assertEquals(500, $result->amount);
        $this->assertTrue($result->success);
        $this->assertEquals($payment->payment_type, $result->payment_type);
        $this->assertEquals( null, $result->description);
    }

    #[Test]
    public function it_can_refund_a_order_payment_with_a_reason(): void
    {
        $payment = OrderPayment::factory()->create(['payment_id' => 'some_payment_id']);

        $expected_stripe_refund = Refund::constructFrom([
            'id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'amount' => 500,
            'status' => 'succeeded'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_stripe_refund) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createRefund')
                ->once()
                ->with(
                    [
                        'charge' => 'some_payment_id',
                        'amount' => null,
                        'metadata' => ['description' => 'some reason']
                    ]
                )
                ->andReturn($expected_stripe_refund);
        });

        $result = (new StripeStandardAccount)->refundPayment($payment, null, 'some reason');

        $this->assertEquals(Transaction::TYPE_REFUND, $result->type);
        $this->assertEquals('src_1JroTYC1Poh057RbV7LbT4iS', $result->id);
        $this->assertEquals(500, $result->amount);
        $this->assertTrue($result->success);
        $this->assertEquals($payment->payment_type, $result->payment_type);
        $this->assertEquals( 'some reason', $result->description);
    }

    #[Test]
    public function it_can_retrieve_a_customer_source(): void
    {
        $expected_stripe_card = \Stripe\Card::constructFrom([
            'id' => 'src_123',
            'name' => 'test test',
            'brand' => 'visa',
            'exp_month' => 5,
            'exp_year' => 2022,
            'last4' => '4242'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_stripe_card) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerSource')
                ->once()
                ->with('cus_123', 'src_123')
                ->andReturn($expected_stripe_card);
        });

        $result = (new StripeStandardAccount)->retrieveCustomerSource('cus_123', 'src_123');

        $this->assertEquals('src_123', $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals('test test', $result->customer_name);
        $this->assertEquals('5', $result->exp_month);
        $this->assertEquals('2022', $result->exp_year);
        $this->assertEquals('visa', $result->brand);
        $this->assertEquals('4242', $result->last_four);
    }

    #[Test]
    public function it_can_create_a_customer_from_a_user_and_token(): void
    {
        $user = User::factory()->create();

        $expected_customer = Customer::constructFrom([
            'id' => 'cus_1JroTYC1Poh057RbV7LbT4iS',
            'email' => '<EMAIL>',
            'default_source' => null
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($user, $expected_customer) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createCustomer')
                ->once()
                ->with([
                    'source' => null,
                    'email' => $user->email,
                    'metadata' => [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'phone' => $user->phone
                    ],
                    'description' => $user->full_name
                ])
                ->andReturn($expected_customer);
        });

        $created_customer = (new StripeStandardAccount)->createCustomer($user);

        $this->assertEquals('cus_1JroTYC1Poh057RbV7LbT4iS', $created_customer->id);
        $this->assertEquals('<EMAIL>', $created_customer->email);
        $this->assertNull($created_customer->first_name);
        $this->assertNull($created_customer->last_name);
        $this->assertNull($created_customer->address_1);
        $this->assertNull($created_customer->address_2);
        $this->assertNull($created_customer->address_city);
        $this->assertNull($created_customer->address_state);
        $this->assertNull($created_customer->address_zip);
        $this->assertNull($created_customer->address_country);
    }

    #[Test]
    public function it_can_create_a_customer_from_a_user_without_a_token(): void
    {
        $user = User::factory()->create();

        $expected_customer = Customer::constructFrom([
            'id' => 'cus_1JroTYC1Poh057RbV7LbT4iS',
            'email' => '<EMAIL>',
            'default_source' => null
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($user, $expected_customer) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createCustomer')
                ->once()
                ->with([
                    'source' => null,
                    'email' => $user->email,
                    'metadata' => [
                        'first_name' => $user->first_name,
                        'last_name' => $user->last_name,
                        'phone' => $user->phone
                    ],
                    'description' => $user->full_name
                ])
                ->andReturn($expected_customer);
        });

        $created_customer = (new StripeStandardAccount)->createCustomer($user);

        $this->assertEquals('cus_1JroTYC1Poh057RbV7LbT4iS', $created_customer->id);
        $this->assertEquals('<EMAIL>', $created_customer->email);
        $this->assertNull($created_customer->first_name);
        $this->assertNull($created_customer->last_name);
        $this->assertNull($created_customer->address_1);
        $this->assertNull($created_customer->address_2);
        $this->assertNull($created_customer->address_city);
        $this->assertNull($created_customer->address_state);
        $this->assertNull($created_customer->address_zip);
        $this->assertNull($created_customer->address_country);
    }

    #[Test]
    public function it_can_create_a_customer_source(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);
        $expected_card = \Stripe\Card::constructFrom([
            'id' => 'src_123',
            'name' => 'test test',
            'brand' => 'visa',
            'exp_month' => 5,
            'exp_year' => 2022,
            'last4' => '4242'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($user, $expected_card) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createCustomerSource')
                ->once()
                ->with('cus_123', [
                    'source' => 'tok_123',
                    'metadata' => [
                        'email' => '<EMAIL>',
                        'name' => 'test name'
                    ]
                ])
                ->andReturn($expected_card);
        });

        $result = (new StripeStandardAccount)->createCustomerSource($user, 'tok_123', ['email' => '<EMAIL>', 'name' => 'test name']);

        $this->assertEquals('src_123', $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals('test test', $result->customer_name);
        $this->assertEquals('5', $result->exp_month);
        $this->assertEquals('2022', $result->exp_year);
        $this->assertEquals('visa', $result->brand);
        $this->assertEquals('4242', $result->last_four);
    }

    #[Test]
    public function it_can_set_a_default_card_from_a_source(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'src_123']);
        $expected_customer = \Stripe\Customer::constructFrom(['id' => 'cus_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $expected_customer) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('updateCustomer')
                ->once()
                ->with('cus_123', [
                    'default_source' => 'src_123',
                    'invoice_settings' => [
                        'default_payment_method' => null
                    ]
                ])
                ->andReturn($expected_customer);

            $mock->shouldReceive('retrieveDefaultSource')->once()
                ->with('cus_123')
                ->andReturn(\Stripe\Card::constructFrom([
                    'id' => 'src_123',
                    'name' => 'test test',
                    'brand' => 'visa',
                    'exp_month' => 5,
                    'exp_year' => 2022,
                    'last4' => '4242'
                ]));
        });

        $result = (new StripeStandardAccount)->setDefaultCard($card);

        $this->assertEquals($card->source_id, $result->id);
        $this->assertEquals($card->user->customer_id, $result->customer_id);
        $this->assertEquals('test test', $result->customer_name);
        $this->assertEquals('5', $result->exp_month);
        $this->assertEquals('2022', $result->exp_year);
        $this->assertEquals('visa', $result->brand);
        $this->assertEquals('4242', $result->last_four);
    }

    #[Test]
    public function it_can_set_a_default_card_from_a_payment_method(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'pm_123']);
        $expected_payment_method = \Stripe\PaymentMethod::constructFrom(['id' => 'pm_123']);
        $details = new \App\Billing\Gateway\PaymentMethod(
            id: 'pm_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'test test',
            exp_month: 'foo',
            exp_year: 'bar',
            brand: 'visa',
            last_four: '4242',
        );

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $expected_payment_method, $details) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrievePaymentMethod')->once()->with('pm_123')->andReturn($expected_payment_method);

            $mock->shouldReceive('updatePaymentMethod')
                ->once()
                ->with($expected_payment_method,  ['card' => [
                    'exp_month' => 'foo',
                    'exp_year' => 'bar',
                ]])
                ->andReturn($expected_payment_method);
        });

        $result = (new StripeStandardAccount)->updateCardDetails($card, $details);

        $this->assertEquals($card->source_id, $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals($card->cardholder_name, $result->customer_name);
        $this->assertEquals('foo', $result->exp_month);
        $this->assertEquals('bar', $result->exp_year);
    }

    #[Test]
    public function it_can_update_a_customer(): void
    {
        $expected_customer = Customer::constructFrom([
            'id' => 'cus_1JroTYC1Poh057RbV7LbT4iS',
            'default_source' => null
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_customer) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('updateCustomer')
                ->once()
                ->with(
                    'some_id',
                    [
                        'email' => '<EMAIL>',
                        'description' => 'first name last name',
                        'metadata' => [
                            'first_name' => 'first name',
                            'last_name' => 'last name',
                        ],
                        'shipping' => [
                            'name' => 'first name last name',
                            'address' => [
                                'line1' => 'some address 1',
                                'line2' => 'some address 2',
                                'city' => 'some city',
                                'country' => 'some country',
                                'postal_code' => 'some postal code',
                                'state' => 'some state'
                            ],
                        ]
                    ]
                )
                ->andReturn($expected_customer);
        });

        $updated_customer = (new StripeStandardAccount)->updateCustomer('some_id', new \App\Billing\Gateway\Customer(
            id: 'cus_1JroTYC1Poh057RbV7LbT4iS',
            email: '<EMAIL>',
            first_name: 'first name',
            last_name: 'last name',
            address_1: 'some address 1',
            address_2: 'some address 2',
            address_city: 'some city',
            address_state: 'some state',
            address_country: 'some country',
            address_zip: 'some postal code',
        ));

        $this->assertEquals('cus_1JroTYC1Poh057RbV7LbT4iS', $updated_customer->id);
        $this->assertEquals('<EMAIL>', $updated_customer->email);
        $this->assertEquals('first name', $updated_customer->first_name);
        $this->assertEquals('last name', $updated_customer->last_name);
        $this->assertEquals('some address 1', $updated_customer->address_1);
        $this->assertEquals('some address 2', $updated_customer->address_2);
        $this->assertEquals('some city', $updated_customer->address_city);
        $this->assertEquals('some state', $updated_customer->address_state);
        $this->assertEquals('some postal code', $updated_customer->address_zip);
        $this->assertEquals('some country', $updated_customer->address_country);
    }

    #[Test]
    public function it_can_set_a_update_card_details_on_a_source(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'src_123']);
        $expected_card = \Stripe\Card::constructFrom([
            'id' => 'src_123',
            'name' => 'test test',
            'brand' => 'visa',
            'exp_month' => 1,
            'exp_year' => 2,
            'last4' => '4242'
        ]);
        $details = new \App\Billing\Gateway\PaymentMethod(
            id: 'pm_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'test test',
            exp_month: 1,
            exp_year: 2,
            brand: 'visa',
            last_four: '4242',
        );

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $expected_card, $details) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerSource')->once()->with('cus_123', 'src_123')->andReturn($expected_card);

            $mock->shouldReceive('updateCustomerCard')
                ->once()
                ->with($expected_card, [
                    'cardholder_name' => $details->customer_name,
                    'exp_month' => $details->exp_month,
                    'exp_year' => $details->exp_year,
                ])
                ->andReturn($expected_card);
        });

        $result = (new StripeStandardAccount)->updateCardDetails($card, $details);

        $this->assertEquals($card->source_id, $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals('test test', $result->customer_name);
        $this->assertEquals(1, $result->exp_month);
        $this->assertEquals(2, $result->exp_year);
    }

    #[Test]
    public function it_can_set_a_update_card_details_on_a_payment_method(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'pm_123']);
        $expected_payment_method = \Stripe\PaymentMethod::constructFrom(['id' => 'pm_123']);
        $details = new \App\Billing\Gateway\PaymentMethod(
            id: 'pm_1JroTYC1Poh057RbV7LbT4iS',
            customer_id: 'cus_1234',
            customer_name: 'test test',
            exp_month: 'foo',
            exp_year: 'bar',
            brand: 'visa',
            last_four: '4242',
        );

        $this->mock(Stripe::class, function (MockInterface $mock) use ($card, $expected_payment_method, $details) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrievePaymentMethod')->once()->with('pm_123')->andReturn($expected_payment_method);

            $mock->shouldReceive('updatePaymentMethod')
                ->once()
                ->with($expected_payment_method,  ['card' => [
                    'exp_month' => 'foo',
                    'exp_year' => 'bar',
                ]])
                ->andReturn($expected_payment_method);
        });

        $result = (new StripeStandardAccount)->updateCardDetails($card, $details);

        $this->assertEquals($card->source_id, $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals($card->cardholder_name, $result->customer_name);
        $this->assertEquals('foo', $result->exp_month);
        $this->assertEquals('bar', $result->exp_year);
    }

    #[Test]
    public function it_can_delete_a_card(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_1234']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'src_1234']);

        $stripe_card = \Stripe\Card::constructFrom([
            'id' => 'src_1JroTYC1Poh057RbV7LbT4iS',
            'customer' => 'cus_1JroTYC1Poh057RbV7LbT4iS',
            'brand' => 'some brand',
            'exp_month' => '05',
            'exp_year' => '2028',
            'last4' => '0022',
            'name' => 'some name'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($stripe_card, $card) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('deleteCustomerSource')
                ->once()
                ->with('cus_1234', 'src_1234')
                ->andReturn($stripe_card);
        });

        (new StripeStandardAccount)->deleteCard($user->customer_id, $card->source_id);
    }

    public function it_can_delete_a_payment_method()
    {
        $user = User::factory()->create(['customer_id' => 'cus_1234']);
        $card = Card::factory()->create(['user_id' => $user->id, 'source_id' => 'pm_12345']);

        $payment_method = \Stripe\PaymentMethod::constructFrom([
            'id' => 'pm_12345'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($payment_method, $card) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('detachPaymentMethod')
                ->once()
                ->with('pm_12345')
                ->andReturn($payment_method);
        });

        (new StripeStandardAccount)->deleteCard($card);
    }

    #[Test]
    public function it_can_capture_a_payment_intent(): void
    {
        $expected_intent = PaymentIntent::constructFrom(['id' => 'int_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_intent) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('capturePaymentIntent')
                ->once()
                ->with('int_123')
                ->andReturn($expected_intent);
        });

        $this->assertEquals($expected_intent, (new StripeStandardAccount)->capturePaymentIntent('int_123'));
    }

    #[Test]
    public function it_can_create_a_setup_intent_for_a_user(): void
    {
        $expected_intent = SetupIntent::constructFrom(['id' => 'seti_123']);

        $user = User::factory()->create(['customer_id' => 'cus_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_intent) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('createSetupIntent')
                ->once()
                ->with([
                    'payment_method_types' => ['card'],
                    'usage' => 'off_session',
                    'customer' => 'cus_123',
                ])
                ->andReturn($expected_intent);
        });

        $this->assertEquals($expected_intent, (new StripeStandardAccount)->createSetupIntent($user));
    }

    #[Test]
    public function it_can_fetch_a_setup_intent_by_id(): void
    {
        $expected_intent = SetupIntent::constructFrom(['id' => 'seti_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_intent) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('fetchSetupIntent')
                ->once()
                ->with('seti_123')
                ->andReturn($expected_intent);
        });

        $this->assertEquals($expected_intent, (new StripeStandardAccount)->fetchSetupIntent('seti_123'));
    }

    #[Test]
    public function it_can_fetch_a_payment_method_by_id(): void
    {
        $expected_method = PaymentMethod::constructFrom(['id' => 'pm_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($expected_method) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('fetchPaymentMethod')
                ->once()
                ->with('pm_123')
                ->andReturn($expected_method);
        });

        $this->assertEquals($expected_method, (new StripeStandardAccount)->fetchPaymentMethod('pm_123'));
    }

    #[Test]
    public function it_can_retrieve_a_users_default_source_when_they_have_one(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);

        $stripe_card = \Stripe\Card::constructFrom([
            'id' => 'src_123',
            'name' => 'test test',
            'brand' => 'visa',
            'exp_month' => 5,
            'exp_year' => 2022,
            'last4' => '4242'
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($stripe_card) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveDefaultSource')
                ->once()
                ->with('cus_123')
                ->andReturn($stripe_card);
        });

        $result = (new StripeStandardAccount)->retrieveDefaultSourceForUser($user);

        $this->assertEquals('src_123', $result->id);
        $this->assertEquals('cus_123', $result->customer_id);
        $this->assertEquals('test test', $result->customer_name);
        $this->assertEquals('5', $result->exp_month);
        $this->assertEquals('2022', $result->exp_year);
        $this->assertEquals('visa', $result->brand);
        $this->assertEquals('4242', $result->last_four);
    }

    #[Test]
    public function it_can_retrieve_a_users_default_source_when_they_dont_have_one(): void
    {
        $user = User::factory()->create(['customer_id' => 'cus_123']);

        $this->mock(Stripe::class, function (MockInterface $mock) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveDefaultSource')
                ->once()
                ->with('cus_123')
                ->andReturnNull();
        });

        $result = (new StripeStandardAccount)->retrieveDefaultSourceForUser($user);

        $this->assertNull($result);
    }

    #[Test]
    public function it_can_fetch_user_payment_methods(): void
    {
        $user = User::factory()->make(['customer_id' => 'cus_123']);

        $response = Collection::constructFrom([
            'data' => [
                PaymentMethod::constructFrom([
                    'id' => 'pm_123',
                    'billing_details' => [
                        'name' => 'John Doe',
                    ],
                    'card' => [
                        'brand' => 'visa',
                        'exp_month' => 5,
                        'exp_year' => 2022,
                        'last4' => '4242'
                    ],
                    'customer' => 'cus_123'
                ]),
                PaymentMethod::constructFrom([
                    'id' => 'pm_456',
                    'billing_details' => [
                        'name' => 'Jane Doe',
                    ],
                    'card' => [
                        'brand' => 'amex',
                        'exp_month' => 6,
                        'exp_year' => 2028,
                        'last4' => '1234'
                    ],
                    'customer' => 'cus_123'
                ]),
            ]
        ]);

        $this->mock(Stripe::class, function (MockInterface $mock) use ($response) {
            $mock->shouldReceive('overrideApiKey')->once()
                ->with('secret_123')
                ->andReturnSelf();

            $mock->shouldReceive('retrieveCustomerPaymentMethods')
                ->once()
                ->with('cus_123')
                ->andReturn($response);
        });

        $result = (new StripeStandardAccount)->retrieveUserPaymentMethods($user);

        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
        $this->assertEquals(2, $result->count());
        $this->assertTrue($result->contains('id', 'pm_123'));
        $this->assertTrue($result->contains('id', 'pm_456'));
    }

    protected function setUp(): void
    {
        parent::setUp();

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::updateOrCreate(['key' => 'stripe_secret_key'], ['value' => encrypt('secret_123')]);
    }
}
