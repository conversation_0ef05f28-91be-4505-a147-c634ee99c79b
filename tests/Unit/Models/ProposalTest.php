<?php

namespace Tests\Unit\Models;

use App\Models\Proposal;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProposalTest extends TenantTestCase
{
    #[Test]
    public function it_can_fetch_the_days_of_week_attribute(): void
    {
        $proposal = Proposal::factory()->make(['days_of_week' => null]);

        $this->assertNull($proposal->days_of_week);

        $proposal->days_of_week = json_encode([1,2,3]);
        $this->assertEquals([1,2,3], $proposal->days_of_week);
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $proposal = Proposal::factory()->create();

        $this->actingAsAdmin()->get(route('admin.proposals.show', compact('proposal')))
            ->assertRedirect(route('admin.proposals.edit', compact('proposal')));
    }
}