<?php

namespace Tests\Unit\Models;

use App\Cart\Item;
use App\Exceptions\CouponInvalidException;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\Product;
use App\Services\SettingsService;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Support\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CouponTest extends TenantTestCase
{
    #[Test]
    public function it_can_be_converted_to_a_cart_coupon(): void
    {
        $coupon = Coupon::factory()->make([
            'description' => '20$',
            'code' => '1A2B3C',
            'discount_amount' => 4567
        ]);
        $coupon->setRelation('pivot', new Pivot(['savings' => 1234]));

        $cart_coupon = $coupon->toCartCoupon();

        $this->assertInstanceOf(\App\Cart\Coupon::class, $cart_coupon);
        $this->assertEquals('20$', $cart_coupon->name);
        $this->assertEquals('1A2B3C', $cart_coupon->code);
        $this->assertEquals(1234, $cart_coupon->amount);
    }

    #[Test]
    public function it_can_get_its_delivery_value_for_an_order(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'delivery']);
        $this->assertEquals(1234, $coupon->valueForOrder(Order::factory()->make(['delivery_fee' => 1234])));
    }

    #[Test]
    public function it_can_get_its_fixed_value_for_an_order(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'fixed', 'discount_amount' => 1234]);
        $this->assertEquals(1234, $coupon->valueForOrder(Order::factory()->make()));
    }

    #[Test]
    public function it_can_get_its_percentage_value_for_an_order(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'percentage', 'discount_percentage' => 10]);
        $this->assertEquals(123, $coupon->valueForOrder(Order::factory()->make(['total' => 1234])));
    }

    #[Test]
    public function it_can_get_its_delivery_value_for_a_cart(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'delivery']);

        $cart = new Cart();
        $cart->extra_attributes->set('delivery_method_id', Pickup::factory()->create(['delivery_rate' => 5])->id);
        $this->assertEquals(500, $coupon->valueForCart($cart));
    }

    #[Test]
    public function it_can_get_its_fixed_value_for_a_cart(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'fixed', 'discount_amount' => 1234]);
        $this->assertEquals(1234, $coupon->valueForCart(new Cart));
    }

    #[Test]
    public function it_can_get_its_percentage_value_for_an_cart(): void
    {
        $coupon = Coupon::factory()->make(['discount_type' => 'percentage', 'discount_percentage' => 10]);
        $cart = Cart::factory()->create();
        $cart->addItemToCart(new Item('abc', Product::factory()->create(['unit_price' => '12.34']),2));
        $this->assertEquals(247, $coupon->valueForCart($cart));
    }

    #[Test]
    public function it_can_get_its_percentage_value_for_a_cart(): void
    {
        $cart = Cart::factory()->create();
        $cart->addItemToCart(new Item('abc', Product::factory()->create(['unit_price' => '12.34']),2));

        $coupon = Coupon::factory()->make(['discount_type' => 'percentage', 'discount_percentage' => 10]);

        $this->assertEquals(247, $coupon->valueForCart($cart));
    }


    #[Test]
    public function it_throws_exception_when_order_does_not_meet_minimum(): void
    {
        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon has already been applied.');

        $coupon = Coupon::factory()->create();
        $order = Order::factory()->create();

        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id]);

        $coupon->validateForCart($order);
    }

    #[Test]
    public function it_throws_exception_when_multiple_coupons_are_not_allowed(): void
    {
        $this->partialMock(SettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('preventsMultipleCouponsPerOrder')->once()->withNoArgs()->andReturnTrue();
        });

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('Only one coupon can be applied at a time.');

        $coupon_one = Coupon::factory()->create();
        $coupon_two = Coupon::factory()->create();
        $order = Order::factory()->create();

        $order->discounts()->attach($coupon_one->id, ['user_id' => $order->customer_id]);

        $coupon_two->validateForCart($order);
    }

    #[Test]
    public function it_throws_exception_when_coupon_has_expired(): void
    {
        Carbon::setTestNow(now());

        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon has expired.');

        $coupon = Coupon::factory()->create(['expires' => true, 'expires_at' => now()->subSecond()]);
        $order = Order::factory()->create();

        $coupon->validateForCart($order);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_throws_exception_when_coupon_has_reached_usage_limit(): void
    {
        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon has reached its usage limit.');

        $coupon = Coupon::factory()->create(['limit_usage' => true, 'max_uses' => 1]);
        $order = Order::factory()->create();

        $other_order = Order::factory()->create();
        $other_order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id]);

        $coupon->validateForCart($order);
    }

    #[Test]
    public function it_throws_exception_when_coupon_has_already_been_used_by_customer(): void
    {
        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('You have already redeemed this coupon.');

        $coupon = Coupon::factory()->create(['once_per_customer' => true]);
        $order = Order::factory()->create();
        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id]);

        $other_order = Order::factory()->create(['customer_id' =>  $order->customer_id]);

        $coupon->validateForCart($other_order);
    }

    #[Test]
    public function it_throws_exception_when_order_does_not_meet_coupon_minimum(): void
    {
        $this->expectException(CouponInvalidException::class);
        $this->expectExceptionMessage('This coupon can only be applied on orders of &#36;12.34 or more.');

        $coupon = Coupon::factory()->create(['min_order' => true, 'min_order_amount' => 1234]);
        $order = Order::factory()->create();

        $coupon->validateForCart($order);
    }

    #[Test]
    public function it_can_be_converted_to_cart_coupon(): void
    {
        $coupon = Coupon::factory()->create();
        $coupon->setRelation('pivot', new Pivot(['savings' => 1234]));

        $cart_coupon = $coupon->toCartCoupon();
        $this->assertEquals($coupon->description, $cart_coupon->name);
        $this->assertEquals($coupon->code, $cart_coupon->code);
        $this->assertEquals(1234, $cart_coupon->amount);
    }
}