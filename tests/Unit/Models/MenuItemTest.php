<?php

namespace Tests\Unit\Models;

use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MenuItemTest extends TenantTestCase
{
    #[Test]
    public function it_clears_its_menu_cache_on_create(): void
    {
        $menu = Menu::factory()->create();

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldReceive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item = MenuItem::factory()->create(['menu_id' => $menu->id]);
    }

    #[Test]
    public function it_clears_its_menu_cache_on_update(): void
    {
        $menu = Menu::factory()->create();
        $menu_item = MenuItem::factory()->create(['menu_id' => $menu->id]);

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldR<PERSON>eive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item->title = 'abc';
        $menu_item->save();
    }

    #[Test]
    public function it_clears_its_menu_cache_on_delete(): void
    {
        $menu = Menu::factory()->create();
        $menu_item = MenuItem::factory()->create(['menu_id' => $menu->id]);

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldReceive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item->delete();
    }

    #[Test]
    public function a_submenu_item_clears_its_parent_menu_cache_on_create(): void
    {
        $menu = Menu::factory()->create();
        $submenu = Menu::factory()->create(['submenu' => true, 'parent_id' => $menu->id]);

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldReceive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item = MenuItem::factory()->create(['menu_id' => $submenu->id]);
    }

    #[Test]
    public function a_submenu_item_clears_its_parent_menu_cache_on_update(): void
    {
        $menu = Menu::factory()->create();
        $submenu = Menu::factory()->create(['submenu' => true, 'parent_id' => $menu->id]);
        $menu_item = MenuItem::factory()->create(['menu_id' => $submenu->id]);

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldReceive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item->title = 'abc';
        $menu_item->save();
    }

    #[Test]
    public function a_submenu_item_clears_its_parent_menu_cache_on_delete(): void
    {
        $menu = Menu::factory()->create();
        $submenu = Menu::factory()->create(['submenu' => true, 'parent_id' => $menu->id]);
        $menu_item = MenuItem::factory()->create(['menu_id' => $submenu->id]);

        Cache::shouldReceive('tags')
            ->andReturnSelf()
            ->shouldReceive('flush')
            ->shouldReceive('forget')
            ->once()
            ->with($menu->cacheKey());

        $menu_item->delete();
    }
}
