<?php

namespace Tests\Unit\Geocoding;

use App\Exceptions\NoGeocodeResultsException;
use App\Models\Setting;
use App\Services\Geocoding\GeocodedAddress;
use App\Services\Geocoding\GeocoderCa;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GeocoderCaTest extends TenantTestCase
{
    #[Test]
    public function it_can_geocode_an_address_string_with_configured_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'], ['value'  => 'some-country']);

        $address_string = ' 123 Fake St, Fakeville, FK, ABC 123 ';

        $result = (new GeocoderCa)->fromAddress($address_string);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === trim($address_string) &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_string_with_default_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $address_string = ' 123 Fake St, Fakeville, FK, ABC 123 ';

        $result = (new GeocoderCa)->fromAddress($address_string);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === trim($address_string) &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_array_with_configured_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'some-country']);

        $result = (new GeocoderCa)->fromAddressParts([
            'street' => ' 123 Fake St ',
            'city' => ' Fakeville ',
            'state' => ' FK ',
            'zip' => ' ABC 123 '
        ]);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === '123 Fake St, Fakeville, FK, ABC 123' &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_array_with_default_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $result = (new GeocoderCa)->fromAddressParts([
            'street' => ' 123 Fake St ',
            'city' => ' Fakeville ',
            'state' => ' FK ',
            'zip' => ' ABC 123 '
        ]);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === '123 Fake St, Fakeville, FK, ABC 123' &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_zipcode_with_configured_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'some-country']);

        $result = (new GeocoderCa)->fromZipcode(' ABC 123 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === 'ABC 123' &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_zipcode_with_default_country_setting(): void
    {
        Http::fake([ '*' => $this->geocodeResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $result = (new GeocoderCa)->fromZipcode(' ABC 123 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            'ABC 123',
            'country response',
            0.9
        ), $result);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://geocoder.ca' &&
                $request['locate'] === 'ABC 123' &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_an_exception_when_geocoding_an_address_string_that_returns_no_results(): void
    {
        Http::fake([ '*' => $this->geocodeEmptyResult() ]);

        config(['services.geocoderCa.key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new GeocoderCa)->fromAddress($address_string);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['locate'] === trim($address_string) &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_a_no_result_exception_when_a_request_exception_is_thrown(): void
    {
        Http::fake([
            '*' =>  Http::response(['error' => ['description' => 'Test was an error.']], 400)
        ]);

        config(['services.geocoderCa.key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new GeocoderCa)->fromAddress($address_string);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['locate'] === trim($address_string) &&
                $request['json'] === '1' &&
                $request['showcountry'] === '1' &&
                $request['country'] === 'canada' &&
                $request['auth'] === 'some-api-key';
        });
    }

    private function geocodeResult($accuracy = 0.9): PromiseInterface
    {
        return Http::response([
            'latt' => 123.45,
            'longt' => 67.89,
            'standard' => [
                'city' => 'Fakeville',
                'prov' => 'FK',
                'confidence' => $accuracy,
            ],
            'postal' => 'ABC 123',
            'country' => 'country response'
        ]);
    }

    private function geocodeEmptyResult(): PromiseInterface
    {
        return Http::response(['error' => ['description' => 'Test was an error.']]);
    }
}