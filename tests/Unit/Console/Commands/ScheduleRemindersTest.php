<?php

namespace Tests\Unit\Console\Commands;

use App\Jobs\ScheduleDeadlineReminderEmail;
use App\Models\Schedule;
use Carbon\Carbon;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ScheduleRemindersTest extends TestCase
{
    #[Test]
    public function it_schedules_deadline_reminder_emails(): void
    {
        Carbon::setTestNow($now = now());

        /** @var Schedule $schedule */
        $schedule_one = Schedule::factory()->create(['reminder_enabled' => true]);

        $this->mock(ScheduleDeadlineReminderEmail::class, function (MockInterface $mock) use ($schedule_one) {
            $mock->shouldReceive('handle')->once()->with($schedule_one->id)->andReturnSelf();
        });

        $this->artisan("grazecart:reminders")
            ->assertExitCode(0);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_schedule_deadline_reminder_emails_for_schedules_with_reminders_disabled(): void
    {
        Carbon::setTestNow($now = now());

        Schedule::factory()->create(['reminder_enabled' => false]);

        $this->mock(ScheduleDeadlineReminderEmail::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->artisan("grazecart:reminders")
            ->assertExitCode(0);

        Carbon::setTestNow();
    }
}
