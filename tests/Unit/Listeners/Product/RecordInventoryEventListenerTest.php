<?php

namespace Tests\Unit\Listeners\Product;

use App\Events\Product\InventoryDecreasedToThresholdOrBelow;
use App\Events\Product\InventoryDecreasedToZeroOrBelow;
use App\Events\Product\InventoryIncreasedAboveThreshold;
use App\Events\Product\InventoryIncreasedAboveZero;
use App\Listeners\Product\RecordInventoryEvent;
use App\Models\Event;
use App\Models\Product;
use Illuminate\Support\Facades\Event as EventBus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordInventoryEventListenerTest extends TenantTestCase
{
    #[Test]
    public function it_listens_to_expected_event(): void
    {
        EventBus::fake([InventoryIncreasedAboveThreshold::class]);

        EventBus::assertListening(InventoryIncreasedAboveThreshold::class, RecordInventoryEvent::class);

        EventBus::fake([InventoryDecreasedToZeroOrBelow::class]);

        EventBus::assertListening(InventoryDecreasedToZeroOrBelow::class, RecordInventoryEvent::class);
    }

    #[Test]
    public function it_records_product_inventory_decreased_to_threshold_or_below_events(): void
    {
        $product = Product::factory()->create();

        $event = new InventoryDecreasedToThresholdOrBelow($product->id, 10, ['current_inventory' => 2, 'order_id' => 1]);

        (new RecordInventoryEvent())->handle($event);

        $this->assertDatabaseHas(Event::class, [
            'user_id' => 10,
            'model_id' => $product->id,
            'model_type' => Product::class,
            'event_id' => InventoryDecreasedToThresholdOrBelow::class,
            'description' => $event->description(),
            'metadata' => json_encode(['current_inventory' => 2, 'order_id' => 1]),
        ]);
    }

    #[Test]
    public function it_records_product_inventory_decreased_to_zero_or_below_events(): void
    {
        $product = Product::factory()->create();

        $event = new InventoryDecreasedToZeroOrBelow($product->id, 10, ['current_inventory' => 2, 'order_id' => 1]);

        (new RecordInventoryEvent())->handle($event);

        $this->assertDatabaseHas(Event::class, [
            'user_id' => 10,
            'model_id' => $product->id,
            'model_type' => Product::class,
            'event_id' => InventoryDecreasedToZeroOrBelow::class,
            'description' => $event->description(),
            'metadata' => json_encode(['current_inventory' => 2, 'order_id' => 1]),
        ]);
    }

    #[Test]
    public function it_records_product_inventory_increased_above_zero_events(): void
    {
        $product = Product::factory()->create();

        $event = new InventoryIncreasedAboveZero($product->id, 10, ['current_inventory' => 2, 'order_id' => 1]);

        (new RecordInventoryEvent())->handle($event);

        $this->assertDatabaseHas(Event::class, [
            'user_id' => 10,
            'model_id' => $product->id,
            'model_type' => Product::class,
            'event_id' => InventoryIncreasedAboveZero::class,
            'description' => $event->description(),
            'metadata' => json_encode(['current_inventory' => 2, 'order_id' => 1]),
        ]);
    }

    #[Test]
    public function it_records_product_inventory_increased_above_threshold_events(): void
    {
        $product = Product::factory()->create();

        $event = new InventoryIncreasedAboveThreshold($product->id, 10, ['current_inventory' => 2, 'order_id' => 1]);

        (new RecordInventoryEvent())->handle($event);

        $this->assertDatabaseHas(Event::class, [
            'user_id' => 10,
            'model_id' => $product->id,
            'model_type' => Product::class,
            'event_id' => InventoryIncreasedAboveThreshold::class,
            'description' => $event->description(),
            'metadata' => json_encode(['current_inventory' => 2, 'order_id' => 1]),
        ]);
    }
}
