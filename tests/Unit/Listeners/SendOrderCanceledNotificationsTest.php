<?php

namespace Tests\Unit\Listeners;

use App\Events\Order\OrderWasCanceled;
use App\Listeners\Order\OrderWasCanceled\SendOrderCanceledNotifications;
use App\Mail\OrderCanceled;
use App\Models\Order;
use App\Models\Setting;
use App\Models\Template;
use App\Models\User;
use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SendOrderCanceledNotificationsTest extends TenantTestCase
{
    #[Test]
    public function it_listens_to_expected_event(): void
    {
        Event::fake([OrderWasCanceled::class]);

        Event::assertListening(
            OrderWasCanceled::class,
            SendOrderCanceledNotifications::class
        );
    }

    #[Test]
    public function it_sends_a_notification_to_the_order_customer_when_cancellation_template_exists(): void
    {
        Mail::fake();
        Notification::fake();

        $template = Template::factory()->create();
        Setting::updateOrCreate(['key' => 'email_order_cancellation_template'], ['value' => $template->id]);

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Mail::assertQueued(
            OrderCanceled::class,
            function (OrderCanceled $mail) use ($order) {
                return $mail->hasTo($order->customer_email);
            }
        );
    }

    #[Test]
    public function it_sends_a_notification_to_the_order_customer_when_cancellation_template_does_not_exist(): void
    {
        Mail::fake();
        Notification::fake();

        Setting::updateOrCreate(['key' => 'email_order_cancellation_template'], ['value' => 12039781203]);

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Mail::assertNotQueued(OrderCanceled::class);
    }

    #[Test]
    public function it_sends_a_notification_general_email_when_configured(): void
    {
        Mail::fake();
        Notification::fake();

        /** @var Order $order */
        $order = Order::factory()->create();

        Setting::updateOrCreate(['key' => 'notify_of_canceled_orders'],['value'  => false]);
        Setting::updateOrCreate(['key' => 'email_general'],['value'  => '<EMAIL>']);

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Setting::updateOrCreate(['key' => 'notify_of_canceled_orders'],['value'  => true]);

        Notification::assertNotSentTo(new AnonymousNotifiable,\App\Notifications\OrderCanceled::class);

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Notification::assertSentTo(
            new AnonymousNotifiable,
            \App\Notifications\OrderCanceled::class,
            function (\App\Notifications\OrderCanceled $notification, $channels, $notifiable) use ($order) {
                return $notification->order_id === $order->id
                    && in_array('mail', $channels)
                    && $notifiable->routes['mail'] === '<EMAIL>';
            }
        );
    }

    #[Test]
    public function it_sends_mail_to_notifiable_admins_when_cancellation_template_exists(): void
    {
        Mail::fake();
        Notification::fake();

        $template = Template::factory()->create();
        Setting::updateOrCreate(['key' => 'email_order_cancellation_template'], ['value' => $template->id]);

        /** @var User $admin */
        $admin = User::factory()->admin()->create();

        /** @var Order $order */
        $order = Order::factory()->create();

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Mail::assertNotQueued(
            OrderCanceled::class,
            function (OrderCanceled $mail) use ($admin) {
                return $mail->hasTo($admin->email);
            }
        );

        $admin->settings = ['email_notify_cancellation' => 1];
        $admin->save();

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Mail::assertQueued(
            OrderCanceled::class,
            function (OrderCanceled $mail) use ($admin) {
                return $mail->hasTo($admin->email);
            }
        );
    }

    #[Test]
    public function it_sends_mail_to_notifiable_admins_when_cancellation_template_does_not_exist(): void
    {
        Mail::fake();
        Notification::fake();

        Setting::updateOrCreate(['key' => 'email_order_cancellation_template'], ['value' => 1290387123]);

        /** @var User $admin */
        $admin = User::factory()->admin()->create();

        /** @var Order $order */
        $order = Order::factory()->create();

        $admin->settings = ['email_notify_cancellation' => 1];
        $admin->save();

        (new SendOrderCanceledNotifications)->handle(new OrderWasCanceled($order));

        Mail::assertNotQueued( OrderCanceled::class);
    }
}
