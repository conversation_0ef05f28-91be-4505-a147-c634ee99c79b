<?php

namespace Tests\Unit\Listeners\Subscription;

use App\Events\Subscription\SubscriptionWasResumed;
use App\Listeners\Subscription\RecordSubscriptionWasResumed;
use App\Models\RecurringOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RecordSubscriptionWasResumedTest extends TenantTestCase
{
    #[Test]
    public function the_listener_is_registered(): void
    {
        Event::fake([SubscriptionWasResumed::class]);
        Event::assertListening(SubscriptionWasResumed::class, RecordSubscriptionWasResumed::class);
    }

    #[Test]
    public function it_records_the_expected_event(): void
    {
        Carbon::setTestNow(now());

        $subscription = RecurringOrder::factory()->create([
            'reorder_frequency' => 14,
            'deleted_at' => now()->subDay()
        ]);

        event(new SubscriptionWasResumed(
            subscription: $subscription,
            old_deleted_at: now()->subDay(),
            old_frequency: 14,
            old_promo_item_id: 542,
            new_delivery_date: now()->addDays(2),
            new_frequency: 28,
            new_promo_item_id: 1234
        ));

        $this->assertDatabaseHas(\App\Models\Event::class, [
            'user_id' => $subscription->customer_id,
            'model_id' => $subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionWasResumed::class,
            'description' => 'The subscription was resumed',
            'metadata' => json_encode([
                'old_deleted_at' => now()->subDay()->format('Y-m-d H:i:s'),
                'old_frequency' => 14,
                'old_promo_item_id' => 542,
                'new_delivery_date' => now()->addDays(2)->format('Y-m-d H:i:s'),
                'new_frequency' => 28,
                'new_promo_item_id' => 1234
            ])
        ]);

        Carbon::setTestNow();
    }
}
