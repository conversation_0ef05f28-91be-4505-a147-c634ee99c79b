<?php

namespace Tests\Unit\Jobs;

use App\Events\Product\InventoryDecreasedToThresholdOrBelow;
use App\Jobs\NotifyAdminsOfOutOfStockProducts;
use App\Models\Event;
use App\Models\Product;
use App\Models\Setting;
use App\Notifications\OutOfStockNotification;
use App\Services\SettingsService;
use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Support\Facades\Notification;
use NotificationChannels\Twilio\TwilioChannel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class NotifyAdminsOfOutOfStockProductsTest extends TenantTestCase
{
    #[Test]
    public function it_sends_notifications_when_there_are_out_of_stock_products(): void
    {
        Notification::fake();

        $settingsService = $this->mock(SettingsService::class);
        $settingsService->shouldReceive('all');
        $settingsService->shouldReceive('outOfStockSmsNotificationsEnabled')->andReturn(true);
        $settingsService->shouldReceive('outOfStockSmsNotificationsContacts')->andReturn(collect('202-555-0193'));

        $product = Product::factory()->create();

        Event::factory()->create([
            'model_type' => Product::class,
            'model_id' => $product->id,
            'event_id' => InventoryDecreasedToThresholdOrBelow::class,
            'created_at' => now()->startOfMinute()->subMinutes(4)->timezone('UTC'),
        ]);

        (new NotifyAdminsOfOutOfStockProducts())->handle();

        $formattedNumber = '+12025550193';
        
        Notification::assertSentTo(
            [new AnonymousNotifiable],
            OutOfStockNotification::class,
            function ($notification, $channels, $notifiable) use ($formattedNumber) {
                return $notifiable->routes[TwilioChannel::class] === $formattedNumber;
            }
        );
    }

    #[Test]
    public function it_does_not_send_notifications_when_out_of_stock_notifications_are_disabled(): void
    {
        Notification::fake();

        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsEnabled'], ['value' => false]);
        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsContacts'], ['value' => collect('202-555-0193')]);

        Event::factory()->create([
            'model_type' => Product::class,
            'model_id' => Product::factory()->create()->id,
            'event_id' => InventoryDecreasedToThresholdOrBelow::class,
            'created_at' => now()->startOfMinute()->addMinute()
        ]);

        (new NotifyAdminsOfOutOfStockProducts())->handle();

        Notification::assertNothingSent();
    }

    #[Test]
    public function it_does_not_send_notifications_when_there_are_no_contacts(): void
    {
        Notification::fake();
        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsEnabled'], ['value' => true]);
        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsContacts'], ['value' => '']);

        Event::factory()->create([
            'model_type' => Product::class,
            'model_id' => Product::factory()->create()->id,
            'event_id' => InventoryDecreasedToThresholdOrBelow::class,
            'created_at' => now()->startOfMinute()->addMinute()
        ]);

        (new NotifyAdminsOfOutOfStockProducts())->handle();

        Notification::assertNothingSent();
    }

    #[Test]
    public function it_does_not_send_notifications_when_there_are_no_out_of_stock_products(): void
    {
        Notification::fake();
        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsEnabled'], ['value' => true]);
        Setting::updateOrCreate(['key' => 'outOfStockSmsNotificationsContacts'], ['value' => '202-555-0193']);

        (new NotifyAdminsOfOutOfStockProducts())->handle();

        Notification::assertNothingSent();
    }

    #[Test]
    public function it_does_not_sends_notifications_for_out_of_stock_events_older_than_five_minutes(): void
    {
        Notification::fake();

        $settingsService = $this->mock(SettingsService::class);
        $settingsService->shouldReceive('all');
        $settingsService->shouldReceive('outOfStockSmsNotificationsEnabled')->andReturn(true);
        $settingsService->shouldReceive('outOfStockSmsNotificationsContacts')->andReturn(collect('202-555-0193'));

        Event::factory()->create([
            'model_type' => Product::class,
            'model_id' => Product::factory()->create()->id,
            'event_id' => InventoryDecreasedToThresholdOrBelow::class,
            'created_at' => now()->startOfMinute()->subMinutes(5)->subSecond()
        ]);

        (new NotifyAdminsOfOutOfStockProducts())->handle();

        Notification::assertNothingSent();
    }
}
