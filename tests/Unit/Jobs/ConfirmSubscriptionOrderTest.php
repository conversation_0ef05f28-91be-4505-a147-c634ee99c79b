<?php

namespace Tests\Unit\Jobs;

use App\Actions\Order\ConfirmRecurring;
use App\Events\Subscription\RecurringOrderWasConfirmed;
use App\Jobs\ConfirmSubscriptionOrder;
use App\Models\Order;
use App\Models\RecurringOrder;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ConfirmSubscriptionOrderTest extends TenantTestCase
{
    #[Test]
    public function it_confirms_an_order(): void
    {
        Event::fake([RecurringOrderWasConfirmed::class]);

        $order = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'confirmed' => 0, 'canceled' => 0]);

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) use ($order) {
            return $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(Order $arg) => $arg->id === $order->id), [])
                ->andReturn($order);
        });

        (new ConfirmSubscriptionOrder($order->id))->handle();

        Event::assertDispatched(
            RecurringOrderWasConfirmed::class,
            fn(RecurringOrderWasConfirmed $event) => $event->order->id === $order->id
        );
    }

    #[Test]
    public function it_does_not_confirm_a_canceled_order(): void
    {
        Event::fake([RecurringOrderWasConfirmed::class]);

        $order = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'confirmed' => 0, 'canceled' => 1]);

        $this->mock(ConfirmRecurring::class, function (MockInterface $mock) {
            return $mock->shouldNotReceive('handle');
        });

        (new ConfirmSubscriptionOrder($order->id))->handle();

        Event::assertNotDispatched(RecurringOrderWasConfirmed::class);
    }

    #[Test]
    public function it_does_not_confirm_an_already_confirmed_order(): void
    {
        Event::fake([RecurringOrderWasConfirmed::class]);

        $order = Order::factory()->create(['blueprint_id' => RecurringOrder::factory(), 'confirmed' => 1, 'canceled' => 0]);

        $this->mock(
            ConfirmRecurring::class,
            fn(MockInterface $mock) => $mock->shouldNotReceive('handle')
        );

        (new ConfirmSubscriptionOrder($order->id))->handle();

        Event::assertNotDispatched(RecurringOrderWasConfirmed::class);
    }

    #[Test]
    public function it_does_not_confirm_an_invalid_order(): void
    {
        Event::fake([RecurringOrderWasConfirmed::class]);

        $this->mock(
            ConfirmRecurring::class,
            fn(MockInterface $mock) => $mock->shouldNotReceive('handle')
        );

        (new ConfirmSubscriptionOrder(1231241234123))->handle();

        Event::assertNotDispatched(RecurringOrderWasConfirmed::class);
    }
}