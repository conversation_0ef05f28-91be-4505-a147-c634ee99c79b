<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class VendorTest extends TenantTestCase
{
    public function setup(): void
    {
        parent::setup();
        Vendor::query()->delete();
    }

    #[Test]
    public function a_customer_cannot_create_a_vendor(): void
    {
        $this->actingAs(
            User::factory()->customer()->create()
        )
            ->post(route('admin.vendors.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_requires_a_title_to_create_a_vendor(): void
    {
        $this->assertDatabaseCount('vendors', 0);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.index'))
            ->assertOk();

        $this->post(route('admin.vendors.store'))
            ->assertRedirect(route('admin.vendors.index'))
            ->assertSessionHasErrors(['title' => 'The title field is required.']);
    }

    #[Test]
    public function the_email_field_must_be_valid_to_create_a_vendor(): void
    {
        $this->assertDatabaseCount('vendors', 0);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.index'))
            ->assertOk();

        $this->post(route('admin.vendors.store'), ['email' => 'not an email'])
            ->assertRedirect(route('admin.vendors.index'))
            ->assertSessionHasErrors(['email' => 'The email field must be a valid email address.']);
    }

    #[Test]
    public function the_website_field_must_be_formatted_properly_to_create_a_vendor(): void
    {
        $this->assertDatabaseCount('vendors', 0);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.index'))
            ->assertOk();

        $this->post(route('admin.vendors.store'), ['website' => 'www.test.com'])
            ->assertRedirect(route('admin.vendors.index'))
            ->assertSessionHasErrors(['website' => 'The website field must start with: http:// or https://']);
    }

    #[Test]
    public function vendor_creation_filters_out_unsupported_vendor_fields(): void
    {
        $this->assertDatabaseCount('vendors', 0);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.index'))
            ->assertOk();

        $response = $this->post(route('admin.vendors.store'), [
            'title' => 'some vendor',
            'some_other_field' => 'invalid'
        ]);

        $this->assertDatabaseCount('vendors', 1);
        $this->assertDatabaseHas('vendors', ['title' => 'some vendor']);

        $vendor = Vendor::latest()->first();

        $response->assertRedirect(route('admin.vendors.edit', ['vendor' => $vendor]));
    }

    #[Test]
    public function an_admin_can_create_a_vendor(): void
    {
        $this->assertDatabaseCount('vendors', 0);

        $vendor_attributes = [
            'title' => 'some vendor',
            'email' => '<EMAIL>',
            'website' => 'https://test.com'
        ];

        $response = $this->actingAsAdmin()
            ->post(route('admin.vendors.store'), $vendor_attributes);

        $this->assertDatabaseCount('vendors', 1);
        $this->assertDatabaseHas('vendors', $vendor_attributes);
        $vendor = Vendor::where($vendor_attributes)->first();

        $response->assertRedirect(route('admin.vendors.edit', ['vendor' => $vendor]));
    }

    #[Test]
    public function an_admin_can_create_a_vendor_with_just_a_title(): void
    {
        $vendor_attributes = ['title' => 'some vendor'];

        $response = $this->actingAsAdmin()
            ->post(route('admin.vendors.store'), $vendor_attributes)
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Vendor::class, $vendor_attributes);

        $vendor = Vendor::where($vendor_attributes)->first();

        $response->assertRedirect(route('admin.vendors.edit', ['vendor' => $vendor]));
    }

    #[Test]
    public function vendor_cache_is_flushed_when_saving_a_new_vendor(): void
    {
        Cache::shouldReceive('tags')->andReturnSelf()->shouldReceive('get')->shouldReceive('flush');

        $this->actingAsAdmin()
            ->post(route('admin.vendors.store'), ['title' => 'some vendor'])
            ->assertRedirect();
    }

    #[Test]
    public function vendor_cache_is_flushed_when_deleting_a_vendor(): void
    {
        $vendor = Vendor::factory()->create();

        Cache::shouldReceive('tags')->andReturnSelf()->shouldReceive('get')->shouldReceive('flush');

        $this->actingAsAdmin()
            ->delete(route('admin.vendors.destroy', ['vendor' => $vendor]))
            ->assertRedirect();
    }

    #[Test]
    public function it_can_delete_a_vendor_that_is_not_assigned_to_any_products(): void
    {
        $vendor = Vendor::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.vendors.edit', compact('vendor')))
            ->assertOk();

        $this->delete(route('admin.vendors.destroy', compact('vendor')))
            ->assertRedirect(route('admin.vendors.index'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseMissing(Vendor::class, [
            'id' => $vendor->id,
        ]);
    }

    #[Test]
    public function it_cannot_delete_a_vendor_that_has_active_products(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id]);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.edit', compact('vendor')))
            ->assertOk();

        $this->delete(route('admin.vendors.destroy', compact('vendor')))
            ->assertRedirect(route('admin.vendors.edit', compact('vendor')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'Please remove this vendor from products before deleting it.',
                'level' => 'error'
            ]]);

        $this->assertDatabaseHas(Vendor::class, [
            'id' => $vendor->id,
        ]);
    }

    #[Test]
    public function it_removes_assigned_vendor_from_archived_products_when_deleting_the_vendor(): void
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create(['vendor_id' => $vendor->id, 'deleted_at' => now()]);

        $this->actingAsAdmin()
            ->get(route('admin.vendors.edit', compact('vendor')))
            ->assertOk();

        $this->delete(route('admin.vendors.destroy', compact('vendor')))
            ->assertRedirect(route('admin.vendors.index'))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseMissing(Vendor::class, [
            'id' => $vendor->id,
        ]);

        $this->assertDatabaseHas(Product::class, [
            'id' => $product->id,
            'vendor_id' => null
        ]);
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $vendor = Vendor::factory()->create();

        $this->actingAsAdmin()->get(route('admin.vendors.show', compact('vendor')))
            ->assertRedirect(route('admin.vendors.edit', compact('vendor')));
    }
}
