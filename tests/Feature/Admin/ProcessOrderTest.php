<?php

namespace Tests\Feature\Admin;

use App\Actions\ProcessOrder;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Setting;
use Mockery;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Stripe\Exception\CardException;
use Tests\TenantTestCase;

class ProcessOrderTest extends TenantTestCase
{
    #[Test]
    public function a_guess_cannot_process_an_order(): void
    {
        $order = Order::factory()->create();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_process_an_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_process_an_order_when_customer_does_not_have_a_card_on_file_and_payment_method_is_not_card(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::factory()->create(['key' => 'other']);
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn([]);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has been processed.',
                'level' => 'info'
            ]]);
    }

    #[Test]
    public function an_admin_can_process_an_order_when_customer_does_not_have_a_card_on_file_and_order_is_already_paid(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => true]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn([]);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has been processed.',
                'level' => 'info'
            ]]);
    }

    #[Test]
    public function an_admin_can_process_an_order_when_customer_does_not_have_a_card_on_file_and_total_is_less_than_50(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $pickup = Pickup::factory()->create(['delivery_rate' => 0]);
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false, 'pickup_id' => $pickup->id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 49, 'qty' => 1]);

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn([]);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has been processed.',
                'level' => 'info'
            ]]);
    }

    #[Test]
    public function an_admin_cannot_process_an_order_when_customer_does_not_have_a_card_on_file_and_charge_is_part_of_processing(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $this->mock(ProcessOrder::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The customer does not have a card on file.',
                'level' => 'error'
            ]]);
    }

    #[Test]
    public function an_admin_cannot_process_an_order_that_has_already_been_processed(): void
    {
        $order = Order::factory()->create(['processed_date' => today()]);

        $this->mock(ProcessOrder::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has already been processed.',
                'level' => 'error'
            ]]);
    }

    #[Test]
    public function an_admin_can_process_an_order_when_customer_does_not_have_a_card_on_file_but_charge_is_not_part_of_processing(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => false]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $expected_result = [
            'payment' => null
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has been processed.',
                'level' => 'info'
            ]]);
    }

    #[Test]
    public function an_admin_can_process_an_order_when_customer_has_a_card_on_file_and_charge_is_part_of_processing(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        Card::factory()->create(['user_id' => $order->customer_id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $expected_result = [
            'payment' => []
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order,$expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'The order has been processed.',
                'level' => 'info'
            ]]);
    }

    #[Test]
    public function an_admin_cannot_process_an_order_when_an_exception_is_thrown(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        Card::factory()->create(['user_id' => $order->customer_id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $exception = CardException::factory('Some test error', 409);
        $expected_result = [
            'exception' => $exception
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->post(route('admin.orders.process', compact('order')))
            ->assertRedirect(route('admin.orders.edit', compact('order')))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'Some test error',
                'level' => 'error'
            ]]);
    }

    #[Test]
    public function ajax_an_admin_can_process_an_order_without_successful_payment(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => false]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $expected_result = [
            'payment' => null
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->postJson(route('admin.orders.process', compact('order')))
            ->assertOk()
            ->assertJson([
                'message' => 'The order has been processed.'
            ]);
    }

    #[Test]
    public function ajax_an_admin_can_process_an_order_with_successful_payment(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        Card::factory()->create(['user_id' => $order->customer_id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);
        $expected_order_payment = OrderPayment::factory()->create(['amount' => 12300]);

        $expected_result = [
            'payment' => $expected_order_payment
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->postJson(route('admin.orders.process', compact('order')))
            ->assertOk()
            ->assertJson([
                'message' => 'The order has been processed and charged the amount of 123.00.'
            ]);
    }

    #[Test]
    public function ajax_an_admin_cannot_process_an_order_when_an_exception_is_thrown(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => true]);

        $payment = Payment::where(['key' => 'card'])->first();
        $order = Order::factory()->create(['payment_id' => $payment->id, 'paid' => false]);
        Card::factory()->create(['user_id' => $order->customer_id]);
        OrderItem::factory()->create(['order_id' => $order->id, 'unit_price' => 10000]);

        $exception = CardException::factory('Some test error', 409);
        $expected_result = [
            'exception' => $exception
        ];

        $this->mock(ProcessOrder::class, function (MockInterface $mock) use ($order, $expected_result) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(Mockery::on(fn($arg) => $arg instanceof Order && $arg->id === $order->id))
                ->andReturn($expected_result);
        });

        $this->actingAsAdmin()
            ->get(route('admin.orders.edit', compact('order')))
            ->assertOk();

        $this->postJson(route('admin.orders.process', compact('order')))
            ->assertStatus(400)
            ->assertJson([
                'message' => 'Some test error'
            ]);
    }
}
