<?php

namespace Tests\Feature\Admin;

use App\Imports\ProductImport;
use App\Jobs\SyncSubscriptionReserveInventory;
use App\Models\Category;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Protocol;
use App\Models\Schedule;
use App\Models\Tag;
use App\Models\Vendor;
use App\Support\Enums\ProductType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Bus;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_view_products(): void
    {
        $this->get(route('admin.products.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function only_admins_can_view_products(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.products.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_view_an_unfiltered_list_of_products(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.products.index'))
            ->assertOk()
            ->assertViewIs('products.index')
            ->assertSessionHas('products-filtered', [])
            ->assertViewHas('savedFilters', new Collection())
            ->assertViewHas('appliedFilter', function ($value) { return is_null($value); })
            ->assertViewHas('appliedFilters', collect())
            ->assertViewHas('products');
    }

    #[Test]
    public function it_can_sort_products_by_attributes(): void
    {
        /** @var Product $product_one  */
        $product_one = Product::factory()->create([
            'title' => 'abc',
            'sku' => 'AB'
        ]);

        /** @var Product $product_two */
        $product_two = Product::factory()->create([
            'title' => 'xyz',
            'sku' => 'YZ'
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['orderBy' => 'title', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($product_one, $product_two) {
                $productIds = $arg->map(function (Product $product) {
                    return $product->id;
                });

                return $productIds->search($product_one->id) < $productIds->search($product_two->id);
            });

        $this->get(route('admin.products.index', ['orderBy' => 'title', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($product_one, $product_two) {
                $productIds = $arg->map(function (Product $product) {
                    return $product->id;
                });

                return $productIds->search($product_two->id) < $productIds->search($product_one->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['orderBy' => 'sku', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($product_one, $product_two) {
                $productIds = $arg->map(function (Product $product) {
                    return $product->id;
                });

                return $productIds->search($product_one->id) < $productIds->search($product_two->id);
            });

        $this->get(route('admin.products.index', ['orderBy' => 'sku', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($product_one, $product_two) {
                $productIds = $arg->map(function (Product $product) {
                    return $product->id;
                });

                return $productIds->search($product_two->id) < $productIds->search($product_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_asc_when_using_invalid_sort_attribute(): void
    {
        /** @var Product $product_one  */
        $product_one = Product::factory()->create([
            'title' => 'abc',
            'sku' => 'AB'
        ]);

        /** @var Product $product_two */
        $product_two = Product::factory()->create([
            'title' => 'xyz',
            'sku' => 'YZ'
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['orderBy' => 'title', 'sort' => 'abc']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($product_one, $product_two) {
                $productIds = $arg->map(fn(Product $product) => $product->id);

                return $productIds->search($product_one->id) < $productIds->search($product_two->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_title(): void
    {
        Product::factory()->create(['title' => 'abcde']);
        $expected = Product::factory()->create(['title' => 'vwxyz']);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['products' => 'wxy']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_sku(): void
    {
        Product::factory()->create(['sku' => 'abcde']);
        $expected = Product::factory()->create(['sku' => 'vwxyz']);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['products' => 'vwxyz']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });

        $this->get(route('admin.products.index', ['sku' => 'vwxyz']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_vendor(): void
    {
        Product::factory()->create();
        $vendor = Vendor::factory()->create();
        $expected = Product::factory()->create(['vendor_id' => $vendor->id]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['vendor_id' => $vendor->id]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_inventory_type(): void
    {
        Product::factory()->create(['inventory_type' => 1]);
        $expected = Product::factory()->create(['inventory_type' => 2]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['inventory_type' => [2]]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_unit_of_issue(): void
    {
        Product::factory()->create(['unit_of_issue' => 'package']);
        $expected = Product::factory()->create(['unit_of_issue' => 'weight']);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['unit_of_issue' => 'weight']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_track_inventory(): void
    {
        Product::factory()->create(['track_inventory' => 'yes']);
        $expected = Product::factory()->create(['track_inventory' => 'bundle']);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['track_inventory' => 'bundle']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_sale(): void
    {
        Product::factory()->create(['sale' => 0]);
        $expected = Product::factory()->create(['sale' => 1]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['sale' => 1]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_tag_slug(): void
    {
        Product::factory()->create();
        $tag = Tag::factory()->create();
        $expected = Product::factory()->create();
        $expected->tags()->attach($tag);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['tag' => $tag->slug]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_tag_ids(): void
    {
        Product::factory()->create();
        $tag_one = Tag::factory()->create();
        $tag_two = Tag::factory()->create();
        $expected = Product::factory()->create();
        $expected->tags()->attach($tag_one);
        $expected->tags()->attach($tag_two);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['tags' => [$tag_one->id, $tag_two->id]]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_protocol_slug(): void
    {
        Product::factory()->create();
        $protocol = Protocol::factory()->create();
        $expected = Product::factory()->create();
        $expected->protocols()->attach($protocol);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['protocol' => $protocol->slug]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_collection_id(): void
    {
        Product::factory()->create();
        $collection = \App\Models\Collection::factory()->create();
        $expected = Product::factory()->create();
        $expected->collections()->attach($collection);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['collection_id' => $collection->id]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_show_deleted(): void
    {
        $active = Product::factory()->create(['title' => 'a', 'deleted_at' => null]);
        $deleted = Product::factory()->create(['title' => 'b', 'deleted_at' => now()]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index'))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($active, $deleted) {
                return $arg->contains(fn (Product $product) => $product->id === $active->id)
                    && $arg->doesntContain(fn (Product $product) => $product->id === $deleted->id);
            });

        $this->get(route('admin.products.index', ['show_deleted' => 'true']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($active, $deleted) {
                return $arg->contains(fn (Product $product) => $product->id === $deleted->id)
                    && $arg->contains(fn (Product $product) => $product->id === $active->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_out_of_stock(): void
    {
        Product::factory()->create(['inventory' => 1]);
        $expected = Product::factory()->create(['inventory' => 0]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['out_of_stock' => 'true']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_visibility(): void
    {
        Product::factory()->create(['visible' => 1]);
        $expected = Product::factory()->create(['visible' => 0]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['product_visibility' => 0]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_accounting_class(): void
    {
        Product::factory()->create(['accounting_class' => 'abc']);
        $expected = Product::factory()->create(['accounting_class' => 'xyz']);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['accounting_class' => 'xyz']))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_is_bundle(): void
    {
        Product::factory()->create(['is_bundle' => 0]);
        $expected = Product::factory()->create(['is_bundle' => 1]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['is_bundle' => 1]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_admin_can_filter_products_by_taxable(): void
    {
        Product::factory()->create(['taxable' => 0]);
        $expected = Product::factory()->create(['taxable' => 1]);

        $this->actingAsAdmin()
            ->get(route('admin.products.index', ['taxable' => 1]))
            ->assertOk()
            ->assertViewHas('products', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Product $product) => $product->id === $expected->id);
            });
    }

    #[Test]
    public function an_unauthenticated_user_cannot_create_a_product(): void
    {
        $this->post(route('admin.products.store'), [
                'title' => 'Levi Cookies',
                'slug' => 'levi-cookies',
                'unit_price' => '5.55',
                'unit_of_issue' => 'weight',
                'weight' => '3.33'
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_non_admin_user_cannot_create_a_product(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.products.store'), [
                'title' => 'Levi Cookies',
                'slug' => 'levi-cookies',
                'unit_price' => '5.55',
                'unit_of_issue' => 'weight',
                'weight' => '3.33'
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_create_request(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.products.store'))
            ->assertInvalid(['title' => 'The title field is required.']);

        $this->post(route('admin.products.store'), [
                'unit_price' => '',
                'weight' => '',
            ])
            ->assertInvalid([
                'unit_price' => 'The price field is required.',
                'weight' => 'The weight field is required.',
            ]);

        $this->post(route('admin.products.store'), [
            'unit_price' => 'a',
            'weight' => 'a',
        ])
            ->assertInvalid([
                'unit_price' => 'The price field format is invalid.',
                'weight' => 'The weight field must be a number.',
            ]);

        $this->post(route('admin.products.store'), [
            'weight' => -1,
        ])
            ->assertInvalid([
                'weight' => 'The weight field must be at least 0.',
            ]);

        $this->post(route('admin.products.store'), [
            'type_id' => -1,
        ])
            ->assertInvalid([
                'type_id' => 'The selected type id is invalid.',
            ]);
    }

    #[Test]
    public function a_product_can_be_created(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.products.store'), [
                'title' => 'Levi Cookies',
                'unit_price' => '5.55',
                'unit_of_issue' => 'weight',
                'weight' => '3.33',
                'fulfillment_instructions' => 'Fulfillment Instructions',
            ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Levi Cookies',
            'unit_of_issue' => 'weight',
            'unit_price' => 555,
            'weight' => 3.33,
            'slug' => 'levi-cookies',
            'fulfillment_instructions' => 'Fulfillment Instructions',
        ]);
    }

    #[Test]
    public function a_product_of_a_specific_type_can_be_created(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.products.store'), [
                'type_id' => (string) ProductType::STANDARD->value,
                'track_inventory' => false,
                'title' => 'test 1',
                'unit_price' => '1.11',
                'unit_of_issue' => 'weight',
                'weight' => '1.11'
            ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::STANDARD->value,
            'track_inventory' => 'yes',
            'title' => 'test 1',
            'unit_of_issue' => 'weight',
            'unit_price' => 111,
            'weight' => 1.11,
            'slug' => 'test-1'
        ]);

        $this->post(route('admin.products.store'), [
                'type_id' => ProductType::GIFT_CARD->value,
                'track_inventory' => false,
                'title' => 'test 2',
                'unit_price' => '2.22',
                'unit_of_issue' => 'weight',
                'weight' => '2.22'
            ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::GIFT_CARD->value,
            'track_inventory' => 'yes',
            'title' => 'test 2',
            'unit_of_issue' => 'weight',
            'unit_price' => 222,
            'weight' => 2.22,
            'slug' => 'test-2'
        ]);

        $this->post(route('admin.products.store'), [
            'type_id' => (string) ProductType::BUNDLE->value,
            'track_inventory' => false,
            'is_bundle' => false,
            'title' => 'test 3',
            'unit_price' => '3.33',
            'unit_of_issue' => 'weight',
            'weight' => '3.33'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::STANDARD->value,
            'track_inventory' => 'bundle',
            'is_bundle' => 1,
            'title' => 'test 3',
            'slug' => 'test-3',
            'unit_price' => 333,
            'unit_of_issue' => 'weight',
            'weight' => '3.33'
        ]);

        $this->post(route('admin.products.store'), [
            'type_id' => (string) ProductType::PREORDER->value,
            'track_inventory' => 'bundle',
            'title' => 'test 4',
            'unit_price' => '4.44',
            'unit_of_issue' => 'weight',
            'weight' => '4.44'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::PREORDER->value,
            'track_inventory' => 'yes',
            'title' => 'test 4',
            'slug' => 'test-4',
            'unit_price' => 444,
            'unit_of_issue' => 'weight',
            'weight' => '4.44'
        ]);
    }

    #[Test]
    public function product_can_be_duplicated(): void
    {
        $originalProduct = Product::factory()->create([
            'title' => 'Steak',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345'
        ]);

        $response = $this->actingAsAdmin()
            ->post(route('admin.products.duplicate', [$originalProduct->id]), [
                'title' => 'Delicious Steak'
            ]);

        $this->assertDatabaseHas('products', [
            'title' => 'Delicious Steak',
        ]);

        $product = Product::where('title', 'Delicious Steak')->first();

        $this->assertEquals(700, $product->unit_price);
        $this->assertEquals(0.345, $product->weight);
        $this->assertEquals('package', $product->unit_of_issue);

        $response->assertRedirect('/admin/products/' . $product->id . '/edit?tab=description');
    }

    #[Test]
    public function a_guest_cannot_view_product_edit_page(): void
    {
        $product = Product::factory()->create();

        $this->get(route('admin.products.edit', compact('product')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function only_admins_can_view_product_edit_page(): void
    {
        $product = Product::factory()->create();

        $this->actingAsCustomer()
            ->get(route('admin.products.edit', compact('product')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function admin_can_view_product_edit_page(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk()
            ->assertViewIs('products.edit')
            ->assertViewHas('product', fn(Product $arg) => $arg->id === $product->id);
    }

    #[Test]
    public function it_redirects_product_edit_page_to_gift_card_edit_page_for_gift_card_products(): void
    {
        $gift_card = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', ['product' => $gift_card]))
            ->assertRedirect(route('admin.gift-cards.edit', compact('gift_card')));
    }

    #[Test]
    public function product_can_be_updated(): void
    {
        $product = Product::factory()->create([
            'title' => 'Steak',
            'slug' => 'steak',
            'summary' => 'Steak Summary',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345',
            'back_order' => 1,
        ]);

        $this->assertDatabaseHas('products', [
            'slug' => 'steak',
            'summary' => 'Steak Summary',
            'unit_of_issue' => 'package',
            'back_order' => 1,
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->put(route('admin.products.update', compact('product')), [
                'unit_of_issue' => 'weight',
                'summary' => 'New Steak Summary',
                'back_order' => 0,
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')));

        $this->assertDatabaseHas('products', [
            'slug' => 'steak',
            'unit_of_issue' => 'weight',
            'summary' => 'New Steak Summary',
            'back_order' => 0,
        ]);
    }

    #[Test]
    public function product_settings_tab_can_be_updated(): void
    {
        $vendor = Vendor::factory()->create();
        $schedule = Schedule::factory()->create();
        $category = Category::factory()->create();

        $existing_settings = ['existing' => 'value', 'order_minimum' => '100',];

        $product = Product::factory()->create([
            'title' => 'Steak',
            'slug' => 'steak',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345',
            'settings' => $existing_settings,
        ]);

        $settings_tab = [
            'visible' => 1,
            'taxable' => 1,
            'sku' => '',
            'barcode' => '',
            'category_id' => $category->id,
            'inventory_type' => 1,
            'notes' => '',
            'is_grouped' => 1,
            'vendor_id' => $vendor->id,
            'keywords' => '',
            'pickups' => 0,
            'custom_sort' => '',
            'accounting_class' => '',
            'hide_from_search' => 0,
            'slug' => 'by-weight',
            'fulfillment_id' => null,
            'schedule_id' => $schedule->id,
            'settings' => [
                'quantity_limit' => '',
                'order_minimum' => '',
                'sale_message' => 'Sale',
                'links_externally' => 0,
                'checkout_flow' => 'standard',
                'schedule_type' => 'estimate'
            ],
            'track_inventory' => 'yes',
            'is_bundle' => 0,
        ];

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), $settings_tab)
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'visible' => 1,
            'taxable' => 1,
            'sku' => null,
            'barcode' => null,
            'category_id' => $category->id,
            'inventory_type' => 1,
            'notes' => null,
            'is_grouped' => 1,
            'vendor_id' => $vendor->id,
            'keywords' => null,
            'custom_sort' => null,
            'accounting_class' => null,
            'hide_from_search' => 0,
            'slug' => 'by-weight',
            'fulfillment_id' => null,
            'schedule_id' => $schedule->id,
            'track_inventory' => 'yes',
            'is_bundle' => 0,
        ]);

        $product->refresh();

        // ensure settings append
        $this->assertEquals(json_decode(json_encode(array_merge($existing_settings, $settings_tab['settings']))), $product->settings);
    }

    #[Test]
    public function product_schedule_is_updated_when_fulfillment_id_is_updated(): void
    {
        $schedule = Schedule::factory()->create();
        $schedule_two = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        $product = Product::factory()->create(['schedule_id' => null, 'fulfillment_id' => null]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->put(route('admin.products.update', compact('product')), [
            'schedule_id' => $schedule_two->id,
            'fulfillment_id' => $pickup->id
        ])
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'schedule_id' => $schedule->id,
            'fulfillment_id' => $pickup->id
        ]);
    }

    #[Test]
    public function product_schedule_is_not_updated_when_fulfillment_location_does_not_have_a_valid_schedule(): void
    {
        $schedule_two = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => 0]);

        $product = Product::factory()->create(['schedule_id' => null, 'fulfillment_id' => null]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->put(route('admin.products.update', compact('product')), [
            'schedule_id' => $schedule_two->id,
            'fulfillment_id' => $pickup->id
        ])
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'schedule_id' => null,
            'fulfillment_id' => $pickup->id
        ]);
    }

    #[Test]
    public function product_is_soft_deleted(): void
    {
        $product = Product::factory()->create([
            'title' => 'Steak',
            'slug' => 'steak',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345'
        ]);

        $this->assertDatabaseHas('products', [
            'slug' => 'steak',
            'unit_of_issue' => 'package'
        ]);

        $response = $this->actingAsAdmin()
            ->delete('/admin/products/' . $product->id);

        $response->assertRedirect('/admin/products');

        $this->assertSoftDeleted('products', [
            'slug' => 'steak'
        ]);
    }

    #[Test]
    public function deleted_product_is_restored(): void
    {
        $product = Product::factory()->create([
            'title' => 'Steak',
            'slug' => 'steak',
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345'
        ]);

        $this->assertDatabaseHas('products', [
            'slug' => 'steak',
            'unit_of_issue' => 'package'
        ]);

        $this->actingAsAdmin()->delete('/admin/products/' . $product->id);

        $this->assertSoftDeleted('products', [
            'slug' => 'steak'
        ]);

        $response = $this->followingRedirects()->actingAsAdmin()
            ->post('/admin/products', [
                'title' => 'Steak',
                'unit_price' => '7.00',
                'unit_of_issue' => 'package',
                'weight' => '0.345'
            ]);

        $product = Product::where('slug', 'steak')->first();
        $this->assertNull($product->deleted_at);
    }

    #[Test]
    public function pricing_groups_can_be_auto_assigned_to_all_products_when_creating_new_pricing_group(): void
    {
        $product = Product::factory()->create([
            'title' => 'Steak',
            'slug' => 'steak',
            'unit_price' => '7.00',
            'sale_unit_price' => '5.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345'
        ]);

        $product2 = Product::factory()->create([
            'title' => 'Pork Chop',
            'slug' => 'pork-chop',
            'unit_price' => '4.00',
            'sale_unit_price' => '2.00',
            'unit_of_issue' => 'weight',
            'weight' => '0.345'
        ]);

        $response = $this->actingAsAdmin()
            ->post('/api/price-groups', [
                'title' => 'Wholesalex',
                'type' => 0,
                'auto_assign' => true
            ]);

        $this->assertDatabaseHas('product_price_groups', [
            'title' => 'Wholesalex'
        ]);

        $product->load('price');
        $product2->load('price');

        $this->assertDatabaseHas('product_prices', [
            'group_id' => $response->json()['id'],
            'product_id' => $product->id,
            'unit_price' => $product->unit_price,
            'sale_unit_price' => $product->sale_unit_price
        ]);
    }

    #[Test]
    public function new_product_created_has_the_appropriate_track_inventory_settings(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.products.store'), [
                'type_id' => ProductType::STANDARD->value,
                'title' => 'test 1',
                'unit_price' => '5.55',
                'unit_of_issue' => 'weight',
                'weight' => '3.33'
            ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::STANDARD->value,
            'track_inventory' => 'yes',
            'title' => 'test 1',
            'unit_of_issue' => 'weight',
            'unit_price' => 555,
            'weight' => 3.33,
            'slug' => 'test-1'
        ]);

        $this->post(route('admin.products.store'), [
            'type_id' => ProductType::BUNDLE->value,
            'title' => 'test 2',
            'unit_price' => '0.32',
            'unit_of_issue' => 'weight',
            'weight' => '3.33'
        ]);

        $this->assertDatabaseHas(Product::class, [
            'type_id' => ProductType::STANDARD->value,
            'track_inventory' => 'bundle',
            'title' => 'test 2',
            'unit_of_issue' => 'weight',
            'unit_price' => 32,
            'weight' => 3.33,
            'slug' => 'test-2'
        ]);
    }

    #[Test]
    public function when_bundle_product_cannot_allow_back_ordering(): void
    {
        $product = Product::factory()->create([
            'type_id' => ProductType::BUNDLE->value,
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345',
            'back_order' => false,
            'track_inventory' => 'no',
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), [
                'back_order' => true,
                'track_inventory' => 'yes',
                'is_bundle' => 1,
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'back_order' => false,
            'track_inventory' => 'bundle',
            'is_bundle' => 1,
        ]);

        $product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'unit_price' => '7.00',
            'unit_of_issue' => 'package',
            'weight' => '0.345',
            'back_order' => false,
            'track_inventory' => 'yes',
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), [
                'back_order' => true,
                'track_inventory' => 'yes',
                'is_bundle' => 0,
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'back_order' => true,
            'track_inventory' => 'yes',
            'is_bundle' => 0,
        ]);
    }

    #[Test]
    public function inventory_is_reset_to_0_when_invetory_is_not_tracked_or_tracked_on_bundle(): void
    {
        $product = Product::factory()->create([
            'track_inventory' => 'yes',
            'inventory' => 10,
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), [
                'track_inventory' => 'no',
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'track_inventory' => 'no',
            'inventory' => 0,
        ]);

        $product = Product::factory()->create([
            'type_id' => ProductType::BUNDLE->value,
            'track_inventory' => 'yes',
            'inventory' => 10,
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), [
                'track_inventory' => 'bundle',
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'track_inventory' => 'bundle',
            'inventory' => 0,
        ]);

        $product = Product::factory()->create([
            'track_inventory' => 'yes',
            'inventory' => 10,
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.products.edit', compact('product')))
            ->assertOk();

        $this->actingAsAdmin()
            ->put(route('admin.products.update', compact('product')), [
                'track_inventory' => 'yes',
            ])
            ->assertRedirect(route('admin.products.edit', compact('product')))
            ->assertSessionDoesntHaveErrors();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'track_inventory' => 'yes',
            'inventory' => 10,
        ]);
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $product = Product::factory()->create();

        $this->actingAsAdmin()->get(route('admin.products.show', compact('product')))
            ->assertRedirect(route('admin.products.edit', compact('product')));
    }

    #[Test]
    public function products_can_be_imported(): void
    {
        Excel::fake();

        $testFile = UploadedFile::fake()->create('products.csv');
        $this->actingAsAdmin()
            ->post(route('admin.products.import.store'), [
                'import_type' => 'not in the list',
                'products' => $testFile,
            ])->assertSessionHasErrors(['import_type' => 'The selected import type is invalid.']);

        $this->actingAsAdmin()
            ->post(route('admin.products.import.store'), [
                'import_type' => 'create',
                'products' => 'not a file',
            ])->assertSessionHasErrors(['products' => 'The products field must be a file.']);

        $this->actingAsAdmin()
            ->post(route('admin.products.import.store'), [
                'import_type' => 'create',
                'products' => $testFile,
            ])->assertSessionDoesntHaveErrors();

        $this->actingAsAdmin()
            ->post(route('admin.products.import.store'), [
                'import_type' => 'update_by_id',
                'products' => $testFile,
            ])->assertSessionDoesntHaveErrors();

        $this->actingAsAdmin()
            ->post(route('admin.products.import.store'), [
                'import_type' => 'update_by_sku',
                'products' => $testFile,
            ])->assertSessionDoesntHaveErrors();

        Excel::assertImported('products.csv', function(ProductImport $productImport) {
            return true;
        });
    }
    #[Test]
    public function it_fires_sync_subsricption_reserve_job(): void
    {
        Bus::fake(SyncSubscriptionReserveInventory::class);

        $this->actingAsAdmin()
            ->post(route('admin.products.sync-reserve-inventory.store'))
            ->assertRedirect(route('admin.products.index'))
            ->assertSessionHas(['flash_notification' => [
                'message' => 'Subscription reserve inventory sync started.',
                'level' => 'success'
            ]]);

        Bus::assertDispatched(SyncSubscriptionReserveInventory::class);
    }
}
