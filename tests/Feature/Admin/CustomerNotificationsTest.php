<?php

namespace Tests\Feature\Admin;

use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Events\User\UserUnsubscribedFromNewsletter;
use App\Events\User\UserUnsubscribedFromSmsMarketing;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomerNotificationsTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_view_customer_notifications(): void
    {
        $this->get(route('customer.notifications'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_user_can_view_customer_notifications(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('customer.notifications'))
            ->assertOk()
            ->assertViewIs('theme::customers.notifications')
            ->assertViewHas('customer', function ($value) use ($user) {
                return $value instanceof User
                    && $value->id === $user->id;
            });
    }

    #[Test]
    public function a_guest_cannot_update_customer_notifications(): void
    {
        $this->put(route('customer.notifications.update'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_validates_update_request(): void
    {
        $this->actingAsCustomer()
            ->put(route('customer.notifications.update'), [
            'order_deadline_email_reminder' => 'a',
            'newsletter' => 'a',
            'sms_marketing' => 'a',
        ])
            ->assertSessionHasErrors(['order_deadline_email_reminder' => 'The order deadline email reminder field must be true or false.'])
            ->assertSessionHasErrors(['newsletter' => 'The newsletter field must be true or false.'])
            ->assertSessionHasErrors(['sms_marketing' => 'The sms marketing field must be true or false.']);
    }

    #[Test]
    public function a_user_can_subscribe_to_all_customer_notifications(): void
    {
        Carbon::setTestNow(now());

        $user = User::factory()->create([
            'order_deadline_email_reminder' => 0,
            'newsletter' => 0,
            'subscribed_to_sms_marketing_at' => null,
        ]);

        $this->actingAs($user)
            ->get(route('customer.notifications'))
            ->assertOk();

        $this->put(route('customer.notifications.update'), [
                'order_deadline_email_reminder' => 1,
                'newsletter' => 1,
                'sms_marketing' => 1,
            ])
            ->assertSessionDoesntHaveErrors()
            ->assertRedirect(route('customer.notifications'));

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'order_deadline_email_reminder' => 1,
            'newsletter' => 1,
            'subscribed_to_sms_marketing_at' => now(),
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_doesnt_fire_newsletter_event_when_newsletter_subscription_has_not_changed(): void
    {
        Event::fake([
            UserSubscribedToNewsletter::class,
            UserUnsubscribedFromNewsletter::class
        ]);

        $user = User::factory()->create(['newsletter' => 1]);

        $this->put(route('customer.notifications.update'), [
            'newsletter' => 1
        ])
            ->assertSessionDoesntHaveErrors();

        Event::assertNotDispatched(UserSubscribedToNewsletter::class);
        Event::assertNotDispatched(UserUnsubscribedFromNewsletter::class);
    }

    #[Test]
    public function it_fire_newsletter_event_when_user_unsubscribes_from_newsletter(): void
    {
        Event::fake([
            UserSubscribedToNewsletter::class,
            UserUnsubscribedFromNewsletter::class
        ]);

        $user = User::factory()->create(['newsletter' => 1]);

        $this->actingAs($user)
            ->put(route('customer.notifications.update'), [
                'newsletter' => 0
            ])
            ->assertSessionDoesntHaveErrors();

        Event::assertNotDispatched(UserSubscribedToNewsletter::class);
        Event::assertDispatched(UserUnsubscribedFromNewsletter::class);
    }

    #[Test]
    public function it_fire_newsletter_event_when_user_subscribes_to_newsletter(): void
    {
        Event::fake([
            UserSubscribedToNewsletter::class,
            UserUnsubscribedFromNewsletter::class
        ]);

        $user = User::factory()->create(['newsletter' => 0]);

        $this->actingAs($user)
            ->put(route('customer.notifications.update'), [
                'newsletter' => 1
            ])
            ->assertSessionDoesntHaveErrors();

        Event::assertDispatched(UserSubscribedToNewsletter::class);
        Event::assertNotDispatched(UserUnsubscribedFromNewsletter::class);
    }

    #[Test]
    public function it_fire_sms_marketing_event_when_user_unsubscribes_from_sms_marketing(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class, UserUnsubscribedFromSmsMarketing::class]);

        $user = User::factory()->create(['subscribed_to_sms_marketing_at' => now()]);

        $this->actingAs($user)
            ->put(route('customer.notifications.update'), [
                'sms_marketing' => 0
            ])
            ->assertSessionDoesntHaveErrors();

        Event::assertNotDispatched(UserSubscribedToSmsMarketing::class);
        Event::assertDispatched(UserUnsubscribedFromSmsMarketing::class);
    }

    #[Test]
    public function it_fire_sms_marketing_event_when_user_subscribes_to_sms_marketing(): void
    {
        Event::fake([UserSubscribedToSmsMarketing::class, UserUnsubscribedFromSmsMarketing::class]);

        $user = User::factory()->create(['subscribed_to_sms_marketing_at' => null]);

        $this->actingAs($user)
            ->put(route('customer.notifications.update'), [
                'sms_marketing' => 1
            ])
            ->assertSessionDoesntHaveErrors();

        Event::assertDispatched(UserSubscribedToSmsMarketing::class);
        Event::assertNotDispatched(UserUnsubscribedFromSmsMarketing::class);
    }
}