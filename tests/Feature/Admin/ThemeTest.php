<?php

namespace Tests\Feature\Admin;

use App\Models\Page;
use Tests\TenantTestCase;

class ThemeTest extends TenantTestCase
{
    public function test_guest_can_not_view_hidden_page(): void
    {
        $page = Page::create([
            'title' => 'How It Works',
            'visible' => true
        ]);

        $response = $this->get('/how-it-works');
        $response->assertStatus(200);

        $page->visible = false;
        $page->save();

        $response = $this->get('/how-it-works');
        $response->assertStatus(404);
    }
}
