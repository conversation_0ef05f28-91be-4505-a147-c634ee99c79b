<?php

namespace Tests\Feature\Admin;

use App\Models\Post;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PostTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user_to_view_posts(): void
    {
        $this->get(route('admin.posts.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function only_an_admin_can_view_posts(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.posts.index'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_shows_a_list_of_posts(): void
    {
        Post::factory()->times(2)->create();

        $this->actingAsAdmin()
            ->get(route('admin.posts.index'))
            ->assertOk()
            ->assertViewIs('posts.index')
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) {
                return $arg->count() === 2;
            });
    }

    #[Test]
    public function it_can_filter_posts_by_title(): void
    {
        Post::factory()->create(['title' => 'abcde']);
        $expected = Post::factory()->create(['title' => 'vwxyz']);

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['posts' => 'wxy']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Post $post) => $post->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_filter_posts_by_author(): void
    {
        $user_one = User::factory()->create();
        Post::factory()->create(['user_id' => $user_one->id]);

        $user_two = User::factory()->create();
        $expected = Post::factory()->create(['user_id' => $user_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['user_id' => $user_two->id]))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Post $post) => $post->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_filter_posts_by_published_date(): void
    {
        Post::factory()->create(['published_at' => now()->copy()->subDays(5)]);

        /** @var Post $expected */
        $expected = Post::factory()->create(['published_at' => now()->copy()->addDays(5)]);

        $date_string = "{$expected->published_at->copy()->subDay()->format('Y-m-d')} - {$expected->published_at->copy()->addDay()->format('Y-m-d')}";

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['published_date' => $date_string]))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($expected) {
                return $arg->contains(fn (Post $post) => $post->id === $expected->id);
            });
    }

    #[Test]
    public function it_can_sort_posts_by_attributes(): void
    {
        /** @var Post $post_one  */
        $post_one = Post::factory()->create([
            'title' => 'abc',
            'published_at' => now()->subDays(5)
        ]);

        /** @var Post $post_two */
        $post_two = Post::factory()->create([
            'title' => 'xyz',
            'published_at' => now()->addDays(5)
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['orderBy' => 'title', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($post_one, $post_two) {
                $postIds = $arg->map(function (Post $post) {
                    return $post->id;
                });

                return $postIds->search($post_one->id) < $postIds->search($post_two->id);
            });

        $this->get(route('admin.posts.index', ['orderBy' => 'title', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($post_one, $post_two) {
                $postIds = $arg->map(function (Post $post) {
                    return $post->id;
                });

                return $postIds->search($post_two->id) < $postIds->search($post_one->id);
            });

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['orderBy' => 'published_at', 'sort' => 'asc']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($post_one, $post_two) {
                $postIds = $arg->map(function (Post $post) {
                    return $post->id;
                });

                return $postIds->search($post_one->id) < $postIds->search($post_two->id);
            });

        $this->get(route('admin.posts.index', ['orderBy' => 'published_at', 'sort' => 'desc']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($post_one, $post_two) {
                $postIds = $arg->map(function (Post $post) {
                    return $post->id;
                });

                return $postIds->search($post_two->id) < $postIds->search($post_one->id);
            });
    }

    #[Test]
    public function it_sorts_by_asc_when_using_invalid_sort_attribute(): void
    {
        /** @var Post $post_one  */
        $post_one = Post::factory()->create([
            'title' => 'abc',
            'published_at' => now()->subDays(5)
        ]);

        /** @var Post $post_two */
        $post_two = Post::factory()->create([
            'title' => 'xyz',
            'published_at' => now()->addDays(5)
        ]);

        $this->actingAsAdmin()
            ->get(route('admin.posts.index', ['orderBy' => 'title', 'sort' => 'abc']))
            ->assertOk()
            ->assertViewHas('posts', function (LengthAwarePaginator $arg) use ($post_one, $post_two) {
                $postIds = $arg->map(fn(Post $post) => $post->id);

                return $postIds->search($post_one->id) < $postIds->search($post_two->id);
            });
    }

    #[Test]
    public function an_unauthenticated_user_cannot_create_a_post(): void
    {
        $this->post(route('admin.posts.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_create_a_post(): void
    {
        $this->actingAsCustomer()
            ->post(route('admin.posts.store'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_create_request(): void
    {
        $this->actingAsAdmin()
            ->post(route('admin.posts.store'))
            ->assertInvalid(['title' => 'The title field is required.']);

        $this->post(route('admin.posts.store'), [
            'title' => Str::random(256)
        ]) ->assertInvalid(['title' => 'The title field must not be greater than 255 characters.']);

        $this->post(route('admin.posts.store'), ['user_id' => 0])
            ->assertInvalid(['user_id' => 'The selected user id is invalid.']);
    }

    #[Test]
    public function an_admin_can_create_an_anonymous_post(): void
    {
        Carbon::setTestNow(now());

        $response = $this->actingAsAdmin()
            ->post(route('admin.posts.store'), [
                'title' => 'My test post'
            ])
            ->assertSessionHasNoErrors();

        $expected = [
            'title' => 'My test post',
            'published_at' => now()->format('Y-m-d H:i:s'),
            'published_year' => now()->year,
            'published_month' => now()->month,
            'published_day' => now()->day,
        ];

        $this->assertDatabaseHas(Post::class, $expected);

        $post = Post::where($expected)->first();

        $response->assertRedirect(route('admin.posts.edit', $post->slug));

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_create_an_authored_post(): void
    {
        Carbon::setTestNow(now());

        $author = User::factory()->create();

        $response = $this->actingAsAdmin()
            ->post(route('admin.posts.store'), [
                'title' => 'My test post',
                'user_id' => $author->id
            ])
            ->assertSessionHasNoErrors();

        $expected = [
            'title' => 'My test post',
            'user_id' => $author->id,
            'published_at' => now()->format('Y-m-d H:i:s'),
            'published_year' => now()->year,
            'published_month' => now()->month,
            'published_day' => now()->day,
        ];

        $this->assertDatabaseHas(Post::class, $expected);

        $post = Post::where($expected)->first();

        $response->assertRedirect(route('admin.posts.edit', $post->slug));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_validates_the_update_request(): void
    {
        $post = Post::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.posts.update', compact('post')), ['title' => null])
            ->assertInvalid([
                'title' => 'The title field is required.',
                'published_at' => 'The published at field is required.'
            ]);

        $this->put(route('admin.posts.update', compact('post')), ['title' => Str::random(256), 'published_at' => now()])
            ->assertInvalid(['title' => 'The title field must not be greater than 255 characters.']);

        $this->put(route('admin.posts.update', compact('post')), ['title' => 'Hello world', 'published_at' => null])
            ->assertInvalid(['published_at' => 'The published at field is required.']);
    }
}