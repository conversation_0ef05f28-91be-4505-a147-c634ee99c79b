<?php

namespace Tests\Feature\Reports;

use App\Exports\SubscriptionInventoryExport;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionInventoryReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_fetch_the_subscription_inventory_report(): void
    {
        $this->get(route('admin.reports.subscription-inventory'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_fetch_the_subscription_inventory_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.subscription-inventory'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_the_subscription_inventory_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.subscription-inventory'))
            ->assertOk()
            ->assertViewIs('reports.subscription-inventory.index')
            ->assertViewHas([
                'savedFilters',
                'appliedFilters',
                'appliedFilter',
                'report',
            ]);
    }

    #[Test]
    public function an_admin_can_fetch_the_subscription_inventory_report_as_an_export(): void
    {
        Excel::fake();

        $this->actingAsAdmin()
            ->get(route('admin.reports.subscription-inventory', [
                'export' => 1,
            ]))
            ->assertOk();

        Excel::assertDownloaded('subscription_inventory_report.csv', fn(SubscriptionInventoryExport $export) => true);
    }
}