<?php

namespace Tests\Feature\API;

use App\Models\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CollectionTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_collections(): void
    {
        $this->getJson(route('api.collections.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_collections(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.collections.index'))
            ->assertForbidden();
    }

    #[Test]
    public function admins_can_fetch_collections(): void
    {
        $collections = Collection::factory()->times(2)->create();

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.collections.index'))
            ->assertOk();

        foreach ($collections as $collection) {
            $response->assertJsonFragment(['id' => $collection->id]);
        }
    }
}