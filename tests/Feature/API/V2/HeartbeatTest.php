<?php

namespace Tests\Feature\API\V2;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class HeartbeatTest extends TenantTestCase
{
    #[Test]
    public function it_requires_bearer_token_to_make_api_request(): void
    {
        $this->getJson(route('api.v2.heartbeat.show'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_returns_a_heartbeat_response(): void
    {
        $this->actingAsSanctumUser()
            ->getJson(route('api.v2.heartbeat.show'))
            ->assertOk()
            ->assertExactJson([
                'success' => true,
                'status' => 200
            ]);
    }
}


