<?php

namespace Tests\Feature\API\V2;

use App\Models\User;
use App\Support\Enums\UserRole;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CurrentUserTest extends TenantTestCase
{
    #[Test]
    public function unauthorized_requests_cannot_fetch_the_current_user(): void
    {
        $this->getJson(route('api.v2.users.current.show'))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_can_return_the_current_user(): void
    {
        $user = User::factory()->create();

        $this->actingAsSanctumUser($user)
            ->get<PERSON>son(route('api.v2.users.current.show'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'role_id' => $user->role_id,
                    'role' => UserRole::get($user->role_id),
                ],
            ]);
    }
}
