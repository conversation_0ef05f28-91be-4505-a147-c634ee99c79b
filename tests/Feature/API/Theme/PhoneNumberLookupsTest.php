<?php

namespace Tests\Feature\API\Theme;

use App\Services\PhoneNumberService;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PhoneNumberLookupsTest extends TenantTestCase
{
    #[Test]
    public function a_guest_cannot_perform_a_lookup(): void
    {
        $this->mock(PhoneNumberService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('lookup');
        });

        $this->postJson(route('api.theme.phone-number-lookups.store'), [
            'phone_number' => '************'
        ])
            ->assertUnauthorized();
    }

    #[Test]
    public function it_validates_the_lookup_request(): void
    {
        $this->mock(PhoneNumberService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('lookup');
        });

        $this->actingAsApiCustomer()
            ->postJson(route('api.theme.phone-number-lookups.store'))
            ->assertInvalid([
                'phone_number' => 'The phone number field is required.'
            ]);
    }

    #[Test]
    public function an_authenticated_user_can_perform_a_lookup(): void
    {
        $response = ['valid' => true, 'type' => 'mobile'];

        $this->mock(
            PhoneNumberService::class,
            fn (MockInterface $mock) => $mock->shouldReceive('lookup')
                ->andReturn($response)
        );

        $this->actingAsApiCustomer()
            ->postJson(route('api.theme.phone-number-lookups.store'), [
                'phone_number' => '************'
            ])
            ->assertOk()
            ->assertExactJson($response);
    }
}
