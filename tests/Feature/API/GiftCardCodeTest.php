<?php

namespace Tests\Feature\API;

use App\Mail\GiftCardIssued;
use App\Models\GiftCertificate;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Support\Enums\ProductType;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GiftCardCodeTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_generate_gift_card_codes(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_generate_gift_card_codes(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->actingAsApiCustomer()
            ->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertForbidden();
    }

    #[Test]
    public function it_validates_the_request(): void
    {
        $this->actingAsApiAdmin()
            ->postJson(route('api.gift-card-codes.store'))
            ->assertUnprocessable()
            ->assertInvalid(['order_item_id' => 'The order item id field is required.']);

        $this->postJson(route('api.gift-card-codes.store'), ['order_item_id' => 12387123])
            ->assertUnprocessable()
            ->assertInvalid(['order_item_id' => 'The selected order item id is invalid.']);
    }

    #[Test]
    public function admins_can_generate_gift_card_codes(): void
    {
        Mail::fake();

        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->actingAsApiAdmin()
            ->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertCreated()
            ->assertJsonStructure([
                'code', 'amount', 'balance'
            ]);

        Mail::assertQueued(GiftCardIssued::class);
    }

    #[Test]
    public function admins_cannot_generate_gift_card_codes_on_canceled_orders(): void
    {
        $order = Order::factory()->create(['canceled' => true]);
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->actingAsApiAdmin()
            ->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertStatus(409);
    }

    #[Test]
    public function admins_cannot_generate_gift_card_codes_for_non_gift_card_products(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::STANDARD->value]);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id]);

        $this->actingAsApiAdmin()
            ->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertStatus(409);
    }

    #[Test]
    public function admins_cannot_generate_gift_card_codes_for_items_already_tied_to_gift_card(): void
    {
        $product = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value]);
        $order_item = OrderItem::factory()->create(['product_id' => $product->id]);
        GiftCertificate::factory()->create(['order_item_id' => $order_item->id]);

        $this->actingAsApiAdmin()
            ->postJson(route('api.gift-card-codes.store'), ['order_item_id' => $order_item->id])
            ->assertStatus(409);
    }
}
