<?php

namespace Tests\Feature\API;

use App\Models\Event;
use App\Models\Order;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderEventTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_order_events(): void
    {
        $order = Order::factory()->create();

        $this->getJson(route('api.order-events.index', compact('order')))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_order_events(): void
    {
        $order = Order::factory()->create();

        $this->actingAsApiCustomer()
            ->getJson(route('api.order-events.index', compact('order')))
            ->assertForbidden();
    }

    #[Test]
    public function admins_cannot_fetch_events_for_invalid_order(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.order-events.index', [123456789]))
            ->assertNotFound();
    }

    #[Test]
    public function admins_can_fetch_order_events(): void
    {
        $order = Order::factory()->create();

        $events = $order->events()->saveMany(
            Event::factory()->times(2)->make()
        );

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.order-events.index', compact('order')))
            ->assertOk();

        foreach ($events as $index => $event) {
            $response->assertJsonPath("{$index}.id", $event->id);
            $response->assertJsonPath("{$index}.user.email", $event->user->email);
            $response->assertJsonPath("{$index}.user.first_name", $event->user->first_name);
            $response->assertJsonPath("{$index}.user.last_name", $event->user->last_name);
            $response->assertJsonPath("{$index}.user.role", $event->user->role);
            $response->assertJsonPath("{$index}.user.role_id", $event->user->role_id);
        }
    }

    #[Test]
    public function admins_can_fetch_order_events_in_created_descending_order(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create();

        $first_event = $order->events()->save(
            Event::factory()->make(['created_at' => now()])
        );

        $second_event = $order->events()->save(
            Event::factory()->make(['created_at' => now()->subSecond()])
        );

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.order-events.index', compact('order')))
            ->assertOk()
            ->json();

        $first_event_index = collect($response)->search(fn ($result) => $result['id'] === $first_event->id);
        $second_event_index = collect($response)->search(fn ($result) => $result['id'] === $second_event->id);

        $this->assertNotFalse($first_event_index);
        $this->assertNotFalse($second_event_index);
        $this->assertTrue($first_event_index < $second_event_index); // first is higher in list than second

        Carbon::setTestNow();
    }
}