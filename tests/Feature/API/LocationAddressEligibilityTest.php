<?php

namespace Tests\Feature\API;

use App\Models\Pickup;
use App\Models\PickupState;
use App\Models\PickupZip;
use App\Models\Setting;
use App\Services\SettingsService;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class LocationAddressEligibilityTest extends TenantTestCase
{
    #[Test]
    public function it_returns_eligible_when_location_is_pickup(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 1]);

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertOk()
            ->assertExactJson(['eligible' => true]);
    }

    #[Test]
    public function it_returns_eligible_when_zip_is_available_for_a_location(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 2]);

        PickupZip::factory()->create(['pickup_id' => $location->id, 'zip' => '12345']);

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertOk()
            ->assertExactJson(['eligible' => true]);
    }

    #[Test]
    public function it_returns_eligible_when_state_is_available_for_a_location(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 2]);

        PickupState::factory()->create(['pickup_id' => $location->id, 'state' => 'AB']);

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertOk()
            ->assertExactJson(['eligible' => true]);
    }

    #[Test]
    public function it_returns_ineligible_when_both_zip_and_state_are_not_available_for_a_location(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 2]);

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertOk()
            ->assertExactJson([
                'eligible' => false,
                'ineligible_reason' => 'OUTSIDE_OF_ZONE',
            ]);
    }

    #[Test]
    public function it_validates_street_is_not_po_box_when_disabled(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 1]);

        $this->mock(SettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('all')->andReturn([]);
            $mock->shouldReceive('allowsPOBoxShipping')->andReturn(false);
        });

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'street' => 'P.O. Box 123',
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertUnprocessable()
            ->assertJsonFragment(['address.street' => ['Shipping to PO Box addresses is not allowed.']]);
    }

    #[Test]
    public function it_validates_street_2_is_not_po_box_when_disabled(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 1]);

        $this->mock(SettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('all')->andReturn([]);
            $mock->shouldReceive('allowsPOBoxShipping')->andReturn(false);
        });

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'street_2' => 'P.O. Box 123',
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertUnprocessable()
            ->assertJsonFragment(['address.street_2' => ['Shipping to PO Box addresses is not allowed.']]);
    }

    #[Test]
    public function it_allows_po_box_when_enabled(): void
    {
        $location = Pickup::factory()->create(['fulfillment_type' => 1]);

        Setting::updateOrCreate(['key' => 'ship_to_pobox'], ['value' => 1]);

        $this->actingAsApiCustomer()
            ->postJson(route('api.locations.address-eligibility', compact('location')), [
                'address' => [
                    'street' => 'P.O. Box 123',
                    'street_2' => 'P.O. Box 123',
                    'state' => 'AB',
                    'zip' => '12345',
                ]
            ])
            ->assertOk()
            ->assertExactJson(['eligible' => true]);
    }
}
