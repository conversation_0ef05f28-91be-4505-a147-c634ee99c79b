<?php

namespace Tests\Feature\API;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\User;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DashboardTest extends TenantTestCase
{
    #[Test]
    public function unauthenticated_users_cannot_fetch_dashboard(): void
    {
        $this->getJson(route('api.dashboard.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function non_admin_users_cannot_fetch_dashboard(): void
    {
        $this->actingAsApiCustomer()
            ->getJson(route('api.dashboard.index'))
            ->assertForbidden();
    }

    #[Test]
    public function it_validate_the_dashboard_request(): void
    {
        $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index'))
            ->assertUnprocessable()
            ->assertInvalid([
                'dateRange' => 'The date range field is required.',
                'chart' => 'The chart field is required.'
            ]);

        $this->getJson(route('api.dashboard.index', [
            'dateRange' => 'something',
            'chart' => 'something',
        ]))
            ->assertUnprocessable()
            ->assertInvalid([
                'dateRange' => 'The selected date range is invalid.',
                'chart' => 'The selected chart is invalid.'
            ]);
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_last_7_days(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->subDays(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->subDays(9), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'last_7_days',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
                'date_range'
            ])
            ->json();

        $this->assertCount(7, $response['labels']);
        $this->assertCount(7, $response['chartData']);
        $this->assertCount(1, $response['all_data']);
        $this->assertCount(2, $response['date_range']);

        foreach (CarbonPeriod::between(now()->subDays(6), now()) as $index => $date) {
            $this->assertTrue(in_array($date->format('D M jS'), $response['labels']));

            if ($index === 4) {
                $this->assertEquals('12.34', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        $this->assertEquals(now()->subDays(6)->format('Y-m-d'), $response['date_range'][0]);
        $this->assertEquals(now()->format('Y-m-d'), $response['date_range'][1]);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_last_30_days(): void
    {
        Carbon::setTestNow(now());

        $o1 = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->subDays(2), 'total' => 1234]);
        $o2 = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->subDays(35), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'last_30_days',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
                'date_range'
            ])
            ->json();

        $this->assertCount(30, $response['labels']);
        $this->assertCount(30, $response['chartData']);
        $this->assertCount(1, $response['all_data']);
        $this->assertCount(2, $response['date_range']);

        foreach (CarbonPeriod::between(now()->subDays(29), now()) as $index => $date) {
            $this->assertTrue(in_array($date->format('n/j'), $response['labels']));

            if ($index === 27) {
                $this->assertEquals('12.34', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        $this->assertEquals(now()->subDays(29)->format('Y-m-d'), $response['date_range'][0]);
        $this->assertEquals(now()->format('Y-m-d'), $response['date_range'][1]);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_the_week(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'week',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
                'date_range'
            ])
            ->json();

        $this->assertCount(7, $response['labels']);
        $this->assertCount(7, $response['chartData']);
        $this->assertCount(1, $response['all_data']);
        $this->assertCount(2, $response['date_range']);

        foreach (CarbonPeriod::between(now()->startOfWeek(), now()->endOfWeek()) as $index => $date) {
            $this->assertTrue(in_array($date->format('D M jS'), $response['labels']));

            if ($index === 2) {
                $this->assertEquals('12.34', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        $this->assertEquals(now()->startOfWeek()->format('Y-m-d'), $response['date_range'][0]);
        $this->assertEquals(now()->endOfWeek()->format('Y-m-d'), $response['date_range'][1]);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_last_week(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastWeek',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
                'date_range'
            ])
            ->json();

        $this->assertCount(7, $response['labels']);
        $this->assertCount(7, $response['chartData']);
        $this->assertCount(1, $response['all_data']);
        $this->assertCount(2, $response['date_range']);

        foreach (CarbonPeriod::between(now()->startOfWeek()->subDays(2)->startOfWeek(), now()->startOfWeek()->subDays(2)->endOfWeek()) as $index => $date) {
            $this->assertTrue(in_array($date->format('D M jS'), $response['labels']));

            if ($index === 5) {
                $this->assertEquals('9.80', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        $this->assertEquals(now()->startOfWeek()->subDays(2)->startOfWeek()->format('Y-m-d'), $response['date_range'][0]);
        $this->assertEquals(now()->startOfWeek()->subDays(2)->endOfWeek()->format('Y-m-d'), $response['date_range'][1]);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_the_month(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'month',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
            ])
            ->json();

        $this->assertCount(now()->startOfMonth()->daysInMonth, $response['labels']);
        $this->assertCount(now()->startOfMonth()->daysInMonth, $response['chartData']);
        $this->assertCount(1, $response['all_data']);

        foreach (CarbonPeriod::between(now()->startOfMonth(), now()->endOfMonth()) as $index => $date) {
            $this->assertTrue(in_array($date->format('n/j'), $response['labels']));

            if ($index === 2) {
                $this->assertEquals('12.34', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_last_month(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastMonth',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
            ])
            ->json();

        $this->assertCount(now()->startOfMonth()->subDays(2)->daysInMonth, $response['labels']);
        $this->assertCount(now()->startOfMonth()->subDays(2)->daysInMonth, $response['chartData']);
        $this->assertCount(1, $response['all_data']);

        foreach (CarbonPeriod::between(now()->startOfMonth()->subDays(2)->startOfMonth(), now()->startOfMonth()->subDays(2)->endOfMonth()) as $index => $date) {
            $this->assertTrue(in_array($date->format('n/j'), $response['labels']));

            if ($index === now()->startOfMonth()->subDays(2)->daysInMonth - 2) {
                $this->assertEquals('9.80', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_the_year(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear(), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(1), 'total' => 2234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(2), 'total' => 3234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(3), 'total' => 4234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(4), 'total' => 5234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(5), 'total' => 6234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(6), 'total' => 7234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(7), 'total' => 8234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(8), 'total' => 9234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(9), 'total' => 10234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(10), 'total' => 11234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(11), 'total' => 12234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subMonths(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'year',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
            ])
            ->json();

        $this->assertCount(12, $response['labels']);
        $this->assertCount(12, $response['chartData']);
        $this->assertCount(12, $response['all_data']);

        foreach (new CarbonPeriod(now()->startOfYear(), '1 month', now()->endOfYear()) as $index => $month) {
            $this->assertTrue(in_array($month->format('m/Y'), $response['labels']));

            $expected = (($index * 1000) + 1234) / 100;
            $this->assertEquals((string) $expected, $response['chartData'][$index]);
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_revenue_trend_dashboard_for_last_year(): void
    {
        Carbon::setTestNow(now());

        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addMonths(2), 'total' => 1234]);
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subMonths(2), 'total' => 980]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastYear',
                'chart' => 'revenueTrends',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'labels',
                'chartData',
                'all_data',
            ])
            ->json();

        $this->assertCount(12, $response['labels']);
        $this->assertCount(12, $response['chartData']);
        $this->assertCount(1, $response['all_data']);

        foreach (new CarbonPeriod(now()->startOfYear()->subMonths(2)->startOfYear(), '1 month', now()->startOfYear()->subMonths(2)->endOfYear()) as $index => $month) {
            $this->assertTrue(in_array($month->format('m/Y'), $response['labels']));

            if ($index === 10) {
                $this->assertEquals('9.80', $response['chartData'][$index]);
            } else {
                $this->assertEquals(0, $response['chartData'][$index]);
            }
        }

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_the_week(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'week',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_last_week(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastWeek',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_the_month(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'month',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_last_month(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastMonth',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_the_year(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'year',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_location_dashboard_for_last_year(): void
    {
        Carbon::setTestNow(now());

        $location_one = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 1234, 'pickup_id' => $location_one->id]);
        $location_two = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 2234, 'pickup_id' => $location_two->id]);
        $location_three = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 3234, 'pickup_id' => $location_three->id]);
        $location_four = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 4234, 'pickup_id' => $location_four->id]);
        $location_five = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 5234, 'pickup_id' => $location_five->id]);
        $location_six = Pickup::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 6234, 'pickup_id' => $location_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastYear',
                'chart' => 'topLocations',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'pickup_id', 'revenue', 'title', 'fulfillment_type', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($location_six->id, $response[0]['pickup_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($location_six->title, $response[0]['title']);
        $this->assertEquals($location_six->fulfillment_type, $response[0]['fulfillment_type']);
        $this->assertEquals($location_six->id, $response[0]['id']);

        $this->assertEquals($location_five->id, $response[1]['pickup_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($location_five->title, $response[1]['title']);
        $this->assertEquals($location_five->fulfillment_type, $response[1]['fulfillment_type']);
        $this->assertEquals($location_five->id, $response[1]['id']);

        $this->assertEquals($location_four->id, $response[2]['pickup_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($location_four->title, $response[2]['title']);
        $this->assertEquals($location_four->fulfillment_type, $response[2]['fulfillment_type']);
        $this->assertEquals($location_four->id, $response[2]['id']);

        $this->assertEquals($location_three->id, $response[3]['pickup_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($location_three->title, $response[3]['title']);
        $this->assertEquals($location_three->fulfillment_type, $response[3]['fulfillment_type']);
        $this->assertEquals($location_three->id, $response[3]['id']);

        $this->assertEquals($location_two->id, $response[4]['pickup_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($location_two->title, $response[4]['title']);
        $this->assertEquals($location_two->fulfillment_type, $response[4]['fulfillment_type']);
        $this->assertEquals($location_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_the_week(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'week',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_last_week(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastWeek',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_the_month(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'month',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_last_month(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastMonth',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_the_year(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'year',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_products_dashboard_for_last_year(): void
    {
        Carbon::setTestNow(now());

        $product_one = Product::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(1)->create(['order_id' => $order_one->id, 'product_id' => $product_one->id]);
        $product_two = Product::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(2)->create(['order_id' => $order_two->id, 'product_id' => $product_two->id]);
        $product_three = Product::factory()->create();
        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(3)->create(['order_id' => $order_three->id, 'product_id' => $product_three->id]);
        $product_four = Product::factory()->create();
        $order_four = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(4)->create(['order_id' => $order_four->id, 'product_id' => $product_four->id]);
        $product_five = Product::factory()->create();
        $order_five = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(5)->create(['order_id' => $order_five->id, 'product_id' => $product_five->id]);
        $product_six = Product::factory()->create();
        $order_six = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2)]);
        OrderItem::factory()->times(6)->create(['order_id' => $order_six->id, 'product_id' => $product_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastYear',
                'chart' => 'topProducts',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => ['product_id', 'count', 'title', 'id']
            ])
            ->json();

        $this->assertEquals($product_six->id, $response[0]['product_id']);
        $this->assertEquals(6, $response[0]['count']);
        $this->assertEquals($product_six->title, $response[0]['title']);
        $this->assertEquals($product_six->id, $response[0]['id']);

        $this->assertEquals($product_five->id, $response[1]['product_id']);
        $this->assertEquals(5, $response[1]['count']);
        $this->assertEquals($product_five->title, $response[1]['title']);
        $this->assertEquals($product_five->id, $response[1]['id']);

        $this->assertEquals($product_four->id, $response[2]['product_id']);
        $this->assertEquals(4, $response[2]['count']);
        $this->assertEquals($product_four->title, $response[2]['title']);
        $this->assertEquals($product_four->id, $response[2]['id']);

        $this->assertEquals($product_three->id, $response[3]['product_id']);
        $this->assertEquals(3, $response[3]['count']);
        $this->assertEquals($product_three->title, $response[3]['title']);
        $this->assertEquals($product_three->id, $response[3]['id']);

        $this->assertEquals($product_two->id, $response[4]['product_id']);
        $this->assertEquals(2, $response[4]['count']);
        $this->assertEquals($product_two->title, $response[4]['title']);
        $this->assertEquals($product_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_the_week(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->addDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'week',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_last_week(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfWeek()->subDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastWeek',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_the_month(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->addDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'month',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_last_month(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfMonth()->subDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastMonth',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_the_year(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->addDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'year',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }

    #[Test]
    public function admins_can_fetch_top_customers_dashboard_for_last_year(): void
    {
        Carbon::setTestNow(now());

        $customer_one = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 1234, 'customer_id' => $customer_one->id]);
        $customer_two = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 2234, 'customer_id' => $customer_two->id]);
        $customer_three = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 3234, 'customer_id' => $customer_three->id]);
        $customer_four = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 4234, 'customer_id' => $customer_four->id]);
        $customer_five = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 5234, 'customer_id' => $customer_five->id]);
        $customer_six = User::factory()->create();
        Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => now()->startOfYear()->subDays(2), 'total' => 6234, 'customer_id' => $customer_six->id]);

        $response = $this->actingAsApiAdmin()
            ->getJson(route('api.dashboard.index', [
                'dateRange' => 'lastYear',
                'chart' => 'topCustomers',
            ]))
            ->assertOk()
            ->assertJsonCount(5)
            ->assertJsonStructure([
                '*' => [
                    'customer_id',  'revenue', 'first_name', 'last_name', 'id'
                ]
            ])
            ->json();

        $this->assertEquals($customer_six->id, $response[0]['customer_id']);
        $this->assertEquals('62.34', $response[0]['revenue']);
        $this->assertEquals($customer_six->first_name, $response[0]['first_name']);
        $this->assertEquals($customer_six->last_name, $response[0]['last_name']);
        $this->assertEquals($customer_six->id, $response[0]['id']);

        $this->assertEquals($customer_five->id, $response[1]['customer_id']);
        $this->assertEquals('52.34', $response[1]['revenue']);
        $this->assertEquals($customer_five->first_name, $response[1]['first_name']);
        $this->assertEquals($customer_five->last_name, $response[1]['last_name']);
        $this->assertEquals($customer_five->id, $response[1]['id']);

        $this->assertEquals($customer_four->id, $response[2]['customer_id']);
        $this->assertEquals('42.34', $response[2]['revenue']);
        $this->assertEquals($customer_four->first_name, $response[2]['first_name']);
        $this->assertEquals($customer_four->last_name, $response[2]['last_name']);
        $this->assertEquals($customer_four->id, $response[2]['id']);

        $this->assertEquals($customer_three->id, $response[3]['customer_id']);
        $this->assertEquals('32.34', $response[3]['revenue']);
        $this->assertEquals($customer_three->first_name, $response[3]['first_name']);
        $this->assertEquals($customer_three->last_name, $response[3]['last_name']);
        $this->assertEquals($customer_three->id, $response[3]['id']);

        $this->assertEquals($customer_two->id, $response[4]['customer_id']);
        $this->assertEquals('22.34', $response[4]['revenue']);
        $this->assertEquals($customer_two->first_name, $response[4]['first_name']);
        $this->assertEquals($customer_two->last_name, $response[4]['last_name']);
        $this->assertEquals($customer_two->id, $response[4]['id']);

        Carbon::setTestNow();
    }
}
