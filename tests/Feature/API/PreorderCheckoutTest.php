<?php

namespace Tests\Feature\API;

use App\Actions\Product\ConfirmPreorderPurchase;
use App\Events\Order\OrderWasConfirmed;
use App\Models\Card;
use App\Models\Date;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class PreorderCheckoutTest extends TenantTestCase
{
    #[Test]
    public function it_requires_a_logged_in_user(): void
    {
        $product = Product::factory()->create();

        $this->postJson(route('api.products.checkout.store', ['product' => $product->slug]))
            ->assertUnauthorized();
    }

    #[Test]
    public function it_requires_a_valid_product(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => 'somethinginvalid']))
            ->assertNotFound();
    }

    #[Test]
    public function it_validates_the_preorder_confirmation_request(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]))
            ->assertUnprocessable()
            ->assertJsonFragment([
                'type' => ['The type field is required.'],
                'items' => ['The items field is required.'],
                'customer' => ['The customer field is required.'],
                'billing' => ['The billing field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['type' => 'something'])
            ->assertUnprocessable() // inactive date
            ->assertJsonFragment(['type' => ['The selected type is invalid.']]);

        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['date_id' => $date->id])
            ->assertUnprocessable() // inactive date
            ->assertJsonFragment(['date_id' => ['The selected date id is invalid.']]);

        $location = Pickup::factory()->create(['status_id' => 2]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['delivery_method_id' => $location->id,])
            ->assertUnprocessable() // closed location
            ->assertJsonFragment(['delivery_method_id' => ['The selected delivery method id is invalid.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), [
                'items' => 'foo',
                'customer' => 'foo',
                'shipping' => 'foo',
                'billing' => 'foo',
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items' => ['The items field must be an array.'],
                'customer' => ['The customer field must be an array.'],
                'shipping' => ['The shipping field must be an array.'],
                'billing' => ['The billing field must be an array.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => []])
            ->assertUnprocessable()
            ->assertJsonFragment(['items' => ['The items field is required.']]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => [[]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The items.0.product_id field is required.'],
                'items.0.quantity' => ['The items.0.quantity field is required.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['items' => [[
                'product_id' => 'foo',
                'quantity' => 0,
            ]]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'items.0.product_id' => ['The selected items.0.product_id is invalid.'],
                'items.0.quantity' => ['The items.0.quantity field must be at least 1.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['customer' => [
                'save_for_later' => 'foo',
                'opt_in_to_sms' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'customer.first_name' => ['The customer.first name field is required.'],
                'customer.last_name' => ['The customer.last name field is required.'],
                'customer.email' => ['The customer.email field is required.'],
                'customer.phone' => ['The customer.phone field is required.'],
                'customer.save_for_later' => ['The customer.save for later field must be true or false.'],
                'customer.opt_in_to_sms' => ['The customer.opt in to sms field must be true or false.'],
            ]);

        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 0]);
        $location = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_PICKUP]); // pickup location
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), [
                'type' => 'pre-order',
                'delivery_method_id' => $location->id,
                'shipping' => ['save_for_later' => 'foo']
            ])
            ->assertUnprocessable()
            ->assertJsonMissingValidationErrors([
                'shipping.street',
                'shipping.city',
                'shipping.state',
                'shipping.zip',
            ]);

        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 1]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), [
                'type' => 'pre-order',
                'delivery_method_id' => $location->id,
                'shipping' => ['save_for_later' => 'foo']
            ])
            ->assertUnprocessable()
            ->assertJsonValidationErrors([
                'shipping.street',
                'shipping.city',
                'shipping.state',
                'shipping.zip',
            ]);

        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 0]);
        $location = Pickup::factory()->create(['fulfillment_type' => 2]); // delivery zone
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), [
                'type' => 'pre-order',
                'delivery_method_id' => $location->id,
                'shipping' => ['save_for_later' => 'foo']
            ])
            ->assertUnprocessable()
            ->assertJsonValidationErrors([
                'shipping.street' => ['The shipping.street field is required.'],
                'shipping.city' => ['The shipping.city field is required.'],
                'shipping.state' => ['The shipping.state field is required.'],
                'shipping.zip' => ['The shipping.zip field is required.'],
                'shipping.save_for_later' => ['The shipping.save for later field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => false]);
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['billing' => [
                'method' => $method->key,
                'save_for_later' => 'foo',
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.method' => ['The selected billing.method is invalid.'],
                'billing.save_for_later' => ['The billing.save for later field must be true or false.'],
            ]);

        $method = Payment::factory()->create(['enabled' => true, 'key' => 'other']);
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['billing' => [
                'method' => $method->key,
                'source_id' => null
            ]])
            ->assertUnprocessable()
            ->assertJsonMissingValidationErrors([
                'billing.source_id'
            ]);

        $method = Payment::where('key', 'card')->first();
        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), ['billing' => [
                'method' => $method->key,
            ]])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'billing.source_id' => ['The billing.source id field is required when billing.method is card.'],
            ]);

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]),[
                'notes' => Str::random(1001),
            ])
            ->assertUnprocessable()
            ->assertJsonFragment([
                'notes' => ['The notes field must not be greater than 1000 characters.'],
            ]);
    }

    #[Test]
    public function it_confirms_a_delivery_preorder(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        $location = Pickup::factory()->create(['fulfillment_type' => Pickup::FULFILLMENT_TYPE_DELIVERY, 'status_id' => 1, 'settings' => ['sales_channel' => 2]]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();

        $cart = [
            'type' => 'pre-order',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => '123 Test St',
                'street_2' => 'Apt Test',
                'city' => 'Test',
                'state' => 'TE',
                'zip' => '12345',
                'country' => 'US',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(ConfirmPreorderPurchase::class, function (MockInterface $mock) use ($cart, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(User $arg) => $arg->id === $user->id), $cart)
                ->andReturn(new Order);
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order'])
            ->assertSessionHas('orderWasConfirmed');

        Event::assertDispatched(OrderWasConfirmed::class);
    }

    #[Test]
    public function it_confirms_a_pickup_without_address_preorder(): void
    {
        Event::fake([OrderWasConfirmed::class]);

        Setting::updateOrCreate(['key' => 'require_address_at_checkout'], ['value' => 0]);


        $location = Pickup::factory()->create(['fulfillment_type' => 1, 'status_id' => 1, 'settings' => ['sales_channel' => 2]]);

        $user = User::factory()->create();
        $card = Card::factory()->create(['user_id' => $user->id]);
        /** @var Date $date */
        $date = Date::factory()->create(['schedule_id' => Schedule::factory(), 'active' => true]);

        $method = Payment::where('key', 'card')->first();
        $method->enabled = true;
        $method->save();

        $product = Product::factory()->create();

        $cart = [
            'type' => 'pre-order',
            'date_id' => $date->id,
            'delivery_method_id' => $location->id,
            'items' => [
                [
                    'product_id' => $product->id,
                    'quantity' => 2
                ]
            ],
            'notes' => 'These are my order notes.',
            'customer' => [
                'first_name' => 'First test',
                'last_name' => 'Last test',
                'email' => '<EMAIL>',
                'phone' => '************',
                'save_for_later' => true,
                'opt_in_to_sms' => true,
            ],
            'shipping' => [
                'street' => null,
                'street_2' => null,
                'city' => null,
                'state' => null,
                'zip' => null,
                'country' => null,
                'save_for_later' => true
            ],
            'billing' => [
                'method' => $method->key,
                'source_id' => $card->source_id,
                'save_for_later' => true
            ],
        ];

        $this->mock(ConfirmPreorderPurchase::class, function (MockInterface $mock) use ($cart, $user) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(\Mockery::on(fn(User $arg) => $arg->id === $user->id), $cart)
                ->andReturn(new Order);
        });

        $this->actingAs($user, 'api')
            ->postJson(route('api.products.checkout.store', ['product' => $product->slug]), $cart)
            ->assertCreated()
            ->assertJsonStructure(['order'])
            ->assertSessionHas('orderWasConfirmed');

        Event::assertDispatched(OrderWasConfirmed::class);
    }
}
