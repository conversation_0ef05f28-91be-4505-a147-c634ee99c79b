<?php

namespace Tests\Feature\API;

use App\Events\User\UserUpdated;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\Setting;
use App\Models\User;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CustomerTest extends TenantTestCase
{
    #[Test]
    public function a_guest_can_update_their_pickup_location(): void
    {
        Event::fake([UserUpdated::class]);

        $pickup = Pickup::factory()->create();

        $this->put(route('api.customers.update'), [
            'pickup_point' => $pickup->id,
        ])
            ->assertOk()
            ->assertJson([
                'request' => [
                    'pickup_point' => $pickup->id
                ],
                'pickup_point' => $pickup->id,
                'redirect' => route('store.index')
            ])
            ->assertCookie('fulfillment_id', $pickup->id, false);

        Event::assertNotDispatched(UserUpdated::class);
    }

    #[Test]
    public function an_authenticated_user_without_orders_can_update_their_pickup_location(): void
    {
        Event::fake([UserUpdated::class]);

        $user = User::factory()->create();

        $pickup = Pickup::factory()->create();

        $this->actingAs($user)
            ->get(route('customer.profile'))
            ->assertOk();

        $this->actingAs($user)
            ->put(route('api.customers.update'), [
                'pickup_point' => $pickup->id,
            ])
            ->assertOk()
            ->assertJson([
                'request' => [
                    'pickup_point' => $pickup->id
                ],
                'pickup_point' => $pickup->id,
                'redirect' => route('customer.profile')
            ])
            ->assertCookieMissing('fulfillment_id');

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'pickup_point' => $pickup->id
        ]);

        Event::assertDispatched(UserUpdated::class, function (UserUpdated $event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    #[Test]
    public function an_authenticated_user_without_orders_can_update_their_address(): void
    {
        Event::fake([UserUpdated::class]);

        $user = User::factory()->create();

        $pickup = Pickup::factory()->create();

        $this->actingAs($user)
            ->get(route('customer.profile'))
            ->assertOk();

        $this->actingAs($user)
            ->put(route('api.customers.update'), [
                'pickup_point' => $pickup->id,
            ])
            ->assertOk()
            ->assertJson([
                'request' => [
                    'pickup_point' => $pickup->id
                ],
                'pickup_point' => $pickup->id,
                'redirect' => route('customer.profile')
            ])
            ->assertCookieMissing('fulfillment_id');

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'pickup_point' => $pickup->id
        ]);

        Event::assertDispatched(UserUpdated::class, function (UserUpdated $event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    #[Test]
    public function changing_to_zone_with_a_repeating_schedule_and_subscriptions_maintains_recurring_order(): void
    {
        $pickup_with_repeating_one = Pickup::factory()->withRepeatingSchedule()->create();
        $new_pickup = Pickup::factory()->withRepeatingSchedule()->create();

        $user = User::factory()->create(['pickup_point' => $pickup_with_repeating_one->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'is_recurring' => true]);
        $promo_item = OrderItem::factory()->create([
            'order_id' => $order->id,
            'subtotal' => 0,
            'type' => 'promo',
        ]);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) {
            $mock->shouldReceive('discountIncentive')->andReturn(5);
        });

        $this->actingAs($user, 'api')
            ->putJson(route('api.customers.update'), ['pickup_point' => $new_pickup->id])
            ->assertOk()
            ->assertJson([
                'request' => [
                    'pickup_point' => $new_pickup->id
                ],
                'pickup_point' => $new_pickup->id,
                'redirect' => url('/')
            ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'is_recurring' => true
        ]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $promo_item->id,
            'order_id' => $order->id,
        ]);
    }

    #[Test]
    public function it_does_not_update_user_when_open_order_is_on_a_blueprint(): void
    {
        $old_pickup = Pickup::factory()->create();
        $new_pickup = Pickup::factory()->create();
        $user = User::factory()->create(['order_id' => 0, 'pickup_point' => $old_pickup->id]);
        $blueprint = RecurringOrder::factory()->create(['customer_id' => $user->id]);
        Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'blueprint_id' => $blueprint->id]);

        $this->actingAs($user, 'api')
            ->putJson(route('api.customers.update'), ['pickup_point' => $new_pickup->id])
            ->assertStatus(409)
            ->assertJsonFragment(['The location cannot be updated when on a subscription.']);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'pickup_point' => $old_pickup->id
        ]);
    }

    #[Test]
    public function it_updates_customer_pickup_point_when_there_is_a_confirmed_order(): void
    {
        $old_pickup = Pickup::factory()->create(['schedule_id' => null]);
        $new_pickup = Pickup::factory()->create();

        $user = User::factory()->create(['order_id' => 0, 'pickup_point' => $old_pickup->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => true, 'schedule_id' => 0]);

        $this->actingAs($user, 'api')
            ->putJson(route('api.customers.update'), ['pickup_point' => $new_pickup->id])
            ->assertOk()
            ->assertJson([
                'request' => [
                    'pickup_point' => $new_pickup->id
                ],
                'pickup_point' => $new_pickup->id,
                'redirect' => url('/')
            ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $user->id,
            'pickup_point' => $new_pickup->id,
        ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
