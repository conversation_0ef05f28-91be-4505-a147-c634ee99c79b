<?php

namespace Tests\Feature\Theme\Checkout;

use App\Cart\Item;
use App\Cart\Offer;
use App\Contracts\CartService;
use App\Models\Product;
use App\Services\OfferService;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CheckoutOffersTest extends TenantTestCase
{
    #[Test]
    public function it_redirects_to_cart_when_cart_is_empty(): void
    {
        $this->mock(CartService::class, function (MockInterface $mock) {
            $mock->shouldReceive('find')->once()->andReturn(new \App\Models\Cart());
        });

        $this->mock(OfferService::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('forCart');
        });

        $this->get(route('checkout.offers.show'))
            ->assertRedirect(route('cart.show'));
    }


    #[Test]
    public function it_loads_offers_page_when_offers_are_found(): void
    {
        $product = Product::factory()->create();

        $cart = new \App\Models\Cart();
        $cart->addItemToCart(new Item('some-id', $product));

        $this->mock(CartService::class, function (MockInterface $mock) use ($cart) {
            $mock->shouldReceive('find')->andReturn($cart);
        });

        $this->mock(OfferService::class, function (MockInterface $mock) use ($cart) {
            $mock->shouldReceive('forCart')
                ->with($cart)
                ->andReturn(collect([new Offer('subscription', $cart)]));
        });

        $this->get(route('checkout.offers.show'))
            ->assertOk();
    }

    #[Test]
    public function it_redirects_to_checkout_when_no_offers_are_found(): void
    {
        $product = Product::factory()->create();

        $cart = new \App\Models\Cart();
        $cart->addItemToCart(new Item('some-id', $product));

        $this->mock(CartService::class, function (MockInterface $mock) use ($cart) {
            $mock->shouldReceive('find')->andReturn($cart);
        });

        $this->mock(OfferService::class, function (MockInterface $mock) use ($cart) {
            $mock->shouldReceive('forCart')
                ->with($cart)
                ->andReturn(collect());
        });

        $this->get(route('checkout.offers.show'))
            ->assertRedirect(route('checkout.confirm.show'));
    }
}
