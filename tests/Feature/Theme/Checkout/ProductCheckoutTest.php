<?php

namespace Tests\Feature\Theme\Checkout;

use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use App\Support\Enums\ProductType;
use Inertia\Testing\AssertableInertia as Assert;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductCheckoutTest extends TenantTestCase
{
    #[Test]
    public function it_requires_an_authenticated_user(): void
    {
        $product = Product::factory()->create(['schedule_id' => Schedule::factory()]);

        $this->get(route('store.products.checkout.show', ['product' => $product->slug]))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_requires_a_valid_product(): void
    {
        $this->actingAsCustomer()
            ->get(route('store.products.checkout.show', ['product' => '123asd']))
            ->assertRedirect('/');
    }

    #[Test]
    public function it_requires_schedule_on_the_product_or_customer_delivery_method(): void
    {
        $product = Product::factory()->create(['schedule_id' => null]);

        $this->actingAsCustomer()
            ->get(route('store.products.checkout.show', ['product' => $product->slug]))
            ->assertRedirect(route('store.index'))
            ->assertSessionHas('flash_notification', [
                'message' => 'This item is unavailable for pre-order.',
                'level' => 'error'
            ]);
    }

    #[Test]
    public function it_shows_the_preorder_product_checkout_page(): void
    {
        $delivery_method = Pickup::factory()->create();

        $product = Product::factory()->create([
            'schedule_id' => Schedule::factory(),
            'type_id' => ProductType::PREORDER->value,
        ]);

        $this->actingAsCustomer()
            ->withCookie('shopping_delivery_method_id', $delivery_method->id)
            ->get(route('store.products.checkout.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertInertia(function(Assert $page) {
                return $page
                    ->component('Checkout/Preorder')
                    ->has('checkout_settings')
                    ->has('cart');
            });
    }

    #[Test]
    public function it_shows_the_gift_card_product_checkout_page(): void
    {
        $product = Product::factory()->create([
            'schedule_id' => Schedule::factory(),
            'type_id' => ProductType::GIFT_CARD->value,
            'settings' => ['fulfilment_method' => 'virtual']
        ]);

        $this->actingAsCustomer()
            ->get(route('store.products.checkout.show', ['product' => $product->slug]))
            ->assertOk()
            ->assertInertia(function(Assert $page) {
                return $page
                    ->component('Checkout/GiftCard')
                    ->has('product')
                    ->has('cart');
            });
    }
}
