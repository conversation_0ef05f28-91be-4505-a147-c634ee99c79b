<?php

namespace Tests\Feature\Theme\Store;

use App\Cart\Item;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\Setting;
use App\Models\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class CartShortStockTest extends TenantTestCase
{
    #[Test]
    public function a_guest_without_a_cart_cannot_adjust_short_stock_items_in_cart(): void
    {
        $this->put(route('cart.short-stock.update'))
            ->assertRedirect(route('cart.show'))
            ->assertSessionHas('flash_notification', [
                'message' => 'The cart is empty.',
                'level' => 'info'
            ]);
    }

    #[Test]
    public function a_authenticated_customer_without_a_cart_cannot_adjust_short_stock_items_in_cart(): void
    {
        $this->actingAsCustomer()
            ->put(route('cart.short-stock.update'))
            ->assertRedirect(route('cart.show'))
            ->assertSessionHas('flash_notification', [
                'message' => 'The cart is empty.',
                'level' => 'info'
            ]);
    }

    #[Test]
    public function an_authenticated_customer_with_an_empty_cart_cannot_adjust_short_stock_items_in_cart(): void
    {
        $user = User::factory()->create();

        Order::factory()->create(['customer_id' => $user->id]);

        $this->actingAs($user)
            ->put(route('cart.short-stock.update'))
            ->assertRedirect(route('cart.show'))
            ->assertSessionHas('flash_notification', [
                'message' => 'The cart is empty.',
                'level' => 'info'
            ]);
    }

    #[Test]
    public function an_authenticated_customer_can_adjust_short_stock_items(): void
    {
        $user = User::factory()->create(['pickup_point' => Pickup::factory()]);

        $cart = Cart::factory()->create(['shopper_type' => User::class, 'shopper_id' => $user->id]);
        $cart->updateCartLocation($user->pickup);

        $available_product = Product::factory()->create(['inventory' => 10]);
        $short_product = Product::factory()->create(['inventory' => 3]);
        $out_product = Product::factory()->create(['inventory' => 1]);

        $available_item = new Item(
            id: 'abc-123',
            product: $available_product,
            quantity: 1,
        );

        $cart->addItemToCart($available_item);

        $short_item = new Item(
            id: 'def-123',
            product: $short_product,
            quantity: 2,
        );

        $cart->addItemToCart($short_item);

        $out_item = new Item(
            id: 'ghi-123',
            product: $out_product,
            quantity: 1,
        );

        $cart->addItemToCart($out_item);

        $short_product->inventory = 1;
        $short_product->save();

        $out_product->inventory = 0;
        $out_product->save();

        $this->actingAs($user)
            ->get('/');

        $this->put(route('cart.short-stock.update'))
            ->assertRedirect('/')
            ->assertSessionHas('flash_notification', [
                'message' => 'The cart has been updated and the total was adjusted.',
                'level' => 'info'
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}
