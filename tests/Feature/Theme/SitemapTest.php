<?php

namespace Tests\Feature\Theme;

use App\Models\Category;
use App\Models\Collection;
use App\Models\Page;
use App\Models\Post;
use App\Models\Product;
use App\Models\Recipe;
use App\Models\User;
use App\Support\Enums\ProductType;
use App\Support\Enums\UserRole;
use Illuminate\Support\Facades\Storage;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SitemapTest extends TenantTestCase
{
    #[Test]
    public function it_generates_and_stores_a_sitemap_when_it_is_not_in_storage(): void
    {
        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee('http://www.sitemaps.org/schemas/sitemap/0.9');
    }

    #[Test]
    public function it_returns_existing_sitemap_when_it_is_in_storage(): void
    {
        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnTrue()
            ->shouldReceive()
            ->get()
            ->once()
            ->withArgs(['sitemap.xml'])
            ->andReturn('abc')
            ->shouldNotReceive()
            ->put();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee('abc');
    }

    #[Test]
    public function it_generates_a_sitemap_containing_the_homepage(): void
    {
        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('homepage.show'));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_visible_custom_urls(): void
    {
        $page_one = Page::factory()->create(['seo_visibility' => false]);
        $page_two = Page::factory()->create(['seo_visibility' => true]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('page.show', [$page_two->slug]))
            ->assertDontSee(route('page.show', [$page_one->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_published_blog_posts(): void
    {
        $post_one = Post::factory()->create(['status' => 'draft']);
        $post_two = Post::factory()->create(['status' => 'published']);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('blog.index'))
            ->assertSee(route('blog.show', [$post_two->slug]))
            ->assertDontSee(route('blog.show', [$post_one->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_authors_with_published_posts(): void
    {
        $user = User::factory()->admin()->create(['first_name' => 'John', 'last_name' => 'Doe', 'role_id' => UserRole::ADMIN->value]);

        $post = Post::factory()->create([
            'user_id' => $user->id,
            'status' => 'published',
        ]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('blog.authors.show', ['john-doe']))
            ->assertSee($post->updated_at->format('c'));
    }

    #[Test]
    public function it_generates_a_sitemap_without_authors_with_no_published_posts(): void
    {
        $user = User::factory()->admin()->create(['first_name' => 'John', 'last_name' => 'Doe', 'role_id' => UserRole::ADMIN->value]);

        $post = Post::factory()->create([
            'user_id' => $user->id,
            'status' => 'draft',
        ]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertDontSee(route('blog.authors.show', ['john-doe']));
    }

    #[Test]
    public function it_generates_a_sitemap_without_non_staff_authors(): void
    {
        $user = User::factory()->admin()->create(['first_name' => 'John', 'last_name' => 'Doe', 'role_id' => UserRole::CUSTOMER->value]);

        $post = Post::factory()->create([
            'user_id' => $user->id,
            'status' => 'draft',
        ]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertDontSee(route('blog.authors.show', ['john-doe']));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_visible_store_category_urls(): void
    {
        $category_one = Category::factory()->create(['extra_attributes' => ['seo_visible' => false]]);
        $category_two = Category::factory()->create(['extra_attributes' => ['seo_visible' => true]]);
        $category_three = Category::factory()->create(['extra_attributes' => ['seo_visible' => true]]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('store.index'))
            ->assertSee(route('store.categories.show', [$category_two->slug]))
            ->assertSee(route('store.categories.show', [$category_three->slug]))
            ->assertDontSee(route('store.categories.show', [$category_one->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_visble_subcategory_urls(): void
    {
        $category_one = Category::factory()->create(['extra_attributes' => ['seo_visible' => true]]);
        $subcategory_one = Category::factory()->create(['category_id' => $category_one->id, 'extra_attributes' => ['seo_visible' => false]]);
        $subcategory_two = Category::factory()->create(['category_id' => $category_one->id, 'extra_attributes' => ['seo_visible' => true]]);

        $category_two = Category::factory()->create(['extra_attributes' => ['seo_visible' => false]]);
        $subcategory_three = Category::factory()->create(['category_id' => $category_two->id, 'extra_attributes' => ['seo_visible' => true]]);
        $subcategory_four = Category::factory()->create(['category_id' => $category_two->id, 'extra_attributes' => ['seo_visible' => true]]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('store.index'))
            ->assertSee(route('store.categories.show', [$category_one->slug]))
            ->assertDontSee(route('store.subcategories.show', [$category_one->slug, $subcategory_one->slug]))
            ->assertSee(route('store.subcategories.show', [$category_one->slug, $subcategory_two->slug]))
            ->assertDontSee(route('store.categories.show', [$category_two->slug]))
            ->assertDontSee(route('store.subcategories.show', [$category_two->slug, $subcategory_three->slug]))
            ->assertDontSee(route('store.subcategories.show', [$category_two->slug, $subcategory_four->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_visible_store_collection_urls(): void
    {
        $collection_one = Collection::factory()->create(['seo_visibility' => 0]);
        $collection_two = Collection::factory()->create(['seo_visibility' => 1]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('store.index'))
            ->assertSee(route('store.collections.show', [$collection_two->slug]))
            ->assertDontSee(route('store.collections.show', [$collection_one->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_visible_store_product_urls(): void
    {
        $product_one = Product::factory()->create(['type_id' => ProductType::STANDARD->value, 'seo_visibility' => 0]);
        $product_two = Product::factory()->create(['type_id' => ProductType::STANDARD->value, 'seo_visibility' => 1]);
        $gift_card = Product::factory()->create(['type_id' => ProductType::GIFT_CARD->value, 'seo_visibility' => 1]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('store.index'))
            ->assertSee(route('store.show', [$product_two->slug]))
            ->assertDontSee(route('store.show', [$product_one->slug]))
            ->assertDontSee(route('store.show', [$gift_card->slug]));
    }

    #[Test]
    public function it_generates_a_sitemap_containing_published_recipes(): void
    {
        $recipe_one = Recipe::factory()->create(['published' => 0]);
        $recipe_two = Recipe::factory()->create(['published' => 1]);

        Storage::shouldReceive()
            ->disk('tenants')
            ->andReturnSelf()
            ->shouldReceive()
            ->exists('sitemap.xml')
            ->once()
            ->andReturnFalse()
            ->shouldNotReceive()
            ->get('sitemap.xml')
            ->shouldReceive()
            ->put()
            ->withArgs(['sitemap.xml', \Mockery::type('string')])
            ->once()
            ->andReturnTrue();

        $this->get('sitemap.xml')
            ->assertOk()
            ->assertHeader('Content-Type', 'text/xml; charset=UTF-8')
            ->assertSee(route('recipes.index'))
            ->assertSee(route('recipes.show', [$recipe_two->slug]))
            ->assertDontSee(route('recipes.show', [$recipe_one->slug]));
    }
}
