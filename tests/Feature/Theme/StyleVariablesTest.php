<?php

namespace Tests\Feature\Theme;

use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class StyleVariablesTest extends TenantTestCase
{
    #[Test]
    public function it_can_return_style_variables_in_css_file(): void
    {
        $this->get(route('css.theme-variables.show'))
            ->assertOk()
            ->assertSee('--display-font-family')
            ->assertSee('--body-font-family')
            ->assertSee('--font-size')
            ->assertSee('--logo-padding')
            ->assertSee('--logo-height')
            ->assertSee('--header-width')
            ->assertSee('--header-border-position')
            ->assertSee('--header-border-size')
            ->assertSee('--header-bg-color')
            ->assertSee('--header-border-color')
            ->assertSee('--brand-color')
            ->assertSee('--brand-color-inverted')
            ->assertSee('--background-color')
            ->assertSee('--text-color')
            ->assertSee('--link-color')
            ->assertSee('--action-color')
            ->assertSee('--action-color-inverted')
            ->assertSee('--announcement-bar-bg-color')
            ->assertSee('--announcement-bar-text-color')
            ->assertSee('--announcement-bar-link-color')
            ->assertSee('--main-navigation-bg-color')
            ->assertSee('--main-navigation-link-color')
            ->assertSee('--main-navigation-link-color-hover')
            ->assertSee('--main-navigation-link-bg-color')
            ->assertSee('--main-navigation-link-font-size')
            ->assertSee('--main-navigation-link-alignment')
            ->assertSee('--auxiliary-bg-color')
            ->assertSee('--auxiliary-border-color')
            ->assertSee('--auxiliary-link-color')
            ->assertSee('--order-status-bg-color')
            ->assertSee('--order-status-color')
            ->assertSee('--store-menu-bg_color')
            ->assertSee('--store-menu-color')
            ->assertSee('--footer-bg-color')
            ->assertSee('--footer-color')
            ->assertSee('--footer-link-color');
    }
}
