<?php

namespace Tests\Feature\Livewire\Theme;

use App\Livewire\Theme\SubscriptionShow;
use App\Models\Address;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Support\Str;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionShowTest extends TenantTestCase
{
    #[Test]
    public function renders_successfully_when_the_authenticated_user_has_subscription()
    {
        $this->namespaceHints();

        $user = User::factory()->create();

        $subscription = RecurringOrder::factory()->create([
            'customer_id' => $user->id,
        ]);

        $this->actingAs($user);

        $soonest_order_window = $subscription->fulfillment->activeOrderWindow();

        Livewire::test(SubscriptionShow::class, ['subscription_id' => $subscription->id])
            ->assertStatus(200);
    }

    private function namespaceHints(): void
    {
        $hints[] = resource_path("theme/resources/views");

        view()->replaceNamespace('theme', $hints);
    }

    #[Test]
    public function renders_correct_shipping_address_details()
    {
        $this->namespaceHints();

        $user = User::factory()->create([
            'state' => 'IL',
            'city' => 'Chicago',
            'street' => 'Good User Street',
            'street_2' => 'Good User Street 2',
            'zip' =>  '60607',
        ]);

        $address = Address::firstOrCreate([
            'street' => 'Good Address Street',
            'city' => 'Add Chicago',
            'state' => 'Add IL',
            'postal_code' => '60601',
            'country' => 'US',
        ]);

        $user->addresses()->attach([$address->id => [
            'name' => Str::limit( "{$address->street}, {$address->city}, {$address->state}, {$address->zip}"),
            'street_2' => 'Good Address Street 2',
            'is_default' => true,
        ]]);

        $fulfilment = Pickup::factory()->delivery()->create();

        $subscription = RecurringOrder::factory()->create([
            'customer_id' => $user->id,
            'fulfillment_id' => $fulfilment->id,
        ]);

        $this->actingAs($user);

        Livewire::test(SubscriptionShow::class, ['subscription_id' => $subscription->id])
            ->assertStatus(200)
            ->assertSee('Good Address Street')
            ->assertSee('Add Chicago')
            ->assertSee('Add IL')
            ->assertSee('Good Address Street 2')
            ->assertSee('60601');
    }
}
