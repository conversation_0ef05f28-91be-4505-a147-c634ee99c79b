<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\CategoryShow;
use App\Models\Category;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class CategoryShowTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_renders_successfully()
    {
        $category = Category::factory()->create();

        Livewire::test(CategoryShow::class, compact('category'))
            ->assertStatus(200);
    }

    #[Test]
    public function it_can_display_a_category()
    {
       $category = Category::factory()->create(['name' => 'Milkshakes']);

        Livewire::test(CategoryShow::class, compact('category'))
            ->assertSee('Milkshakes');
    }

    #[Test]
    public function it_can_display_correct_subcategories()
    {
        $category = Category::factory()->create(['name' => 'Friuts']);

        Category::factory()->create(['name' => 'Managoes', 'category_id' => $category->id]);
        Category::factory()->create(['name' => 'Pinapples', 'category_id' => $category->id]);
        Category::factory()->create(['name' => 'Matope', 'category_id' => $category->id]);

        Livewire::test(CategoryShow::class, compact('category'))
            ->set('tab', 'subcategories')
            ->assertSee('Managoes')
            ->assertSee('Pinapples')
            ->assertSee('Matope');
    }

    #[Test]
    public function it_displays_the_tabs_for_a_parent_category()
    {
       $category = Category::factory()->create();

        Livewire::test(CategoryShow::class, compact('category'))
            ->set('tab', 'settings')
            ->assertSee('Basic')
            ->set('tab', 'subcategories')
            ->assertSee('A list of subcategories under');
    }

    #[Test]
    public function it_displays_the_tabs_for_a_child_category()
    {
        $category = Category::factory()->create(['category_id' => Category::factory()]);

        Livewire::test(CategoryShow::class, compact('category'))
            ->set('tab', 'settings')
            ->assertSee('Basic')
            ->set('tab', 'subcategories')
            ->assertDontSee('A list of subcategories under');
    }

    #[Test]
    public function it_sorts_subcategories_by_position()
    {
        $category = Category::factory()->create(['name' => 'Friuts']);

        $subcategory_one = Category::factory()->create(['name' => 'Managoes', 'category_id' => $category->id, 'position' => 19]);
        $subcategory_two = Category::factory()->create(['name' => 'Pinapples', 'category_id' => $category->id, 'position' => 10]);

        Livewire::test(CategoryShow::class, compact('category'))
            ->set('tab', 'subcategories')
            ->assertSee('Subcategories')
            ->assertSeeInOrder([$subcategory_two->name, $subcategory_one->name]);
    }

    #[Test]
    public function it_saves_category_with_valid_data()
    {
        $category = Category::factory()->create();

        $response = Livewire::test(CategoryShow::class, compact('category'))
            ->set('name', 'Updated Name')
            ->set('slug', 'updated-slug')
            ->set('summary', 'Updated Summary')
            ->set('subheading', 'Updated Subheading')
            ->call('save');

        $category->refresh();

        $response->assertDispatched('categoryUpdated');
        $this->assertEquals('Updated Name', $category->name);
        $this->assertEquals('updated-slug', $category->slug);
        $this->assertEquals('Updated Summary', $category->extra_attributes->get('summary'));
        $this->assertEquals('Updated Subheading', $category->extra_attributes->get('subheading'));
    }
}
