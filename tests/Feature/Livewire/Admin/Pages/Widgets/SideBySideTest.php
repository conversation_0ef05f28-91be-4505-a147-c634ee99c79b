<?php

namespace Tests\Feature\Livewire\Admin\Pages\Widgets;

use App\Livewire\Admin\Pages\Widgets\SideBySide;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SideBySideTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully(): void
    {
        Livewire::test(SideBySide::class, [
            'page_id' => 1,
            'widget' => [
                'id' => 'abc-def',
                'type' => 'side-by-side',
                'settings' => [
                    'name' => 'Side By Side Widget Name',
                    'html_id' => 'test-side-by-side',
                    'max_width' => 'none',
                    'padding' => [
                        'top' => 'sm',
                        'bottom' => 'sm'
                    ],
                    'background' => [
                        'color' => '#ffffff',
                    ],
                    'image_position' => 'left',
                    'image_url' => 'https://example.com/image.jpg',
                    'text' => 'Some text',
                ]
            ]
        ])
            ->assertStatus(200)
            ->assertSet('name', 'Side By Side Widget Name')
            ->assertSet('html_id', 'test-side-by-side')
            ->assertSet('max_width', 'none')
            ->assertSet('padding_top', 'sm')
            ->assertSet('padding_bottom', 'sm')
            ->assertSet('background_color', '#ffffff')
            ->assertSet('image_position', 'left')
            ->assertSet('image_url', 'https://example.com/image.jpg')
            ->assertSet('text', 'Some text');
    }

    #[Test]
    public function it_can_update_the_widget_settings(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'side-by-side',
            'settings' => [
                'name' => 'Side By Side Widget Name',
                'html_id' => 'test-side-by-side',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'background' => [
                    'color' => '#ffffff',
                ],
                'image_position' => 'left',
                'image_url' => 'https://example.com/image.jpg',
                'text' => 'Some text',
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(SideBySide::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->set('name', 'Side By Side Widget Name 2')
            ->set('html_id', 'abc')
            ->set('max_width', 'lg')
            ->set('padding_top', 'lg')
            ->set('padding_bottom', 'lg')
            ->set('background_color', '#000000')
            ->set('image_position', 'right')
            ->set('image_url', 'https://example.com/new-image.jpg')
            ->set('text', 'New text')
            ->call('save')
            ->assertHasNoErrors();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode([
                'type' => 'promo',
                'content' => [
                    [
                        'id' => 'abc-def',
                        'type' => 'side-by-side',
                        'settings' => [
                            'name' => 'Side By Side Widget Name 2',
                            'html_id' => 'abc',
                            'max_width' => 'lg',
                            'padding' => [
                                'top' => 'lg',
                                'bottom' => 'lg'
                            ],
                            'background' => [
                                'color' => '#000000',
                            ],
                            'image_position' => 'right',
                            'image_url' => 'https://example.com/new-image.jpg',
                            'text' => 'New text',
                        ],
                    ]
                ]
            ])
        ]);
    }

    #[Test]
    public function it_dispatches_an_event_on_successful_save(): void
    {
        $widget = [
            'id' => 'abc-def',
            'type' => 'side-by-side',
            'settings' => [
                'name' => 'Side By Side Widget Name',
                'html_id' => 'test-side-by-side',
                'max_width' => 'none',
                'padding' => [
                    'top' => 'sm',
                    'bottom' => 'sm'
                ],
                'background' => [
                    'color' => '#ffffff',
                ],
                'image_position' => 'left',
                'image_url' => 'https://example.com/image.jpg',
                'text' => 'Some text',
            ],
        ];

        $page = Page::factory()->create([
            'settings' => [
                'type' => 'promo',
                'content' => [$widget]
            ]
        ]);

        Livewire::test(SideBySide::class, [
            'page_id' => $page->id,
            'widget' => $widget,
        ])
            ->assertStatus(200)
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Widget updated!',
                'message' => 'The widget settings have been successfully updated.',
                'duration' => 3000
            ]);
    }
}
