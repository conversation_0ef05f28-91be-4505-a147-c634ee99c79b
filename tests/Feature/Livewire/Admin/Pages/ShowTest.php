<?php

namespace Tests\Feature\Livewire\Admin\Pages;

use App\Livewire\Admin\Pages\Show;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ShowTest extends TenantTestCase
{
    #[Test]
    public function it_renders_successfully()
    {
        $page = Page::factory()->create(['settings' => ['content' => []]]);

        Livewire::test(Show::class, [
            'page_id' => $page->id
        ])
            ->assertOk()
            ->assertViewIs('livewire.pages.show')
            ->assertSet('page_id', $page->id)
            ->assertSet('slug', $page->slug)
            ->assertSet('title', $page->title)
            ->assertSet('public', $page->visible)
            ->assertSet('seo_meta_title', $page->page_title)
            ->assertSet('seo_meta_description', $page->description)
            ->assertSet('seo_visible', $page->seo_visibility)
            ->assertSet('content', $page->settings->content);
    }

    #[Test]
    public function it_can_update_the_widget_sort_order(): void
    {
        $widget_one = ['id' => 'abc-def'];
        $widget_two = ['id' => 'def-ghi'];
        $widget_three = ['id' => 'ghi-jkl'];

        $page = Page::factory()->create([
            'settings' => [
                'content' => [
                    $widget_one,
                    $widget_two,
                    $widget_three,
                ]
            ]
        ]);

        Livewire::test(Show::class, [
            'page_id' => $page->id
        ])
            ->call('updateWidgetOrder', [
                ['order' => 1, 'value' => $widget_three['id']],
                ['order' => 2, 'value' => $widget_one['id']],
                ['order' => 3, 'value' => $widget_two['id']],
            ])
            ->assertSet('content', [
                $widget_three,
                $widget_one,
                $widget_two,
            ]);

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode(['content' => [
                $widget_three,
                $widget_one,
                $widget_two,
            ]]),
        ]);
    }

    #[Test]
    public function its_settings_can_be_updated(): void
    {
        $page = Page::factory()->create([
            'slug' => 'old-slug',
            'title' => 'Old Title',
            'visible' => false,
            'page_title' => 'Old Meta Title',
            'description' => 'Old Meta Description',
            'seo_visibility' => false,
        ]);

        Livewire::test(Show::class, [
            'page_id' => $page->id
        ])
            ->set('slug', 'new-slug')
            ->set('title', 'New Title')
            ->set('public', true)
            ->set('seo_meta_title', 'New Meta Title')
            ->set('seo_meta_description', 'New Meta Description')
            ->set('seo_visible', true)
            ->call('save')
            ->assertHasNoErrors()
            ->assertSet('slug', 'new-slug')
            ->assertSet('title', 'New Title')
            ->assertSet('public', true)
            ->assertSet('seo_meta_title', 'New Meta Title')
            ->assertSet('seo_meta_description', 'New Meta Description')
            ->assertSet('seo_visible', true);

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'slug' => 'new-slug',
            'title' => 'New Title',
            'visible' => true,
            'page_title' => 'New Meta Title',
            'description' => 'New Meta Description',
            'seo_visibility' => true,
        ]);
    }

    #[Test]
    public function it_sends_a_notification_on_save(): void
    {
        $page = Page::factory()->create();

        Livewire::test(Show::class, [
            'page_id' => $page->id
        ])
            ->call('save')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Page updated!',
                'message' => 'The page has been updated!',
                'duration' => 3000
            ]);
    }

    #[Test]
    public function it_can_clone_a_widget(): void
    {
        $widget = ['id' => 'abc-def', 'type' => 'banner', 'settings' => ['key' => 'value']];

        $page = Page::factory()->create([
            'settings' => [
                'content' => [
                    $widget,
                ],
            ],
        ]);

        Livewire::test(Show::class, [
            'page_id' => $page->id,
        ])
            ->call('cloneWidget', $widget['id'])
            ->assertSet('content', function ($content) use ($widget) {
                return count($content) === 2
                    && $content[0]['id'] === $widget['id']
                    && $content[1]['type'] === $widget['type']
                    && $content[1]['settings'] === $widget['settings']
                    && $content[1]['id'] !== $widget['id']; // Ensure the cloned widget has a new ID
            });

       $page->refresh();

        $this->assertDatabaseHas(Page::class, [
            'id' => $page->id,
            'settings' => json_encode(['content' => [
                $widget,
                [
                    'id' => $page->settings->content[1]['id'], // New ID
                    'type' => $widget['type'],
                    'settings' => $widget['settings'],
                ],
            ]]),
        ]);
    }

}
