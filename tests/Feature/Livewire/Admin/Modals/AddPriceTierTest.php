<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\AddPriceTier;
use App\Models\Price;
use App\Models\Product;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddPriceTierTest extends TenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $product = Product::factory()->create();

        Livewire::test(AddPriceTier::class)
            ->dispatch('open-modal-add-price-tier', product_id: $product->id)
            ->assertStatus(200)
            ->assertSet('product_id', $product->id);
    }

    #[Test]
    public function it_validates_the_request_upon_submission(): void
    {
       $product = Product::factory()->create();

        Livewire::test(AddPriceTier::class)
            ->dispatch('open-modal-add-price-tier', product_id: $product->id)
            ->set('quantity', 0)
            ->call('submit')
            ->assertHasErrors(['quantity' => 'min:1'])
            ->set('unit_price', 'abc')
            ->call('submit')
            ->assertHasErrors(['unit_price' => 'numeric'])
            ->set('sale_unit_price', 'abc')
            ->call('submit')
            ->assertHasErrors(['sale_unit_price' => 'numeric']);
    }

    #[Test]
    public function it_can_add_a_price(): void
    {
        $product = Product::factory()->create();

        Livewire::test(AddPriceTier::class)
            ->dispatch('open-modal-add-price-tier', product_id: $product->id)
            ->set('quantity', 5)
            ->set('unit_price', '11.12')
            ->set('sale_unit_price', '22.34')
            ->call('submit')
            ->assertHasNoErrors()
            ->assertDispatched('admin-notification-sent', notification: [
                'level' => 'success',
                'title' => 'Success!',
                'message' => 'The price tier has been added successfully.',
                'duration' => 3000
            ])
            ->assertDispatched('price-tier-added', product_id: $product->id, quantity: 5);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $product->id,
            'quantity' => 5,
            'unit_price' => 1112,
            'sale_unit_price' => 2234,
        ]);
    }
}
