<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\PreviewWidget;
use App\Models\Page;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class PreviewWidgetTest extends LivewireTenantTestCase
{
    #[Test]
    public function it_can_render(): void
    {
        $page = Page::factory()->create();

        $widget = [
            'id' => 'abc-123',
            'type' => 'text',
            'data' => ['text' => 'Hello, world!'],
        ];

        Livewire::test(PreviewWidget::class, ['page_id' => $page->id])
            ->dispatch('open-modal-preview-widget', $widget)
            ->assertStatus(200)
            ->assertSet('open', true)
            ->assertSet('widget', $widget);
    }

    #[Test]
    function it_can_close(): void
    {
        $page = Page::factory()->create();

        $widget = [
            'id' => 'abc-123',
            'type' => 'text',
            'data' => ['text' => 'Hello, world!'],
        ];

        Livewire::test(PreviewWidget::class, ['page_id' => $page->id])
            ->dispatch('open-modal-preview-widget', $widget)
            ->assertStatus(200)
            ->assertSet('page_id', $page->id)
            ->assertSet('open', true)
            ->call('close')
            ->assertSet('widget', null)
            ->assertSet('open', false);
    }
}
