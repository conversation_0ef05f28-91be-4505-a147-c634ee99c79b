<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\OrderReplacement;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class OrderReplacementTest extends LivewireTenantTestCase
{
    #[Test]
    public function can_mount_component()
    {
        $order = Order::factory()->create();

        Livewire::test(OrderReplacement::class)
            ->dispatch('open-modal-order-replacement', $order->id)
            ->assertStatus(200)
            ->assertSee('Order Replacement')
            ->assertSee('Create a new, unconfirmed order that is identical to this order.')
            ->assertSee('Delivery Date');
    }

    #[Test]
    function it_validates_the_submit_request(): void
    {
        $order = Order::factory()->create();

        Livewire::test(OrderReplacement::class)
            ->dispatch('open-modal-order-replacement', $order->id)
            ->assertStatus(200)
            ->set('delivery_date', '1999-12-31')
            ->call('submit')
            ->assertHasErrors(['delivery_date' => 'The delivery date field must be a date after or equal to today.']);
    }
    
    #[Test]
    public function an_admin_can_replace_an_order(): void
    {
        Carbon::setTestNow(now());

        $order = Order::factory()->create([
            'confirmed' => true,
            'processed' => true,
            'status_id' => OrderStatus::COMPLETED,
            'pickup_date' => now(),
            'created_at' => now()->subDays(2),
        ]);


        $newPickupDate = now()->addDays(5)->toDateString();

        Livewire::test(OrderReplacement::class)
            ->dispatch('open-modal-order-replacement', $order->id)
            ->assertStatus(200)
            ->set('delivery_date', $newPickupDate)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertSet('open', false);

        $order->refresh();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_date' => now()->format('Y-m-d'),
            'confirmed' => true,
            'processed' => true,
            'status_id' => OrderStatus::COMPLETED,
        ]);

        $this->assertDatabaseHas(Order::class, [
            'pickup_date' => $newPickupDate,
            'confirmed' => false,
            'processed' => false,
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function an_admin_can_replace_a_recurring_order(): void
    {
        Carbon::setTestNow(now());

        $blueprint = RecurringOrder::factory()->create(['created_at' => now()->subDays(2)]);

        /** @var Order $order */
        $order = Order::factory()->create([
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'is_recurring' => true,
            'confirmed' => true,
            'processed' => true,
            'status_id' => OrderStatus::COMPLETED,
            'pickup_date' => now(),
            'created_at' => now()->subDays(2)
        ]);


        $newPickupDate = now()->addDays(5)->toDateString();

        Livewire::test(OrderReplacement::class)
            ->dispatch('open-modal-order-replacement', $order->id)
            ->assertStatus(200)
            ->set('delivery_date', $newPickupDate)
            ->call('submit')
            ->assertHasNoErrors()
            ->assertSet('open', false);

        $order->refresh();

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'pickup_date' => now()->format('Y-m-d'),
            'blueprint_id' => $blueprint->id,
            'customer_id' => $blueprint->customer_id,
            'is_recurring' => true,
            'confirmed' => true,
            'processed' => true,
            'status_id' => OrderStatus::COMPLETED,
            'created_at' => now()->subDays(2)
        ]);


        $this->assertDatabaseHas(Order::class, [
            'pickup_date' => $newPickupDate,
            'confirmed' => false,
            'processed' => false,
            'is_recurring' => null,
        ]);

        Carbon::setTestNow();
    }

}
