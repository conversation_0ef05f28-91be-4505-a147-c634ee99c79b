<?php

namespace Tests\Feature\Livewire\Admin;

use App\Livewire\Admin\SubscriptionProductSelect;
use App\Models\Product;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Collection;
use Livewire\Livewire;
use Mo<PERSON>y\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class SubscriptionProductSelectTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $promo_products = Product::factory(2)->create();

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($promo_products) {
            $mock->shouldReceive('productIncentiveIds')->twice()->andReturn($promo_products->map->id);
        });

        Livewire::test(SubscriptionProductSelect::class)
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->doesntContain($promo_products[0]->id))
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->doesntContain($promo_products[1]->id));
    }

    #[Test]
    function it_can_render_with_an_initial_product(): void
    {
        $product = Product::factory()->create();

        Livewire::test(SubscriptionProductSelect::class, ['selected_product_id' => $product->id])
            ->assertStatus(200)
            ->assertViewHas('selected_product_id', fn(int $arg) => $arg === $product->id)
            ->assertViewHas('selected_product', fn(Product $arg) => $arg->id === $product->id);
    }

    #[Test]
    public function it_can_filter_products_by_title_term(): void
    {
        $promo_product = Product::factory()->create(['title' => 'promo-title', 'sku' => '']);
        $other_product = Product::factory()->create(['title' => 'other-title', 'sku' => '']);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($promo_product) {
            $mock->shouldReceive('productIncentiveIds')->andReturn(collect([$promo_product->id]));
        });

        Livewire::test(SubscriptionProductSelect::class)
            ->set('term', 'romo')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 1)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 0)
            ->set('term', 'ther')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 0)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 1)
            ->set('term', 'title')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 1)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 1);
    }

    #[Test]
    public function it_can_filter_products_by_sku_term(): void
    {
        $promo_product = Product::factory()->create(['title' => 'promo-title', 'sku' => 'promo-sku']);
        $other_product = Product::factory()->create(['title' => 'other-title', 'sku' => 'other-sku']);

        $this->mock(SubscriptionSettingsService::class, function (MockInterface $mock) use ($promo_product) {
            $mock->shouldReceive('productIncentiveIds')->andReturn(collect([$promo_product->id]));
        });

        Livewire::test(SubscriptionProductSelect::class)
            ->set('term', 'mo-s')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 1)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 0)
            ->set('term', 'er-s')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 0)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 1)
            ->set('term', 'sku')
            ->assertStatus(200)
            ->assertViewHas('current_promo_products', fn(Collection $arg) => $arg->count() === 1)
            ->assertViewHas('other_products', fn(Collection $arg) => $arg->count() === 1);
    }

    #[Test]
    function it_can_select_a_product(): void
    {
        $product = Product::factory()->create();

        Livewire::test(SubscriptionProductSelect::class)
            ->call('selectProduct', $product->id)
            ->assertStatus(200)
            
            ->assertViewHas('selected_product', fn(Product $arg) => $arg->id === $product->id)
            ->assertViewHas('open', fn(bool $arg) => $arg === false);
    }
}
