<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    public function definition(): array
    {
        return [
            'slug' => $this->faker->unique()->slug(),
            'name' => $this->faker->word(),
            'description' => $this->faker->sentence(),
            'category_id' => null,
        ];
    }

    public function childCategory(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category_id' => Category::class,
            ];
        });
    }
}
