<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Menu;
use Illuminate\Support\Str;

class MenuFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'title' => $title = $this->faker->unique()->md5(),
            'name' =>  Str::slug($title),
            'submenu' => false,
            'parent_id' => 0
        ];
    }
}
