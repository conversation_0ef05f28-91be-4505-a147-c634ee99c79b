<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('profiles', function (Blueprint $table) {
            $table->unsignedInteger('user_id')->change();

            $table->longtext('bio')->nullable()->change();
            $table->string('position_title', 255)->nullable()->change();
            $table->string('facebook', 255)->nullable()->change();
            $table->string('twitter', 255)->nullable()->change();
            $table->string('linkedin', 255)->nullable()->change();
            $table->text('cover_photo')->nullable()->change();

            $table->renameColumn('cover_photo', 'photo_path');

            $table->dropColumn(['google', 'website', 'start_date', 'end_date']);
        });
    }
};
