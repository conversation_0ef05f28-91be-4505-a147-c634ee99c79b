<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dates', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('schedule_id')->unsigned();
            $table->integer('user_id')->unsigned()->nullable();
            $table->date('order_start_date');
            $table->date('order_end_date');
            $table->date('pickup_date');
            $table->text('notes');
            $table->boolean('active')->default(true);
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('secondary_scheduled_at')->nullable();
            $table->boolean('reminder_sent')->default(false);
            $table->boolean('secondary_reminder_sent')->default(false);
            $table->timestamps();

            $table->foreign('schedule_id')
                ->references('id')
                ->on('schedules')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('dates');
    }
};
