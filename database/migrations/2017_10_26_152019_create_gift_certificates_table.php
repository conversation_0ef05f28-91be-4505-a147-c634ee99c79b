<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('gift_certificates', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('product_id')->unsigned()->nullable();
            $table->integer('order_item_id')->unsigned();
            $table->string('code')->unique();
            $table->integer('amount')->unsigned();
            $table->integer('qty')->unsigned()->default(1);
            $table->string('issuer_name')->nullable();
            $table->string('redeemer_name')->nullable();
            $table->integer('issuer_id')->unsigned();
            $table->integer('redeemer_id')->unsigned()->nullable();
            $table->string('redeemer_email')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(false);
            $table->timestamp('redeemed')->nullable();
            $table->timestamp('expiration_date')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('order_item_id')
                ->references('id')->on('order_items')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gift_certificates');
    }
};
