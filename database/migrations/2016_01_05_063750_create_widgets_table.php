<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widgets', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('page_id')->unsigned()->index();
            $table->integer('container_id')->unsigned()->nullable();
            $table->boolean('enabled');
            $table->string('title');
            $table->boolean('visible')->default(true);
            $table->string('description')->nullable();
            $table->string('template');
            $table->text('content');
            $table->text('settings')->nullable();
            $table->integer('sort')->unsigned();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('widgets');
    }
};
