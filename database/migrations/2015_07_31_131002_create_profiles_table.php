<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProfilesTable extends Migration
{
    public function up()
    {
        Schema::dropIfExists('profiles');
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->string('slug', 255)->nullable();
            $table->string('position_title', 255);
            $table->longtext('bio');
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('facebook', 255);
            $table->string('twitter', 255);
            $table->string('linkedin', 255);
            $table->string('google', 255);
            $table->string('website', 255);
            $table->string('cover_photo', 255);
            $table->timestamps();
        });
    }
}
