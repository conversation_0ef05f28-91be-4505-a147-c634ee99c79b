<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_cards', function (Blueprint $table) {
            $table->dropColumn('customer_id');
            $table->dropColumn('gateway_id');
            $table->dropColumn('brand');
            $table->dropColumn('exp_month');
            $table->dropColumn('exp_year');
            $table->dropColumn('last_four');
            $table->dropColumn('cardholder_name');
            $table->dropColumn('address_city');
            $table->dropColumn('address_country');
            $table->dropColumn('address_line1');
            $table->dropColumn('address_line2');
            $table->dropColumn('address_state');
            $table->dropColumn('address_zip');
            $table->dropColumn('deleted_at');
        });
    }
};
