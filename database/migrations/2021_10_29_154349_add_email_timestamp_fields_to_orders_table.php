<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->timestamp('processed_email_sent_at')->nullable()->after('first_time_order');
            $table->timestamp('confirmation_email_sent_at')->nullable()->after('first_time_order');
            $table->timestamp('packed_email_sent_at')->nullable()->after('first_time_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('packed_email_sent_at');
            $table->dropColumn('confirmation_email_sent_at');
            $table->dropColumn('processed_email_sent_at');
        });
    }
};
