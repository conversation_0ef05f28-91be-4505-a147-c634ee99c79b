@extends('settings.layout', ['settingTitle' => 'Email & Notifications'])

@section('setting_toolbar')
    <button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
    <form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
        @csrf
        @method('PUT')
        <div class="panel">
            <div class="panel-body pa-0">
                <table class="table   table-striped table-settings">
                    <tbody>
                    {{--Store email--}}
                    <tr>
                        <td>
                            <h2>Reply-To Email Address</h2>
                            <p>Set a reply-to email address used on emails sent from GrazeCart. This will also be the email address where any configured admin
                                notifications are set.<br>The sender name of your emails is your configrued farm's name. The sender email address is
                                <EMAIL></p>
                        </td>
                        <td>
                            <input type="text" name="settings[email_general]" class="form-control"
                                   value="{{ old('email_general', setting('email_general')) }}" />
                        </td>
                    </tr>

                    {{--Support email--}}
                    <tr>
                        <td>
                            <h2>Contact Form Email Address</h2>
                            <p>The email address that the contact form will send to.</p>
                        </td>
                        <td>
                            <input type="text" name="settings[email_contact]" class="form-control"
                                   value="{{ old('email_contact', setting('email_contact')) }}" />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
            </div>
        </div>

        <div class="panel">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">
                    Admin Notifications
                </div>
            </div>
            <div class="panel-body br-sm br-t pa-0">
                <table class="table table-settings table-striped">
                    <tbody>
                    <tr>
                        <td>
                            <h2>New Order Notification</h2>
                            <p>Send an email notification to {{ setting('email_general') }} when a customer places an order.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[notify_of_new_orders]" value="1" @if(setting('notify_of_new_orders')) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[notify_of_new_orders]" value="0" @if(!setting('notify_of_new_orders')) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Order Comment Notification</h2>
                            <p>Send an email notification to {{ setting('email_general') }} when a customer adds a comment to their order.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[notify_of_order_comment]" value="1" @if(setting('notify_of_order_comment', true)) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[notify_of_order_comment]" value="0" @if(!setting('notify_of_order_comment', true)) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Order Cancellation Notification</h2>
                            <p>Send an email notification to {{ setting('email_general') }} when a customer canceles an order.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[notify_of_canceled_orders]" value="1" @if(setting('notify_of_canceled_orders')) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[notify_of_canceled_orders]" value="0" @if(!setting('notify_of_canceled_orders')) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Proposal Notification</h2>
                            <p>Send an email notification to {{ setting('email_general') }} when someone submits a pickup proposal.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[notify_of_new_proposals]" value="1" @if(setting('notify_of_new_proposals')) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[notify_of_new_proposals]" value="0" @if(!setting('notify_of_new_proposals')) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Out of Stock SMS Notifications</h2>
                            <p>Send SMS alerts to the configured phone numbers when inventory for any product reaches 0 or negative. Alerts are sent only when
                                inventory reaches consumed through store purchases and/or subscription generation.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[out_of_stock_sms_notifications]" value="1" @checked(setting('out_of_stock_sms_notifications'))>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[out_of_stock_sms_notifications]" value="0" @checked(!setting('out_of_stock_sms_notifications'))>
                                    No
                                </label>
                            </div>
                            <div class="mt-2">
                                <input type="text" name="settings[out_of_stock_sms_notifications_contacts]" placeholder="Enter the phone numbers separated by commas"
                                       class="form-control" value="{{ old('out_of_stock_sms_notifications_contacts', setting('out_of_stock_sms_notifications_contacts')) }}" />
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button type="submit" class="btn btn-action" @click.prevent="submitForm('settingsForm')">Save</button>
            </div>
        </div>

        <div class="panel">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">
                    Customer Notifications
                </div>
            </div>
            <div class="panel-body br-sm br-t pa-0">
                <table class="table table-settings table-striped">
                    <tbody>
                    <tr>
                        <td>
                            <h2>New User Welcome</h2>
                            <p>Send the welcome email to a user when they create their account.</p>
                        </td>
                        <td>
                            <div class="radio">
                                <label class="mr-sm">
                                    <input tabindex="1" type="radio" name="settings[send_customer_welcome_email]" value="1" @if(setting('send_customer_welcome_email', true)) checked @endif>
                                    Yes
                                </label>
                                <label>
                                    <input tabindex="1" type="radio" name="settings[send_customer_welcome_email]" value="0" @if(!setting('send_customer_welcome_email', true)) checked @endif>
                                    No
                                </label>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button type="submit" class="btn btn-action" @click.prevent="submitForm('settingsForm')">Save</button>
            </div>
        </div>

        <div class="panel">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">
                    Default Email Templates
                </div>
                <a href="/admin/templates" class="btn btn-alt btn-sm push-right">Edit Templates <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="panel-body pa-0">
                <table class="table   table-settings table-striped">
                    <tbody>
                    <tr>
                        <td>
                            <h2>Order Confirmation</h2>
                            <p>The template used for order confirmation emails.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_order_confirmation_template]"
                                    placeholder="- Select an email template -"
                                    :selected="(int) setting('email_order_confirmation_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Order Packed</h2>
                            <p>The template to use for order packed emails.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_order_packed_template]"
                                    placeholder="- Select an email template -"
                                    :selected="(int) setting('email_order_packed_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Order Cancellation</h2>
                            <p>The template used for order cancellation emails.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_order_cancellation_template]"
                                    placeholder="- Select an email template -"
                                    :selected="(int) setting('email_order_cancellation_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Card Declined</h2>
                            <p>The template used for card declined emails.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_card_declined_template]"
                                    placeholder="- Select an email template -"
                                    :selected="(int) setting('email_card_declined_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>New User Welcome</h2>
                            <p>The template used for new user welcome emails.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_customer_welcome_template]"
                                    placeholder="- Select an email template -"
                                    :selected="(int) setting('email_customer_welcome_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Gift Card Issued</h2>
                            <p>The template used when sending issued gift card emails. The email is sent for each individual gift card code.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_issued_gift_card_template]"
                                    placeholder="Default Template"
                                    :selected="(int) setting('email_issued_gift_card_template')"
                            />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Gift Purchased</h2>
                            <p>The template sent to the recipient of a gift order.</p>
                        </td>
                        <td>
                            <x-form.email-template-select
                                    class="form-control"
                                    name="settings[email_gift_purchased_template]"
                                    placeholder="Default Template"
                                    :selected="(int) setting('email_gift_purchased_template')"
                            />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button type="submit" class="btn btn-action" @click.prevent="submitForm('settingsForm')">Save</button>
            </div>
        </div>
    </form>
@stop
