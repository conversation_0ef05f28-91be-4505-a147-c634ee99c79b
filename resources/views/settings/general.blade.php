@php use App\Services\SettingsService; @endphp

@extends('settings.layout', ['settingTitle' => 'General'])

@section('setting_toolbar')
    <button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('setting_content')
    <form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
        @csrf
        @method('PUT')
        <div class="panel">
            <div class="panel-heading">Farm Information</div>
            <div class="panel-body pa-0">
                <table class="table   table-striped table-settings">
                    <tbody>
                    {{--Farm name--}}
                    <tr>
                        <td>
                            <h2>Farm Name</h2>
                            <p>The name of your farm as it will appear on invoices and throughout your site.</p>
                        </td>
                        <td>
                            <input type="text" name="settings[farm_name]" class="form-control" value="{{ old('farm_name', setting('farm_name')) }}" />
                        </td>
                    </tr>
                    {{--Farm phone--}}
                    <tr>
                        <td>
                            <h2>Phone</h2>
                            <p>The phone number that will be visible on your website.</p>
                        </td>
                        <td>
                            <input type="text" name="settings[farm_phone]" class="form-control"
                                   value="{{ old('farm_phone', setting('farm_phone')) }}" />
                        </td>
                    </tr>

                    {{--Farm street--}}
                    <tr>
                        <td>
                            <h2>Street</h2>
                            <p>The street address of your farm as it will appear on invoices.</p>
                        </td>
                        <td>
                            <input type="text" name="settings[farm_street]" class="form-control"
                                   value="{{ old('farm_street', setting('farm_street')) }}" />
                        </td>
                    </tr>
                    {{--Farm city--}}
                    <tr>
                        <td>
                            <h2>City</h2>
                        </td>
                        <td>
                            <input type="text" name="settings[farm_city]" class="form-control"
                                   value="{{ old('farm_city', setting('farm_city')) }}" />
                        </td>
                    </tr>
                    {{--Farm state--}}
                    <tr>
                        <td>
                            <h2>State</h2>
                        </td>
                        <td>
                            <x-form.state-select
                                    class="form-control"
                                    name="settings[farm_state]"
                                    :selected="setting('farm_state')"
                            />
                        </td>
                    </tr>
                    {{--Farm zip--}}
                    <tr>
                        <td>
                            <h2>Zip</h2>
                        </td>
                        <td>
                            <input type="text" name="settings[farm_zip]" class="form-control"
                                   value="{{ old('farm_zip', setting('farm_zip')) }}" />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Country</h2>
                        </td>
                        <td>
                            <x-form.country-select
                                    class="form-control"
                                    name="settings[farm_country]"
                                    :selected="app(SettingsService::class)->farmCountry()"
                            />
                        </td>
                    </tr>
                    {{--Timezone--}}
                    <tr>
                        <td>
                            <h2>Timezone</h2>
                        </td>
                        <td>
                            <select class="form-control" name="settings[timezone]">
                                @php $selected_timezone = setting('timezone'); @endphp
                                @foreach([
                                    'America/New_York' => 'Eastern',
                                    'America/Chicago' => 'Central',
                                    'America/Denver' => 'Mountain',
                                    'America/Phoenix' => 'Mountain no DST',
                                    'America/Los_Angeles' => 'Pacific',
                                    'America/Anchorage' => 'Alaska',
                                    'America/Adak' => 'Hawaii',
                                    'Pacific/Honolulu' => 'Hawaii no DST'
                                ] as $timezone => $label)
                                    <option value="{{ $timezone }}" @selected($selected_timezone === $timezone)>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Language (beta)</h2>
                        </td>
                        <td>
                            <select class="form-control" name="settings[local]">
                                {!! selectOptions([
                                    'en' => 'English',
                                    'es' => 'Spanish'
                                ], setting('local', 'en')) !!}
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Preferred Domain</h2>
                        </td>
                        <td>
                            <x-form.preferred-domain-select
                                    class="form-control"
                                    name="settings[preferred_domain]"
                                    :selected="setting('preferred_domain')"
                            />
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="panel-footer text-right">
                    <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
                </div>
            </div>
        </div>

        <div class="panel">
            <div class="panel-heading">Pickup Locations Map</div>
            <div class="panel-body pa-0">
                <table class="table   table-striped table-settings">
                    <tbody>
                    <tr>
                        <td>
                            <h2>Map Center</h2>
                            <p>The latitude and longitude for the center of the pickup locations map.</p>
                        </td>
                        <td>
                            <div class="form-group">
                                <label>Latitude</label>
                                <input type="text" name="settings[map_center_latitude]" class="form-control" value="{{ old('map_center_latitude', isset(mapCenter()[0])) ? trim(mapCenter()[0]) : '' }}">
                            </div>
                            <div class="form-group">
                                <label>Longitude</label>
                                <input type="text" name="settings[map_center_longitude]" class="form-control" value="{{ old('map_center_longitude', isset(mapCenter()[1])) ? trim(mapCenter()[1]) : '' }}">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Map Zoom Level</h2>
                            <p>The default zoom level of the pickup locations map. The higher the number to more zoomed in.</p>
                        </td>
                        <td>
                            <label>Zoom Level</label>
                            <x-form.map-zoom-select
                                    class="form-control"
                                    name="settings[map_center_zoom]"
                                    :selected="(int) setting('map_center_zoom', 10)"
                            />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="panel-footer text-right">
                <button class="btn btn-action" @click="submitForm('settingsForm', $event)">Save</button>
            </div>
        </div>
    </form>
@stop
