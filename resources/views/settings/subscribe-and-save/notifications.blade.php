@php /** @var \App\Services\SubscriptionSettingsService $subscription_settings_service */ @endphp

<div class="space-y-6 pb-12 sm:px-6 lg:col-span-9 lg:px-0">
    <form action="{{ route('admin.integrations.subscribe-save.update') }}" method="POST">
        @csrf
        @method('PUT')
        <div class="shadow sm:overflow-hidden sm:rounded-lg">
            <div class="space-y-6 bg-white py-6 px-4 sm:p-6">
                <div class="md:grid md:grid-cols-3 md:gap-6">
                    <div class="md:col-span-1">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Notifications</h3>
                        <p class="mt-1 text-sm text-gray-500">Set which communications subscription customers receive and how.</p>
                    </div>
                    <div class="mt-5 space-y-6 md:col-span-2 md:mt-0">
                        <div class="grid grid-cols-3 gap-6">
                            <div class="col-span-3 space-y-6 divide-y divide-gray-200">
                                <div>
                                    <p class="m-0 contents text-base font-medium text-gray-900">Welcome</p>
                                    <p class="m-0 text-sm text-gray-500">The notification sent to a new subscriber upon placing their first subscription order.
                                        Sends in replacement of the standard subscription confirmation.</p>
                                    <div class="mt-6 grid grid-cols-2 gap-6">
                                        <div class="col-span-2 sm:col-span-1">
                                            <div class="flex justify-between">
                                                <label for="recurring_orders_welcome_email_template_id" class="block text-sm font-medium text-gray-700">Email</label>
                                                <span class="text-sm text-gray-500">Enabled</span>
                                            </div>
                                            <x-form.email-template-select
                                                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm"
                                                    name="settings[recurring_orders_welcome_email_template_id]"
                                                    id="recurring_orders_welcome_email_template_id"
                                                    placeholder="Default template"
                                                    placeholder_value="default"
                                                    :selected="$subscription_settings_service->welcomeEmailTemplateId()"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div class="pt-6">
                                    <p class="m-0 contents text-base font-medium text-gray-900">Confirmation</p>
                                    <p class="m-0 text-sm text-gray-500">The notification(s) sent to subscribers when the subscription advances and the next
                                        order is generated.</p>
                                    <div class="mt-6 grid grid-cols-2 gap-6">
                                        <div class="col-span-2 sm:col-span-1">
                                            <div class="flex justify-between">
                                                <label for="recurring_orders_reorder_email_template_id" class="block text-sm font-medium text-gray-700">Email</label>
                                                <span class="text-sm text-gray-500">Enabled</span>
                                            </div>
                                            <x-form.email-template-select
                                                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm"
                                                    name="settings[recurring_orders_reorder_email_template_id]"
                                                    id="recurring_orders_reorder_email_template_id"
                                                    placeholder="Default template"
                                                    placeholder_value="default"
                                                    :selected="(int) $subscription_settings_service->confirmationEmailTemplateId()"
                                            />
                                        </div>

                                        <div class="col-span-2 sm:col-span-1">
                                            <div class="flex justify-between">
                                                <label for="recurring_orders_reorder_sms_template_id" class="block text-sm font-medium text-gray-700">SMS</label>
                                                <div>
                                                    <input type="hidden" name="settings[recurring_orders_reorder_sms_enabled]" value="0">
                                                    <input id="recurring_orders_reorder_sms_enabled" type="checkbox" class="switch disable-gray" name="settings[recurring_orders_reorder_sms_enabled]" value="1" @if($subscription_settings_service->confirmationSmsIsEnabled()) checked @endif>
                                                </div>
                                            </div>
                                            <select name="settings[recurring_orders_reorder_sms_template_id]" id="recurring_orders_reorder_sms_template_id" class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                                                <option value="default" selected>Default template</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="pt-6">
                                    <p class="m-0 contents text-base font-medium text-gray-900">Reminder</p>
                                    <p class="m-0 text-sm text-gray-500">The notification(s) automatically sent to subscribers before:</p>
                                    <ul class="pl-6 mt-1 text-sm list-disc list-outside">
                                        <li>the current subscription order is longer editable by the customer</li>
                                        <li>the next subscription order is generated and editable</li>
                                    </ul>
                                    <div class="mt-6 grid grid-cols-2 gap-6">
                                        <div class="col-span-2 sm:col-span-1">
                                            <div class="flex justify-between">
                                                <label for="recurring_orders_deadline_email_template_id" class="block text-sm font-medium text-gray-700">Email</label>
                                                <div>
                                                    <input type="hidden" name="settings[recurring_orders_deadline_email_enabled]" value="0">
                                                    <input id="recurring_orders_deadline_email_enabled" type="checkbox" class="switch disable-gray" name="settings[recurring_orders_deadline_email_enabled]" value="1" @if($subscription_settings_service->deadlineEmailIsEnabled()) checked @endif>
                                                </div>
                                            </div>
                                            <x-form.email-template-select
                                                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm"
                                                    name="settings[recurring_orders_deadline_email_template_id]"
                                                    id="recurring_orders_deadline_email_template_id"
                                                    placeholder="Default template"
                                                    placeholder_value="default"
                                                    :selected="(int) $subscription_settings_service->deadlineEmailTemplateId()"
                                            />
                                        </div>

                                        <div class="col-span-2 sm:col-span-1">
                                            <div class="flex justify-between">
                                                <label for="recurring_orders_deadline_sms_template_id" class="block text-sm font-medium text-gray-700">SMS</label>
                                                <div>
                                                    <input type="hidden" name="settings[recurring_orders_deadline_sms_enabled]" value="0">
                                                    <input id="recurring_orders_deadline_sms_enabled" type="checkbox" class="switch disable-gray" name="settings[recurring_orders_deadline_sms_enabled]" value="1" @if($subscription_settings_service->deadlineSmsIsEnabled()) checked @endif>
                                                </div>
                                            </div>
                                            <select name="settings[recurring_orders_deadline_sms_template_id]" id="recurring_orders_deadline_sms_template_id" class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                                                <option value="default" selected>Default template</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mt-6 ">
                                        <p class="m-0 contents text-sm font-medium text-gray-900">Timeline</p>
                                        <p class="m-0 text-sm text-gray-500">Set how soon before the subscription order deadline these notifications are
                                            sent.</p>
                                        <div class="mt-2 grid grid-cols-2 gap-6">
                                            <div class="col-span-2 sm:col-span-1">
                                                <label for="recurring_orders_deadline_days_before" class="block text-sm font-medium text-gray-700">Day(s)
                                                    before</label>
                                                <input type="number" name="settings[recurring_orders_deadline_days_before]" id="recurring_orders_deadline_days_before" value="{{ old('recurring_orders_deadline_days_before', $subscription_settings_service->deadlineReminderSendsDaysBefore()) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm">
                                            </div>

                                            <div class="col-span-2 sm:col-span-1">
                                                <label for="recurring_orders_deadline_days_before" class="block text-sm font-medium text-gray-700">Hour(s)
                                                    before</label>
                                                <input type="number" name="settings[recurring_orders_deadline_hours_before]" id="recurring_orders_deadline_hours_before" value="{{ old('recurring_orders_deadline_days_before', $subscription_settings_service->deadlineReminderSendsHoursBefore()) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-keppel-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-keppel-700 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">
                    Save
                </button>
            </div>
        </div>
    </form>
</div>
