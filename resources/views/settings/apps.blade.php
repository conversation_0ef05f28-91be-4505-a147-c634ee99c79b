@extends('settings.layout', ['settingTitle' => 'Marketplace'])

@section('setting_content')
    <div class="panel">
        <div class="panel-heading">Integration Apps</div>
        <div class="panel-body">
            <ul class="settings-list">
                <li>
                    <div class="flex align-items-m pa-sm">
                        <div class="flex-item mr-md">
                            <a href="/admin/settings/integrations/drip">
                                <img src="{{ config('integrations.drip.logo') }}" alt="{{ config('integrations.drip.title') }} logo" style="height: 25px;">
                            </a>
                        </div>
                        <div class="flex-item">
                            <a href="/admin/settings/integrations/drip" class="btn-alt bold">{{ config('integrations.drip.title') }}</a>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="flex align-items-m pa-sm">
                        <div class="flex-item mr-md">
                            <a href="/admin/settings/google-analytics">
                                <img src="{{ config('integrations.google-analytics.logo') }}" alt="{{ config('integrations.google-analytics.title') }} logo" style="height: 25px;">
                            </a>
                        </div>
                        <div class="flex-item">
                            <a href="/admin/settings/google-analytics" class="btn-alt bold">{{ config('integrations.google-analytics.title') }}</a>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="flex align-items-m pa-sm">
                        <div class="flex-item mr-md">
                            <a href="/admin/settings/integrations/attentive">
                                <img src="{{ config('integrations.attentive.logo') }}" alt="{{ config('integrations.attentive.title') }} logo" style="height: 25px;">
                            </a>
                        </div>
                        <div class="flex-item">
                            <a href="/admin/settings/integrations/attentive" class="btn-alt bold">{{ config('integrations.attentive.title') }}</a>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="panel">
        <div class="panel-heading">GrazeCart Apps</div>
        <div class="panel-body">
            Read the <a href="https://help.grazecart.com/hc/en-us/categories/360004577751-Add-On-Marketplace" target="_blank">docs</a> or
            <a href="mailto:<EMAIL>">contact</a> support to request access!
            <ul class="pt-lg settings-list">
                <li>
                    <a href="/admin/pickup-manager"><i class="far fa-truck"></i> Pick-up Manager</a>
                </li>
                <li>
                    <a href="/admin/tags?type=2"><i class="far fa-tags"></i> Order Tags</a>
                </li>
                <li>
                    <a href="{{ route('admin.settings.subscriptions.index') }}"><i class="fas fa-sync-alt"></i>Subscribe & Save</a>
                </li>
            </ul>
        </div>
    </div>
@stop
