@php use Illuminate\Support\Carbon; @endphp
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>{{ route('homepage.show') }}</loc>
    </url>
    @foreach($pages as $page)
        @php /** @var \App\Models\Page $page */ @endphp
        <url>
            <loc>{{ route('page.show', [$page->slug]) }}</loc>
            <lastmod>{{ $page->updated_at->format('c') }}</lastmod>
        </url>
    @endforeach
    <url>
        <loc>{{ route('blog.index') }}</loc>
    </url>
    @foreach($posts as $post)
        @php /** @var \App\Models\Post $post */ @endphp
        <url>
            <loc>{{ route('blog.show', [$post->slug]) }}</loc>
            <lastmod>{{ $post->updated_at->format('c') }}</lastmod>
        </url>
    @endforeach
    @foreach($authors as $author)
        @php /** @var \App\Models\User $author */ @endphp
        <url>
            <loc>{{ route('blog.authors.show', [$author->profile?->slug ?? $author->authorSlug()]) }}</loc>
            <lastmod>{{ Carbon::parse($author->most_recent_post_at)->format('c') }}</lastmod>
        </url>
    @endforeach
    <url>
        <loc>{{ route('recipes.index') }}</loc>
    </url>
    @foreach($recipes as $recipe)
        @php /** @var \App\Models\Recipe $recipe */ @endphp
        <url>
            <loc>{{ route('recipes.show', [$recipe->slug]) }}</loc>
            <lastmod>{{ $recipe->updated_at->format('c') }}</lastmod>
        </url>
    @endforeach
    <url>
        <loc>{{ route('store.index') }}</loc>
        <changefreq>weekly</changefreq>
    </url>
    @foreach($categories as $category)
        @php /** @var \App\Models\Category $category */ @endphp
        <url>
            <loc>{{ route('store.categories.show', [$category->slug]) }}</loc>
            <lastmod>{{ $category->updated_at->format('c') }}</lastmod>
        </url>

        @foreach($category->subcategories as $subcategory)
            @php /** @var \App\Models\Category $subcategory */ @endphp
            <url>
                <loc>{{ route('store.subcategories.show', [$category->slug, $subcategory->slug]) }}</loc>
                <lastmod>{{ $subcategory->updated_at->format('c') }}</lastmod>
            </url>
        @endforeach
    @endforeach
    @foreach($collections as $collection)
        @php /** @var \App\Models\Collection $collection */ @endphp
        <url>
            <loc>{{ route('store.collections.show', [$collection->slug]) }}</loc>
            <lastmod>{{ $collection->updated_at->format('c') }}</lastmod>
        </url>
    @endforeach
    @foreach($products as $product)
        @php /** @var \App\Models\Product $product */ @endphp
        <url>
            <loc>{{ route('store.show', [$product->slug]) }}</loc>
            <lastmod>{{ $product->updated_at->format('c') }}</lastmod>
        </url>
    @endforeach
</urlset>
