@php
    /** @var App\Models\Category $category */
    $isSubcategory = $category->isSubcategory();
@endphp

@extends('layouts.main', ['pageTitle' => $isSubcategory ? 'Subcategory' : 'Category' . ' #'.$category->id])

@section('toolbar-breadcrumb')
    <li><a href="{{ route('admin.categories.index') }}">Categories</a></li>
    @if($isSubcategory)
        <li>
            <a href="{{ route('admin.categories.edit', [$category->parentCategory->id]) }}"> {{ $category->parentCategory->name }}</a>
        </li>
    @endif
    <li>{{ $category->name }}</li>
@endsection

@section('content')
    <livewire:admin.category-show :category="$category"/>
@endsection
