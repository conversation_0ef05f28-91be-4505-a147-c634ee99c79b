@php /** @var \App\Models\Order $order */ @endphp
<table style="border-collapse: collapse; margin-top: 8px; margin-left: auto;">
    <tr style="padding: 3px 0px;">
        <td style="text-align: right;">Subtotal:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->subtotal) }}</td>
    </tr>
    <tr style="padding: 3px 0px;">
        <td style="text-align: right;">Fees:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->fees_subtotal) }}</td>
    </tr>
    <tr style="padding: 3px 0px;">
        <td style="text-align: right;">Tax:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->tax) }}</td>
    </tr>
    <tr style="padding: 3px 0px;">
        <td style="text-align: right;">Discounts &amp; Credit:</td>
        <td style="text-align: right; width: 150px;">-&#36;{{ money($order->order_discount + $order->credit_applied + $order->coupon_subtotal) }}</td>
    </tr>
    <tr style="font-size: 1.1em; padding: 3px 0px;">
        <td style="text-align: right;">Total:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->total) }}</td>
    </tr>
    <tr style="font-size: 1.1em; padding: 3px 0px;">
        <td style="text-align: right;">Paid:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->payments_subtotal) }}</td>
    </tr>
    <tr style="font-weight: bolder; font-size: 1.1em; padding: 3px 0px;">
        <td style="text-align: right;">Total Due:</td>
        <td style="text-align: right; width: 150px;">&#36;{{ money($order->total_due) }}</td>
    </tr>
</table>