<div class="panel ">
<div class="panel-heading">Products in Collection (@{{ productsInCollection.length }})</div>
<div class="panel-body">
    <div class="form-group">
        <label>Order Products By:</label>
        <select class="form-control" v-on="change: sortCollection" options="sortOptions" v-model="sortOrder"></select>
    </div>

    <label>Add Product:</label>
    <div class="select">
        <input type="text" class="form-control" autocomplete="off" v-model="q" v-on="focus: showResults = true, blur: showResults = false" placeholder="search products to add to collection" tabindex="1">
        <ul class="select-results" v-if="showResults">
            <li v-show="!filteredCount">
                <em>No results found</em>
            </li>
            <li v-repeat="product: products | filterBy q | count" track-by="id">
                <span v-on="mousedown: toggleProduct(product, $event)"><i class="fas fa-lg" v-class="product.active ? 'fa-check-circle-o' : 'fa-circle-o'"></i> @{{ product.title }}</span>
            </li>
        </ul>
    </div>
    <div class="form-group text-right" v-if="sortable">
        <button type="button" class="btn btn-success btn-sm" v-on="click: sortCollectionItems">Save Custom Sort Order</button>
    </div>
    <ul class="list-group" id="productsInCollectionList">
        <li class="list-group-item" v-if="!productsInCollection.length">
            <em>There are no products in this collection. To add products use the search bar above.</em>
        </li>
        <li class="list-group-item" v-repeat="product: results | productsInCollection" track-by="id" v-transition="collection" data-id="@{{ product.id }}">
            <span v-if="sortable" class="collection__product-sort-handle">
                <i class="fas fa-arrows-v"></i>
            </span>
            <a href="/admin/products/@{{ product.id }}/edit">@{{ product.title }}</a> <a href="#" class="pull-right" v-on="click: toggleProduct(product, $event)"><i class="fas fa-times"></i></a>
        </li>
    </ul>
</div>
</div>