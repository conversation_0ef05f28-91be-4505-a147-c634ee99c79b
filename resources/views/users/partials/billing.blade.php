<form action="{{ route('admin.users.update', $user->id) }}" method="POST" id="updateResourceForm">
    @csrf
    @method('put')
    <input type="hidden" name="tab" value="{{ request('tab') }}">
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Default Payment Method</h2>
                        <p>The payment method that will be selected during checkout.</p>
                    </td>
                    <td>
                        <x-form.payment-method-select
                                class="form-control"
                                name="settings[default_payment_method]"
                                tabindex="1"
                                placeholder="N/A"
                                :selected="(int) $user->setting('default_payment_method')"
                        />
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button type="button" class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>
</form>

<div class="panel">
    <div class="panel-heading flex align-items-m">
        <div>Credit Cards</div>
        <button
                type="button"
                class="btn btn-alt btn-sm flex-item push-right pa-0"
                @click="showModal('addCardModal')"
        ><i class="fas fa-plus-circle fa-lg"></i> Add Card
        </button>
    </div>
    <div class="bg-white px-4 py-3">
        @include('users.partials.credit_card')
    </div>
</div>


@include('users.partials.billing.' . setting('payment_gateway', 'stripe'))
