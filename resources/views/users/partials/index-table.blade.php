@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="/admin/users" method="GET" id="filterUsersForm" class="hidden-print">
            <div class="flex align-items-m">
                <button 
                    type="button" 
                    class="btn btn-white flex-item br-right-0 br--l" 
                    @click.stop="showPanel('filterPanel')" 
                    tabindex="1"
                >Filter <span class="hide-mobile">Customers</span> <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input 
                        tabindex="1"  
                        type="text" 
                        class="form-control input-overlay-left br--r" 
                        placeholder="Search customers by name or email..." 
                        name="users" 
                        value="{{ Request::get('users') }}" 
                    >
                </div>
            </div>
            @include('users.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'customer']) 
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-list">
                <thead>
                <tr>
                    <th>{!! sortTable('First', 'first_name') !!}</th>
                    <th>{!! sortTable('Last', 'last_name') !!}</th>
                    <th>{!! sortTable('Email', 'email') !!}</th>
                    <th>{!! sortTable('Orders', 'orders_count') !!}</th>
                    <th>{!! sortTable('Location', 'pickup_point') !!}</th>
                    <th>{!! sortTable('Registered', 'created_at', 'asc') !!}</th>
                    <th>{!! sortTable('Last Ordered', 'last_purchase') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($users as $user)
                    <tr>
                        <td><a href="{{ route('admin.users.edit', $user->id) }}">{{ $user->present()->firstName() }}</a></td>
                        <td>{{ $user->last_name }}</td>
                        <td><a href="mailto:{{ $user->email }}">{{ $user->email }}</a></td>
                        <td data-label="Orders"><a href="/admin/users/{{ $user->id }}/edit?tab=orders">{{ $user->orders_count }}</a></td>
                        <td data-label="Location">{{ $user->present()->pickupLocation() }}</td>
                        <td data-label="Registered">{{ $user->created_at->format('m/d/y') }}</td>
                        <td data-label="Last Order">{{ $user->last_purchase ? $user->last_purchase->format('m/d/y') : 'N/A' }}</td>
                    </tr>
                @endforeach
                </tbody>
                @if(!$users->count())<tr><td colspan="100%">No customers found.</td></tr>@endif
            </table>
        </div>
    </div>   
</div>

<div class="text-center">
    <p>Showing {{ $users->count() }} of {{ $users->total() }} result(s)</p>
    {!! $users->appends(Request::all())->render() !!}
</div>