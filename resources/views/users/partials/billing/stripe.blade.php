@if(stripeIsConnected())
    <div class="gc-modal gc-modal-mask" id="addCardModal" @click="hideModal('addCardModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container gc-modal-container--sm" @click.stop>
                <form x-data="stripeForm" action="/admin/users/{{ $user->id }}/cards" method="POST" id="payment-form">
                    @csrf
                    <div class="gc-modal-header">
                        Add Credit Card
                    </div>

                    <div class="gc-modal-body">
                        <div class="form-group">
                            <label>Cardholder Name</label>
                            <input type="text" class="form-control" name="cardholder_name" value="{{ $user->full_name }}" id="cardholder_name">
                        </div>
                        <div class="form-group border pa-sm pt-xs pb-xs br-sm">
                            <div id="card-element"></div>
                        </div>
                        <div id="card-errors" role="alert"></div>

                        @if(auth()->user()->hasCard())
                            <div class="form-group">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="make_default" value="1">
                                        Make default
                                    </label>
                                </div>
                            </div>
                        @else
                            <input type="hidden" name="make_default" value="1">
                        @endif
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('addCardModal')">Cancel</button>
                        <button type="button" x-on:click="submit" class="btn btn-action" id="payment-form-button">Add Card</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endif

@section('payment_scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        var stripe = Stripe('{{ app(\App\Services\SettingsService::class)->stripePublicKey() }}');
    </script>
@stop
