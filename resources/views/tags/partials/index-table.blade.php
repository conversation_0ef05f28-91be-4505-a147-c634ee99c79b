<div class="panel ">
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full">
                <thead>
                <tr>
                    <th>{!! sortTable('Name', 'title') !!}</th>
                    <th>{!! sortTable('Slug', 'slug') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($tags as $tag)
                    <tr>
                        <td>
                            <a href="{{ route('admin.tags.edit', [$tag->id]) }}">{{ $tag->title }}</a>
                        </td>
                        <td>{{ $tag->slug }}</td>
                    </tr>
                @endforeach
                @if(!$tags->count())
                    <tr>
                        <td colspan="100%"><h4>No tags found.</h4></td>
                    </tr>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="text-center">
    <p>Showing {{ $tags->count() }} of {{ $tags->total() }} result(s)</p>
    {!! $tags->appends(request()->all())->render() !!}
</div>
