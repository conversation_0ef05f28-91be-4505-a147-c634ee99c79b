@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">
        <form action="/admin/orders" method="GET" id="filterOrdersForm" class="hidden-print">
            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Orders</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search by order#, customer name, email, or phone..."
                            name="orders"
                            value="{{ Request::get('orders') }}"
                    >
                </div>
            </div>
            <input type="hidden" name="orderBy" value="{{ request()->get('orderBy', 'confirmed_date') }}">
            <input type="hidden" name="sort" value="{{ request()->get('sort', 'desc') }}">
            @include('orders.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'order', 'showExport' => 'filterOrdersForm'])
    </div>

    <div>
        <div class="pa-md flex justify-between">
            <div class="flex flex-wrap flex-items-m">
                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled}">
                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled }" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        Update Status <i class="fas fa-caret-down"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a href="#" onclick="bulkAction('status', 1)">New</a></li>
                        <li><a href="#" onclick="bulkAction('status', 2)">Processing</a></li>
                        <li><a href="#" onclick="bulkAction('status', 3)">Packed</a></li>
                        <li><a href="#" onclick="bulkAction('status', 4)">Picked Up</a></li>
                        <li><a href="#" onclick="bulkAction('status', 9)">Out For Delivery</a></li>
                        <li>
                        <li><a href="#" onclick="bulkAction('status', 5)">Completed</a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="bulkAction('status', 6)">On Hold</a></li>
                    </ul>
                </div>

                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled }">
                    <button type="button" class="btn btn-sm btn-default dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled }" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-check"></i> Tag <i class="fas fa-caret-down"></i>
                    </button>
                    <ul class="dropdown-menu" style="overflow-y: auto; max-height: 400px;">
                        <li><a href="#" onclick="bulkAction('field','flagged')"><i class="fas fa-fw fa-flag" title="Flagged"></i> Flagged</a></li>
                        <li><a href="#" onclick="bulkAction('field','exported')"><i class="fas fa-fw fa-cloud-download" title="Exported"></i> Exported</a></li>
                        <li><a href="#" onclick="bulkAction('field','paid')"><i class="fas fa-fw fa-check" title="Exported"></i> Paid</a></li>
                        @foreach($orderTags as $orderTag)
                            @if($loop->first)
                                <li class="divider"></li>
                            @endif
                            <li>
                                <a href="#" onclick="bulkAction('tag', '{{ $orderTag->id }}')"><i class="fas fa-fw fa-tag" title="{{ $orderTag->title }}"></i> {{ $orderTag->title }}
                                </a></li>
                        @endforeach
                    </ul>
                </div>

                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled }">
                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled }" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-print"></i> Print <i class="fas fa-caret-down"></i>
                    </button>
                    <ul class="dropdown-menu pull-right">
                        <li><a href="#" onclick="bulkAction('print','invoice', true)">Print Invoices</a></li>
                        <li><a href="#" onclick="bulkAction('print','packing', true)">Print Packing Lists</a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="bulkAction('print','shipping-label', true)">Print Shipping Labels</a></li>
                        <li><a href="#" onclick="bulkAction('print','packing-label', true)">Print Packing Labels</a></li>
                    </ul>
                </div>

                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled}">
                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled}" @click="showModal('customOrderMessageModal')">
                        <i class="fas fa-envelope"></i> Notify
                    </button>
                </div>

                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled && ! selectAllButtonsEnabled}">
                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled && ! selectAllButtonsEnabled}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-cloud-download"></i> Export <i class="fas fa-caret-down"></i>
                    </button>
                    <ul class="dropdown-menu pull-right">
                        <li><a href="#" onclick="bulkAction('export','sales_receipt', false)">QuickBooks Sales Receipt</a></li>
                        <li><a href="#" onclick="bulkAction('export','invoice', false)">QuickBooks Invoice</a></li>
                        @if((bool) setting('plugin_order_export', false))
                            <li><a href="#" onclick="bulkAction('export','tap', false)">Transaction Pro</a></li>
                        @endif
                        <li><a href="#" onclick="bulkAction('export','manifest', false)">Manifest</a></li>
                        <li><a href="#" onclick="bulkAction('export','now_courier', false)">Now Courier</a></li>
                    </ul>
                </div>

                @isset($canBulkProcessOrders)
                    @if($canBulkProcessOrders && setting('process_order_show'))
                        <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled}">
                            <button type="button" class="btn btn-action btn-sm text-white dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled }" onclick="bulkAction('process','orders')">
                                Process
                            </button>
                        </div>
                    @endif
                @endisset
            </div>

            <div>
                <div class="btn-group flex-item mr-sm" :class="{'bulkaction--disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled}">
                    <button type="button" class="btn btn-danger btn-sm text-white dropdown-toggle" :class="{'disabled': ! selectPageButtonsEnabled || selectAllButtonsEnabled }" onclick="bulkAction('cancel','orders')">
                        Cancel
                    </button>
                </div>
            </div>

        </div>
    </div>
    <div class="panel-body pa-0">
        <form action="/admin/orders/bulk-update/message" method="POST" id="bulkActionForm">
            <input type="hidden" name="orderBy" value="{{ request('orderBy') }}">
            <input type="hidden" name="sort" value="{{ request('sort') }}">
            <input type="hidden" name="type" id="selectAllType" value="">
            @csrf
            @method('PUT')
            <div class="table-responsive">
                <table class="table table-striped table-full">
                    <thead>
                    <tr>
                        <th>
                            <bulk-selector
                                    class="-ml-2 -mb-2"
                                    :page-count="{{ $orders->count() }}"
                                    :all-count="{{ $orders->total() }}"
                                    @change="selectedType => toggleBulkCheckSelector('.checkable', selectedType)"
                            />
                        </th>
                        <th>{!! sortTable('Order #', 'id') !!}</th>
                        <th>{!! sortTable('Customer', 'orders.customer_last_name') !!}</th>
                        <th>{!! sortTable('Delivery Method', 'pickup_title') !!}</th>
                        <th>{!! sortTable('Delivery Date', 'orders.pickup_date') !!}</th>
                        <th>{!! sortTable('Weight (lb)', 'weight') !!}</th>
                        <th>
                            <span class="tooltip" title="&#36;{{ $total }}">{!! sortTable('Total', 'orders.total') !!}</span>
                        </th>
                        <th>{!! sortTable('Status', 'orders.status_id') !!}</th>
                        @isset($canBulkProcessOrders)
                            @if($canBulkProcessOrders)
                                <th>Tags</th>
                            @endif
                        @endisset
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($orders as $order)
                        <tr>
                            <td>
                                <input
                                        type="checkbox"
                                        class="checkable"
                                        value="{{ $order->id }}"
                                        name="orders[]"
                                        @click="toggleChecked({{ $order->id }}, $event)"
                                >
                            </td>
                            <td>
                                <a href="{{ route('admin.orders.edit', [$order->id]) }}" class="btn btn-action btn-sm">#{{ $order->id }}</a>
                                <ul class="inline-flex align-items-m mt-sm">
                                    <li class="__{{ (int) $order->confirmed }} flex-item mr-xs">
                                        <span class="label label-light">Unconfirmed</span>
                                    </li>
                                    <li class="_{{ (int) $order->paid }} flex-item mr-xs">
                                        <span class="label label-light">Paid</span>
                                    </li>
                                    <li class="_{{ $order->flagged }} flex-item mr-xs"><i class="fas fa-flag text-gray-5" title="Flagged"></i></li>
                                    <li class="_{{ $order->exported }} flex-item mr-xs"><i class="fas fa-download text-gray-5" title="Exported"></i></li>
                                    <li class="_{{ $order->picked_up }} flex-item mr-xs"><i class="fas fa-user text-gray-5" title="Picked up"></i></li>
                                    <li class="_{{ $order->first_time_order }} flex-item mr-xs"><i class="fas fa-star text-gray-5" title="First time order"></i>
                                    </li>
                                </ul>
                            </td>
                            <td data-label="Customer"><a href="/admin/users/{{ $order->customer_id }}/edit">{{ $order->getCustomerName() }}</a></td>
                            <td data-label="Location" style="max-width: 200px;">{{ $order->pickup_title }}</td>
                            <td data-label="Pickup Date">{{ $order->pickup_date ? $order->pickup_date->format('m/d/y') : 'N/A' }}</td>
                            <td data-label="Weight">{!! $order->weight !!}</td>
                            <td data-label="Total">&#36;{{ number_format($order->total / 100, 2) }}</td>
                            <td data-label="Status">{{ $order->status() }}</td>
                            @isset($canBulkProcessOrders)
                                @if($canBulkProcessOrders)
                                    <td data-label="Tags">
                                        <ul class="tags mt-md">
                                            @foreach($order->tags as $i => $tag)
                                                <li class="tag {{$tag->slug}}">
                                                    <span class="tagTitle ">
                                                        <span class="light">{{ $tag->title }}</span>
                                                    </span>
                                                    &nbsp;
                                                    &nbsp;
                                                </li>
                                            @endforeach
                                        </ul>
                                    </td>
                                @endif
                            @endisset
                        </tr>
                    @endforeach
                    @if(!$orders->count())
                        <tr>
                            <td colspan="100%">No orders found.</td>
                        </tr>
                    @endif
                    </tbody>
                </table>
            </div>
            @include('orders.partials.custom-order-message-modal')
        </form>
    </div>
</div>

<div class="text-center">
    <p>Showing {{ $orders->count() }} of {{ $orders->total() }} result(s)</p>
    {!! $orders->appends(Request::all())->render() !!}
</div>
