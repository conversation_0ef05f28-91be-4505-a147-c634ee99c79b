@if(!$order->isCanceled())
    <ul class="info-boxes">
        {{--Customer Info--}}
        <li>
            <span class="info-box-heading">
                <strong><i class="fas fa-user"></i> <a href="/admin/users/{{ $order->customer_id }}/edit" class="text-gray">{{ $order->getCustomerName() }}</a></strong>
                <small>&nbsp;(<a href="#" @click="showModal('contactInfoModal')">edit</a>)</small>
            </span>
            <a href="mailto:{{ $order->customer_email }}">{{ $order->customer_email }}</a>
            | <a href="tel:{{ $order->customer_phone }}">{{ $order->customer_phone }}</a>
        </li>

        {{--Payment Status--}}
        <li>
            @if($order->is_paid)
                <span class="info-box-heading">
                    <strong><i class="fas fa-check-circle text-success"></i> Paid</strong>&nbsp;
                    <small>&nbsp;(<a href="#" @click="showModal('paymentOrderModal')">edit</a>)</small>
                </span>
                @if($order->payment_date)
                    {{ $order->paymentMethod['title'] ?? "None selected" }} | {{ carbonDate($order->payment_date) }}
                @endif
            @else
                <span x-data class="info-box-heading">
                    <strong><i class="fas fa-times text-danger"></i> Not Paid</strong>
                    <small>&nbsp;(<a href="#" @click="showModal('paymentOrderModal')">edit</a>)</small>
                    <small>&nbsp;(<a href="#" x-on:click="$dispatch('open-modal-order-payment', { order_id: {{ $order->id }} })">edit</a>)</small>

                </span>
                {{ $order->paymentMethod['title'] ?? "None selected" }}

                @if($order->isConfirmed() && $order->paymentMethod)

                    @if($order->paymentMethod->isCard())
                        @if($order->customer && $order->customer->hasCard())
                            <button
                                    type="button"
                                    class="ml-sm btn btn-sm btn-light"
                                    @click="event('OrderPayments:createCardPayment')"
                            ><i class="fas fa-credit-card"></i> Add Payment
                            </button>
                        @else
                            <button
                                    type="button"
                                    disabled
                                    class="btn btn-sm btn-light"
                            >No Card On File
                            </button>
                        @endif
                    @else
                        <button
                                type="button"
                                class="ml-sm btn btn-sm btn-light"
                                @click="event('OrderPayments:createCashPayment')"
                        >Add Payment
                        </button>
                    @endif

                @endif

            @endif
        </li>

        {{--Pickup Point--}}
        <li>
            <span class="info-box-heading"><strong><i class="fas fa-truck"></i> Logistics</strong>&nbsp;<small>(<a href="#" @click="showModal('pickupOrderModal')">edit</a>)</small>
            </span>
            {{ $order->pickup['title'] ?? 'Unknown' }} - {{ carbonDate($order->pickup_date, 'M, jS, Y', '(Date TBA)') }}
        </li>

        {{--Order Date--}}
        <li>
            <span class="info-box-heading"><strong><i class="fas fa-calendar"></i> Date</strong></span>
            {{ $order->present()->confirmedDate('D, M jS') }}
            @if( $order->isUnconfirmed() )
                {{ $order->created_at->format('D, M jS') }}
            @endif
        </li>
    </ul>
@else
    <ul class="info-boxes">
        {{--Customer Info--}}
        <li>
            <span class="info-box-heading">
                <strong><i class="fas fa-user"></i> {{ $order->getCustomerName() }}</strong>
            </span>
            {{ $order->customer_email }} | {{ $order->customer_phone }}
        </li>

        {{--Payment Status--}}
        <li>
            @if($order->paid)
                <span class="info-box-heading">
                    <strong><i class="fas fa-check-circle text-success"></i> Paid</strong>
                </span>
                @if($order->payment_date)
                    {{ $order->paymentMethod['title'] ?? 'None selected' }} | {{ carbonDate($order->payment_date) }}
                @endif
            @else
                <span class="info-box-heading">
                    <strong><i class="fas fa-times text-danger"></i> Not Paid</strong>
                </span>
            @endif
        </li>

        {{--Pickup Point--}}
        <li>
            <span class="info-box-heading"><strong><i class="fas fa-map-marker"></i> Pickup Location</strong>&nbsp;<small>(<a href="#" @click="showModal('pickupOrderModal')">edit</a>)</small>
            </span>
            {{ $order->pickup['title'] ?? "Unknown" }} {{ carbonDate($order->pickup_date, 'M, jS, Y', '(Date TBA)') }}
        </li>

        {{--Order Date--}}
        <li>
            <span class="info-box-heading"><strong><i class="fas fa-calendar"></i> Date</strong></span>
            {{ $order->present()->confirmedDate('D, M jS') }}
        </li>
    </ul>
@endif
