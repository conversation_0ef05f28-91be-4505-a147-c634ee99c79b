<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Orders</div>
            <button type="button" class="btn btn-alt flex-item" @click="hidePanel('filterPanel')"><i
                        class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            {{--Status--}}
            <div class="form-group">
                <label for="order_status">Order Status</label>
                <x-form.order-status-select
                        class="form-control select2 autofocus"
                        name="order_status[]"
                        data-placeholder="Select statuses"
                        tabindex="1"
                        multiple
                        :selected="request('order_status')"
                />
            </div>

            {{--Pickup--}}
            <div class="form-group">
                <label for="pickup_id">Delivery Method</label>
                <x-form.delivery-method-select
                        class="form-control select2"
                        name="pickup_id[]"
                        tabindex="1"
                        data-placeholder="Select zones or locations"
                        multiple
                        placeholder="All"
                        :selected="request('pickup_id')"
                />
            </div>

            <div class="form-group">
                <label for="title">Product Search</label>
                <input type="text" name="products" class="form-control" value="{{ Request::get('products') }}"
                       tabindex="1" placeholder="Search by product name or SKU"/>
            </div>

            {{--Packing Group--}}
            <div class="form-group">
                <label for="inventory_type">Packing Group</label>
                <x-form.packing-group-select
                        class="form-control select2"
                        name="inventory_type[]"
                        data-placeholder="Select packing groups"
                        tabindex="1"
                        multiple
                        :selected="request('inventory_type')"
                />
            </div>


            <div class="form-group">
                <label for="pickup_id">Order Type</label>
                <select
                        class="form-control"
                        name="subscription_status"
                        tabindex="1"
                >
                    <option value="all">Show All</option>
                    {!! selectOptions(['subscription' => 'Subscription', 'standard' => 'Standard'], request('subscription_status')) !!}
                </select>
            </div>

            {{--Schedule--}}
            <div class="form-group">
                <label for="schedule">Schedule</label>
                <x-form.schedule-select
                        class="form-control select2"
                        name="schedule_id[]"
                        tabindex="1"
                        data-placeholder="Select schedules"
                        multiple
                        :selected="request('schedule_id')"
                        :only-active="true"
                />
            </div>

            <div x-data="dateRange('confirmed_date_start', 'confirmed_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="confirmed_date">Confirmed date</label>
                    <x-form.date-range-dropdown/>
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <div class="relative">
                        <x-form.pikaday-input
                                name="confirmed_date[start]"
                                class="form-control"
                                value="{{ request('confirmed_date')['start'] ?? '' }}"
                                id="confirmed_date_start"
                                placeholder="Start date"
                                tabindex="1"
                        />
                        <button type="button" x-on:click="clearDate('confirmed_date_start')" class="absolute right-0 inset-y-0 pr-3">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="relative">
                        <x-form.pikaday-input
                                name="confirmed_date[end]"
                                class="form-control"
                                value="{{ request('confirmed_date')['end'] ?? '' }}"
                                id="confirmed_date_end"
                                placeholder="End date"
                                tabindex="1"
                        />
                        <button type="button" x-on:click="clearDate('confirmed_date_end')" class="absolute right-0 inset-y-0 pr-3">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>


            {{--Pickup Date--}}
            <div x-data="dateRange('pickup_date_start', 'pickup_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="pickup_date">Pickup date</label>
                    <x-form.date-range-dropdown/>
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="pickup_date[start]"
                            class="form-control"
                            value="{{ request('pickup_date')['start'] ?? '' }}"
                            id="pickup_date_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="pickup_date[end]"
                            class="form-control"
                            value="{{ request('pickup_date')['end'] ?? '' }}"
                            id="pickup_date_end"
                            placeholder="End date"
                            tabindex="1"
                    />

                </div>
            </div>

            <div x-data="dateRange('pack_deadline_at_start', 'pack_deadline_at_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="pack_deadline_at">Pack Deadline date</label>
                    <x-form.date-range-dropdown/>
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="pack_deadline_at[start]"
                            class="form-control"
                            value="{{ request('pack_deadline_at')['start'] ?? '' }}"
                            id="pack_deadline_at_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="pack_deadline_at[end]"
                            class="form-control"
                            value="{{ request('pack_deadline_at')['end'] ?? '' }}"
                            id="pack_deadline_at_end"
                            placeholder="End date"
                            tabindex="1"
                    />

                </div>
            </div>


            {{--Payment Date--}}
            <div x-data="dateRange('payment_date_start', 'payment_date_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="payment_date">Payment date</label>
                    <x-form.date-range-dropdown/>
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="payment_date[start]"
                            class="form-control"
                            value="{{ request('payment_date')['start'] ?? '' }}"
                            id="payment_date_start"
                            placeholder="Start date"
                            tabindex="1"
                    />
                    <x-form.pikaday-input
                            name="payment_date[end]"
                            class="form-control"
                            value="{{ request('payment_date')['end'] ?? '' }}"
                            id="payment_date_end"
                            placeholder="End date"
                            tabindex="1"
                    />
                </div>
            </div>

            {{--Tags--}}
            @isset($canBulkProcessOrders)
                @if($canBulkProcessOrders)
                    <div class="form-group">
                        <label for="order_tags">Tags</label>
                        <x-form.order-tag-select
                                class="form-control select2"
                                name="order_tags[]"
                                data-placeholder="Select some tags"
                                multiple
                                style="width: 100%"
                                :selected="request('order_tags')"
                        />
                    </div>
                @endif
            @endisset

            {{--Flagged--}}
            <div class="form-group">
                <div class="checkbox">
                    <label for="flagged_checkbox">
                        <input type="checkbox" id="flagged_checkbox" name="flagged" tabindex="1" value="true"
                               @if(Request::has('flagged')) checked @endif>
                        Flagged</label>
                </div>
            </div>

            {{--Picked Up--}}
            <div class="form-group">
                <div class="checkbox">
                    <label for="picked_up_checkbox">
                        <input type="checkbox" id="picked_up_checkbox" name="picked_up" tabindex="1" value="true"
                               @if(Request::has('picked_up')) checked @endif>
                        Picked Up</label>
                </div>
            </div>

            {{--Mispacked--}}
            <div class="form-group">
                <div class="checkbox">
                    <label for="fulfillment_error_checkbox">
                        <input type="checkbox" id="fulfillment_error_checkbox" name="fulfillment_error" tabindex="1"
                               value="true" @if(Request::has('fulfillment_error')) checked @endif>
                        Mispacked</label>
                </div>
            </div>

            {{--First Time Order--}}
            <div class="form-group">
                <div class="checkbox">
                    <label for="first_time_order_checkbox">
                        <input type="checkbox" id="first_time_order_checkbox" name="first_time_order" tabindex="1"
                               value="true" @if(Request::has('first_time_order')) checked @endif>
                        First Time Order</label>
                </div>
            </div>

            <div class="form-group">
                <label for="type_id">Packed By</label>
                <x-form.staff-select
                        class="form-control"
                        name="staff_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('staff_id')"
                        :include_not_assigned="true"
                />
            </div>

            {{--Exported--}}
            <div class="form-group">
                <label for="exported">Export Status</label>
                {{ html()->select('exported', [null => 'All',true => 'Exported',false => 'Not Exported'], request('exported'))->class('form-control')->id('exported') }}
            </div>

            {{--Paid--}}
            <div class="form-group">
                <label for="paid">Payment Status</label>
                <x-form.payment-status-select
                        class="form-control"
                        name="paid"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('paid')"
                />
            </div>

            {{--Payment Method--}}
            <div class="form-group">
                <label for="paid">Payment Method</label>
                <x-form.payment-method-select
                        class="form-control"
                        name="payment_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('payment_id')"
                />
            </div>

            {{--Vendors--}}
            <div class="form-group">
                <label for="vendor_id">Vendor</label>
                <x-form.vendor-select
                        class="form-control"
                        name="vendor_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('vendor_id')"
                />
            </div>

            {{--Confirmation Status--}}
            <div class="form-group">
                <label for="confirmed">Confirmation Status</label>
                <x-form.confirmation-status-select
                        class="form-control"
                        name="confirmed"
                        tabindex="1"
                        :selected="request('confirmed')"
                />
            </div>

            {{--Sales Channel--}}
            <div class="form-group">
                <label for="type_id">Sales Channel</label>
                <x-form.channel-select
                        class="form-control  select2"
                        name="order_type_id[]"
                        tabindex="1"
                        data-placeholder="Select sales channels"
                        multiple
                        :selected="request('order_type_id')"
                        :include_all="true"
                />
            </div>

            <div class="form-group">
                <label for="title">Filter by Order Ids</label>
                <input type="text" name="order_ids" class="form-control" value="{{ Request::get('order_ids') }}"
                       tabindex="1" placeholder="Add Order Ids separated by comma"/>
            </div>

        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button
                    type="submit"
                    class="btn btn-alt mr-md"
                    name="nc-export"
                    value="true"
            >NC <i class="fas fa-cloud-download"></i>
            </button>
            <button
                    type="submit"
                    class="btn btn-alt mr-md"
                    name="export"
                    value="true"
            >
                <i class="fas fa-cloud-download"></i>
            </button>
            <button
                    type="submit"
                    class="btn btn-action btn-block btn-lg"
                    @click="submitForm('filterOrdersForm')"
            >Filter
            </button>
        </div>
    </div>
</div>
