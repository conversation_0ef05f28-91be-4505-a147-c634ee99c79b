@php
    /** @var int $subscriptionId */
    /** @var \Illuminate\Support\Collection $events */
@endphp

<div>
    <div class="bg-white shadow rounded-md px-4 py-5 sm:px-6">
        <form>
            <div class="">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold leading-6 text-gray-900">Events</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of all subscription skip or cancel events</p>
                    </div>
                </div>
                @if($events->isEmpty())
                    <div class="mt-8 flow-root text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                             fill="none"
                             viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No events</h3>
                    </div>
                @else
                <div class="mt-8 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                                            <table class="min-w-full divide-y divide-gray-300">
                                                <thead>
                                                <tr>
                                                    <th scope="col"
                                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                                        Event
                                                    </th>
                                                    <th scope="col"
                                                        class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                                        Changes
                                                    </th>
                                                    <th scope="col"
                                                        class="relative py-3.5 pl-3 pr-4 text-right text-sm font-semibold text-gray-900 sm:pr-3">
                                                        Event time
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody class="divide-y divide-gray-200">

                                                @foreach($events as $event)
                                                    @php /** @var \App\Models\Event $event */ @endphp

                                                    <tr class="border-t border-gray-300">
                                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-3">
                                                            @if($event->event_id === \App\Events\Subscription\SubscriptionWasExpedited::class)
                                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">

                                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 text-green-500">
  <path stroke-linecap="round" stroke-linejoin="round" d="M3 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061A1.125 1.125 0 0 1 3 16.811V8.69ZM12.75 8.689c0-.864.933-1.406 1.683-.977l7.108 4.061a1.125 1.125 0 0 1 0 1.954l-7.108 4.061a1.125 1.125 0 0 1-1.683-.977V8.69Z" />
</svg>

                                                    Subscription Was Expedited
                                                </span>
                                                            @elseif($event->event_id === \App\Events\Subscription\SubscriptionWasSkipped::class)
                                                                <span class="inline-flex items-center gap-x-1.5 rounded-md px-2 py-1 text-sm font-medium">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 text-yellow-600">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
</svg>

                                                    Subscription Was Skipped
                                                </span>
                                                            @endif
                                                        </td>
                                                        <td class="whitespace-nowrap flex items-center space-x-2 px-3 py-4 text-sm text-gray-500">

                                                            <div>
                                                                {{ Carbon\Carbon::parse($event->metadata->old_delivery_date)->format('M j, Y') }}
                                                            </div>

                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5" />
                                                            </svg>

                                                            <div>
                                                                {{ Carbon\Carbon::parse($event->metadata->new_delivery_date)->format('M j, Y')  }}
                                                            </div>

                                                        </td>
                                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm sm:pr-3">
                                                            {{ $event->created_at->format('M j | H:i A') }}
                                                        </td>
                                                    </tr>

                                                @endforeach
                                                </tbody>
                                            </table>

                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
