@php
    /** @var App\Models\RecurringOrder $subscription */
    /** @var App\Models\Order|null $next_order */
    /** @var int $delivery_count */
    /** @var int $aov */
    /** @var Illuminate\Support\Collection $available_delivery_methods */
@endphp

<div>
    <div class="bg-white shadow rounded-md px-4 py-5 sm:px-6">
        <form>
            <div class="">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold leading-6 text-gray-900">Products</h1>
                        <p class="mt-2 text-sm text-gray-700">A list of all products included on the next subscription order</p>
                    </div>
                    @if( ! $subscription->trashed())
                        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                            <button type="button"
                                    wire:click="$dispatch('open-modal-add-subscription-item', { subscription_id: {{ $subscription->id }} })"
                                    class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600">
                                Add product
                            </button>
                        </div>
                    @endif
                </div>
                <div class="mt-8 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead>
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                        Product
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Quantity
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Type
                                    </th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Unit price
                                    </th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                        <span class="sr-only">Manage</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                @foreach($subscription->items as $item)
                                    @php /** @var \App\Models\RecurringOrderItem $item */ @endphp
                                    <tr>
                                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                                            <a href="{{ route('admin.products.edit', [$item->product]) }}" class="text-keppel-600 hover:text-keppel-500">
                                                {{ $item->product->title }}
                                            </a>
                                            @if($item->product->sku)
                                                <span class="mt-1 block text-gray-500 text-xs">{{ $item->product->sku }}</span>
                                            @endif
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $item->qty }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ $item->formattedType() }}</td>
                                        @if (! is_null($item->unit_price_override))
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                                ${{ money($item->unit_price_override) }}
                                            </td>
                                        @else
                                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-400 italic">Market
                                                price
                                            </td>
                                        @endif
                                        <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0 flex flex-shrink-0 items-center justify-end space-x-4">
                                            @if( ! $subscription->trashed())
                                                <button type="button"
                                                        wire:click="$dispatch('open-modal-edit-subscription-item', { item_id: {{ $item->id }} })"
                                                        class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2"
                                                >
                                                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                                                    </svg>
                                                </button>
                                                <span class="text-gray-300" aria-hidden="true">|</span>
                                                <button type="button"
                                                        wire:click="$dispatch('open-modal-delete-subscription-item-confirmation', { item_id: {{ $item->id }} })"
                                                        class="rounded-md bg-white font-medium text-keppel-600 hover:text-keppel-500 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2"
                                                >
                                                    <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                                    </svg>
                                                </button>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
