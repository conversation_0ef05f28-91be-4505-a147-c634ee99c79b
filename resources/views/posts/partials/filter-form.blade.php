<div class="panel ">
    <div class="panel-heading">Filter Posts</div>
    <div class="panel-body">
        <form action="{{ route('admin.posts.index') }}" method="GET">
            {{--Title--}}
            <div class="form-group">
                <label for="title">Search</label>
                <input type="text" name="posts" class="form-control" value="{{ Request::get('posts') }}" />
            </div>
            {{--Author--}}
            <div class="form-group">
                <label for="user_id">Author</label>
                <x-form.staff-select
                        class="form-control"
                        name="user_id"
                        placeholder="All"
                        :selected="request('user_id')"
                />
            </div>

            {{--Published Date--}}
            <div class="form-group">
                <label for="published_date">Published</label>
                <input type="text" name="published_date" class="form-control" value="{{ Request::get('published_date') }}" id="publishDate" />
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-default btn-block">Filter</button>
            </div>
        </form>
    </div>
</div>
