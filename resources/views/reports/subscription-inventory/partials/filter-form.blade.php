<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Report</div>
            <button type="button" class="btn btn-alt flex-item" @click="hidePanel('filterPanel')"><i
                        class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            <div class="form-group">
                <label for="date_type">Date Type</label>
                <select class="form-control" name="date_type" tabindex="1">
                    {!! selectOptions(['generate_at' => 'Generate Date', 'ready_at' => 'Delivery Date'], request('date_type')) !!}
                </select>
            </div>

            <div x-data="dateRange('date_range_start', 'date_range_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="date_range">Date Range</label>
                    <x-form.date-range-dropdown :use_future_date_ranges="true" />
                </div>
                <div class="flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="date_range[start]"
                            class="form-control"
                            value="{{ request('date_range')['start'] ?? '' }}"
                            id="date_range_start"
                            placeholder="Start date"
                            tabindex="1"
                    />
                    <x-form.pikaday-input
                            name="date_range[end]"
                            class="form-control"
                            value="{{ request('date_range')['end'] ?? '' }}"
                            id="date_range_end"
                            placeholder="End date"
                            tabindex="1"
                    />
                </div>
            </div>

            <div class="form-group">
                <label for="delivery_methods">Delivery Methods</label>
                <x-form.delivery-method-select
                        class="form-control select2"
                        name="delivery_methods[]"
                        tabindex="1"
                        data-placeholder="Select delivery methods"
                        multiple
                        placeholder="All"
                        :selected="request('delivery_methods')"
                />
            </div>

            <div class="form-group">
                <label for="vendor_id">Vendor</label>
                <x-form.vendor-select
                        class="form-control"
                        name="vendor_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('vendor_id')"
                />
            </div>

            <div class="form-group">
                <label for="sku">Product SKU</label>
                <input type="text" name="sku" class="form-control" value="{{ Request::get('sku') }}" placeholder="Search by product SKU" tabindex="1" />
            </div>

            <div class="form-group">
                <label for="collection_id">Collection</label>
                <x-form.collection-select
                        class="form-control"
                        name="collection_id"
                        :selected="(int) request('collection_id')"
                        :include_all="true"
                />
            </div>

            {{--Tags--}}
            <div class="form-group">
                <label for="order_tags">Tags</label>
                <x-form.order-tag-select
                        class="form-control select2"
                        name="order_tags[]"
                        data-placeholder="Select some tags"
                        multiple
                        style="width: 100%"
                        :selected="request('order_tags')"
                />
            </div>

        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-alt mr-md" name="export" value="true">
                <i class="fas fa-cloud-download"></i>
            </button>
            <button type="submit" class="btn btn-action btn-block btn-lg" @click="submitForm('filterReportsForm')">
                Filter
            </button>
        </div>
    </div>
</div>
