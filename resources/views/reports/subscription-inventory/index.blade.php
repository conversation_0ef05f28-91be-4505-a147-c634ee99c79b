@extends('layouts.main', ['pageTitle' => 'Subscription Inventory | Reports'])

@section('toolbar-breadcrumb')
    <li>Reports</li>
    <li>Subscription Inventory</li>
@stop

@section('toolbar-buttons')
    <button onclick="window.print()" class="btn btn-default flex-item"><i class="fas fa-print"></i> Print</button>
@stop

@section('content')
    <div class="visible-print text-center">
        <h5><strong>Subscription Inventory</strong></h5>
        @if(session()->has('subscription-inventory-filters'))
            @foreach(session('subscription-inventory-filters') as $key => $filterGroup)
                <ul class="list-inline">
                    @foreach($filterGroup as $filter)
                        <li>{{ $filter }}</li>
                    @endforeach
                </ul>
            @endforeach
        @endif
    </div>
    @include('reports.subscription-inventory.partials.index-table')
@stop

@section('scripts')
    <script>

        const trigger = document.querySelector('#net-total-tooltip-trigger');
        const tooltip = document.querySelector('#net-total-tooltip');

        if (trigger && tooltip) {
            const poppedTooltip = Popper.createPopper(trigger, tooltip, {
                placement: 'right'
            });
        }

        function show() {
            if (!tooltip) return;
            tooltip.classList.remove('hidden');

            // We need to tell Popper to update the tooltip position
            // after we show the tooltip, otherwise it will be incorrect
            poppedTooltip.update();
        }

        function hide() {
            if (!tooltip) return;
            tooltip.classList.add('hidden');
        }

        hide();

        const showEvents = ['mouseenter', 'focus'];
        const hideEvents = ['mouseleave', 'blur'];

        if (trigger) {
            showEvents.forEach((event) => {
                trigger.addEventListener(event, show);
            });

            hideEvents.forEach((event) => {
                trigger.addEventListener(event, hide);
            });
        }

    </script>
@stop
