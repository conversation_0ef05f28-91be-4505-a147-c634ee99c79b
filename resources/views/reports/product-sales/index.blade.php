@extends('layouts.main', ['pageTitle' => 'Product Sales | Reports'])


@section('toolbar-breadcrumb')
    <li>Reports</li>
    <li>Product Sales ({{ count($results) }})</li>
@endsection

@section('toolbar-buttons')
    <button onclick="window.print()" class="btn btn-default flex-item"><i class="fas fa-print"></i> Print</button>
@endsection

@section('content')
    <div class="visible-print text-center">
        <h5><strong>Product Sales</strong></h5>
        @if(session()->has('product-sales-applied-filters'))
            @foreach(session('product-sales-applied-filters') as $key => $filterGroup)
                <ul class="list-inline">
                    @foreach($filterGroup as $filter)
                        <li>{{ $filter }}</li>
                    @endforeach
                </ul>
            @endforeach
        @endif
    </div>

    @include('reports.product-sales.partials.index-table')
@endsection
