<div class="checkbox">
    @if($product->isOnSale())
        <label class="mr-sm">
            <input
                    tabindex="1"
                    type="checkbox"
                    name="sale"
                    value="1"
                    class="editable"
                    data-id="{{ $product->id }}"
                    checked="checked"
            > On Sale
        </label>
    @else
        <label class="mr-sm">
            <input
                    tabindex="1"
                    type="checkbox"
                    name="sale"
                    value="1"
                    class="editable"
                    data-id="{{ $product->id }}"
            > On Sale
        </label>
    @endif
</div>
<div class="input-group">
    <span class="input-group-addon">&#36;</span>
    <input
            type="text"
            name="sale_unit_price"
            value="{{ money($product->defaultPrice->sale_unit_price) }}"
            data-id="{{ $product->id }}"
            class="editable form-control flex-item"
    >
</div>
