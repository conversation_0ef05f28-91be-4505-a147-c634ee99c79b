<div class="gc-modal gc-modal-mask" id="createProductModal" @click="hideModal('createProductModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
            <form action="/admin/products" method="POST" id="storeProductForm">
                @csrf
                <div class="gc-modal-header">
                    Create a Product
                </div>

                <div class="gc-modal-body">
                    <div class="form-group">
                        <label for="title">
                            Title
                        </label>
                        <input type="text" name="title" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="type_id">
                            Type
                        </label>
                        <x-form.product-type-select
                                class="form-control"
                                name="type_id"
                        />
                    </div>
                    <div class="flex align-items-b">
                        <div class="flex-item-fill mr-sm">
                            <label for="unit_of_issue">Pricing Unit</label>
                            <x-form.unit-of-issue-select
                                    class="form-control"
                                    name="unit_of_issue"
                                    @change="changePriceUnit($event.target.value, '{{ $weight_uom }}')"
                            />

                        </div>
                        <div class="flex-item-fill mr-sm">
                            <label for="unit_price">
                                @{{ priceUnitMessage }}
                            </label>
                            <div class="input-group">
                                <div class="input-group-addon">$</div>
                                <input type="text" name="unit_price" class="form-control" placeholder="0.00">
                            </div>
                        </div>
                        <div class="flex-item-fill">
                            <label for="unit_price">
                                Weight
                            </label>
                            <div class="input-group">
                                <input type="text" name="weight" class="form-control" placeholder="0.000">
                                <div class="input-group-addon">lb</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('createProductModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click.prevent="submitForm('storeProductForm')">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
