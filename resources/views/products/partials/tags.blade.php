<div class="panel ">
    <div class="panel-heading">
        Tags
    </div>
    <div class="panel-body">
        <div class="select">
            <input type="text" class="form-control" autocomplete="off" v-on="blur: q = '', keyup: addTag | key 'enter'" tabindex="2" v-model="q" placeholder="add tags to product"/>
            <ul class="select-results" v-show="q.length" v-transition="scale">
                <li v-if="!totalMatches">
                    <span v-on="mousedown: addTag"><i class="fas fa-plus-circle"></i> Add "@{{ q }}"</span>
                </li>
                <li v-repeat="tag: tags | filterBy q | count">
                    <span v-on="mousedown: toggleTag(tag, $event)"><i class="fas fa-lg" v-class="tag.active ? 'fa-check-circle-o' : 'fa-circle-o'"></i> @{{ tag.title }}</span>
                </li>
            </ul>
        </div>
        <div class="tags">
            <div v-show="!activeTags.length"><em>There are no tags assigned to this product</em></div>
            <span class="label label-default" v-repeat="tag: tags" v-show="tag.active" v-transition="fade"><i class="fas fa-tag"></i> @{{ tag.title }} <a href="#" v-on="click: removeTag(tag, $event)"><i class="fas fa-times fa-fw"></i></a></span>
        </div>
    </div>
</div>