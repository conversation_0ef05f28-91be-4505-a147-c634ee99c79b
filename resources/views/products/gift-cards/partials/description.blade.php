@php
    /** @var \App\Models\Product $product */
@endphp
<form action="/admin/gift-cards/{{ $product->id }}" method="POST" id="productForm">
    @csrf
    @method('PUT')
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Title</h2>
                    </td>
                    <td>
                        <input type="text" name="title" class="form-control" value="{{ $product->title }}">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Price</h2>
                        <p>Set the redeemable value as well as the sale price. When the card is not on sale, the price paid at checkout will be equal to the
                            redeemable value.</p>
                    </td>
                    <td>
                        <label for="sale">On Sale</label>
                        <div class="radio">
                            <label class="mr-sm">
                                <input type="radio" name="sale" id="sale2" value="0" @if( ! $product->isOnSale()) checked @endif> No
                            </label>
                            <label>
                                <input type="radio" name="sale" id="sale3" value="1" @if($product->isOnSale()) checked @endif> Yes
                            </label>
                        </div>
                        <div class="mt-8 flex align-items-b">
                            <div class="flex-item-fill mr-md">
                                <label for="unit_price">Redeemable value</label>
                                <div class="input-group">
                                    <span class="input-group-addon">$</span>
                                    <input type="text" name="unit_price" class="form-control flex-item" value="{{ money($product->unit_price) }}" />
                                </div>
                            </div>

                            <div class="flex-item-fill">
                                <label for="sale_unit_price">Sale price</label>
                                <div class="input-group">
                                    <span class="input-group-addon">$</span>
                                    <input type="text" name="sale_unit_price" class="form-control" value="{{ money($product->sale_unit_price) }}" />
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Standard callout message</h2>
                        <p>Set a short message that highlights this gift card. Note: an active sale callout message takes precedence over this message.</p>
                    </td>
                    <td>
                        <input type="text" name="settings[standard_callout_message]" value="{{ $product->setting('standard_callout_message') }}" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Sale callout message</h2>
                        <p>Set a short message that only shows when the gift card is actively on sale. Note: This message takes precedence over the standard
                            callout message.</p>
                    </td>
                    <td>
                        <input type="text" name="settings[sale_message]" value="{{ $product->setting('sale_message') }}" class="form-control">
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm')">Save</button>
        </div>
    </div>

    <div class="panel panel-tabs">
        <div class="panel-body">
            <div class="form-group">
                <label for="unit_description">Unit Description</label>
                <input type="text" name="unit_description" class="form-control"
                       value="{{ $product->unit_description }}" />
            </div>

            <div class="form-group">
                <div class="flex justify-between">
                    <label for="summary">Summary</label>
                    <span class="text-sm text-gray-500">Max 1000</span>
                </div>
                <text-editor class="prose max-w-full" name="summary" :rows="2" min-height="200px" content="{{ $product->summary }}"></text-editor>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <text-editor class="prose max-w-full" name="description" :rows="6" content="{{ $product->description }}"></text-editor>
            </div>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm')">Save</button>
        </div>
    </div>

    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Fulfilment</h2>
                        <p>Configure the buying experience for this gift card.</p>
                    </td>
                    <td>
                        <div class="radio space-y-6 divide-y divide-gray-200">
                            <div class="space-y-2">
                                <div>
                                    <label class="mr-sm">
                                        <input tabindex="1" type="radio" name="settings[fulfilment_method]" value="virtual" @if($product->isFulfilledVirtually()) checked @endif>
                                        Virtual
                                        <p class="m-0 mt-1 text-xs text-gray-500">Gift cards are purchased separate from a customer's shopping cart and sales
                                            are tied to a virtual fulfilment location. Customer address information is not collected and payment is collected
                                            immediately upon checkout. Gift card codes are automatically issued and emailed to the customer upon successful
                                            payment.</p>
                                    </label>
                                </div>
                            </div>
                            <div class="pt-4">
                                <label>
                                    <input tabindex="1" type="radio" name="settings[fulfilment_method]" value="physical" @if( ! $product->isFulfilledVirtually()) checked @endif>
                                    Physical (Legacy)
                                    <p class="m-0 mt-1 text-xs text-gray-500">Gift cards are added to the customer's cart along with any other products.</p>
                                </label>
                            </div>

                        </div>
                    </td>
                </tr>

                @if($product->tracksStandardInventory())
                    <tr>
                        <td>
                            <h2>Online Store Inventory</h2>
                            <p>What is available for sale through the online store.</p>
                        </td>
                        <td>
                            <input type="number" name="inventory" value="{{ old('inventory', $product->inventory) }}"
                                   class="form-control" />
                        </td>
                    </tr>
                @endif
                <tr>
                    <td>
                        <h2>Track Inventory</h2>
                        <p>How online store inventory should be tracked for this product.</p>
                    </td>
                    <td>
                        @php($options = \Illuminate\Support\Arr::except(\App\Models\Product::TRACK_INVENTORY_OPTIONS, ['bundle']))
                        {{ html()->select('track_inventory', $options, $product->track_inventory)->class('form-control') }}
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Visibility</h2>
                        <p>Should this product be visible in the store?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="visible" value="1"
                                       @if($product->visible) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="visible" value="0"
                                       @if(!$product->visible) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Taxable</h2>
                        <p>Should tax be applied to this product?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="taxable" value="1"
                                       @if($product->taxable) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="taxable" value="0"
                                       @if(!$product->taxable) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Product SKU</h2>
                        <p>Stock-keeping unit is a unique identifier for each distinct product.</p>
                    </td>
                    <td>
                        <input type="text" name="sku" value="{{ $product->sku }}" class="form-control" tabindex="1" />
                    </td>
                </tr>

                <tr>
                    <td>
                        <h2>Packing Group</h2>
                        <p>This is a way to organize your products based on how they are packed.</p>
                    </td>
                    <td>
                        <x-form.packing-group-select
                                class="form-control"
                                name="inventory_type"
                                :selected="(int) $product->packingGroup->id"
                        />
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Keywords</h2>
                        <p>Add keywords separated by spaces to optimize how this product will show up in the store
                            search.</p>
                    </td>
                    <td>
                        <textarea name="keywords" rows="5" class="form-control">{{ $product->keywords }}</textarea>

                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Limit Per Customer</h2>
                        <p>Specify the maximum quantity a customer can add per order.</p>
                    </td>
                    <td>
                        <input type="number" name="settings[quantity_limit]"
                               value="{{ $product->setting('quantity_limit') }}" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Exclusivity</h2>
                        <p>You can exclude this product from being available at particular locations. By default, a
                            product will be available at all locations. Select any of the locations that you DO NOT want
                            this product to be available at.</p>
                    </td>
                    <td>
                        <input type="hidden" name="pickups" value="0">
                        <div class="relative">
                            <x-form.delivery-method-select
                                    class="form-control select2"
                                    name="pickups[]"
                                    tabindex="1"
                                    data-placeholder="No locations excluded"
                                    multiple
                                    style="width: 100%"
                                    :selected="$product->pickups()->pluck('pickups.id')->toArray()"
                            />
                        </div>

                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Accounting Class ID</h2>
                        <p>This is used when filtering products for accounting purposes.</p>
                    </td>
                    <td>
                        <input type="text" name="accounting_class" value="{{ $product->accounting_class }}"
                               class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>URL Slug</h2>
                        <p>This is the unique identifier used in URLs for this product. Use caution when changing this
                            as any links or bookmarks pointing to this product could be broken.</p>
                    </td>
                    <td>
                        <input type="text" name="slug" value="{{ $product->slug }}" class="form-control" />
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action" @click="submitForm('productForm')">Save</button>
        </div>
    </div>
</form>
