@props(['selected' => null, 'placeholder' => null])

@php
    $options = \App\Models\Payment::query()
        ->where('enabled', true)
        ->pluck('title', 'id');

    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp
<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach($options as $id => $title)
        <option value="{{ $id }}" @selected(in_array($id, $selected_array))>{{ $title }}</option>
    @endforeach
</select>
