@props(['selected' => null])

@php
    $options = [
        'title-asc' => 'Product Name - A-Z',
        'title-desc' => 'Product Name - Z-A',
        'unit_of_issue-asc' => 'Unit Of Issue - Package-Weight',
        'unit_of_issue-desc' => 'Unit Of Issue - Weight-Package',
        'custom_sort-asc' => 'Storage Location ID - Ascending',
        'custom_sort-desc' => 'Storage Location ID - Descending',
        'sku-asc' => 'SKU - Ascending',
        'sku-desc' => 'SKU - Descending',
        'inventory_type-asc' => 'Packing Group - Ascending',
        'inventory_type-desc' => 'Packing Group - Descending',
        'bundle-asc' => 'Bundles - Ascending',
        'bundle-desc' => 'Bundles - Descending',
    ];
@endphp

<select {{ $attributes }}>
    @foreach($options as $key => $label)
        <option value="{{ $key }}" @selected($key === $selected)>{{ $label }}</option>
    @endforeach
</select>
