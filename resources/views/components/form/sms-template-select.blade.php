@props(['selected' => null, 'placeholder' => null, 'placeholder_value' => null])

@php
    $templates = \App\Models\Template::query()
        ->where('type', 'sms')
        ->orderBy('title')
        ->pluck('title', 'id');
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option @if($placeholder_value) value="{{ $placeholder_value }}" @else value="" @endif>{{ $placeholder }}</option>
    @endif
    @foreach($templates as $id => $title)
        <option value="{{ $id }}" @selected($selected === $id)>{{ $title }}</option>
    @endforeach
</select>
