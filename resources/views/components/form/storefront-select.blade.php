@props(['selected' => null, 'placeholder' => null])

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif

    <optgroup label="Collections">
        @foreach(\App\Models\Collection::pluck('title', 'id') as $id => $title)
            <option value="{{ $id }}" @selected($id == $selected)>{{ $title }}</option>
        @endforeach
    </optgroup>
    <optgroup label="Pages">
        @foreach(\App\Models\Page::pluck('title', 'id') as $id => $title)
            <option value="page_{{ $id }}" @selected("page_{$id}" === $selected)>{{ $title }}</option>
        @endforeach
    </optgroup>
</select>
