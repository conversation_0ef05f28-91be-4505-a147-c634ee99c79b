@props(['initial_color' => null])

<div class="[&_.clr-field>button]:size-6 [&_.clr-field>button]:left-2 [&_.clr-field>button]:right-auto [&_.clr-field>button]:rounded-md [&_.clr-field>input]:pl-10">
    <input type="text"
           x-data="colorPicker"
           x-on:change="$dispatch('color-selected', $event.target.value)"
           value="{{ $initial_color ?? '' }}"
           class="block w-full rounded-md border border-gray-300 focus:outline-none focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm"
    />
</div>


