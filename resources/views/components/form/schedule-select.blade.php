@props(['selected' => null, 'placeholder' => null, 'only-active' => null])

@php
    $selected_array = \Illuminate\Support\Arr::wrap($selected);
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach(
        \App\Models\Schedule::when($onlyActive, function ($query) {
            $query->whereHas('pickups')
            ->orWhere('created_at', '>=', now()->subDays(180));
        })
        ->pluck('title', 'id') as $id => $title
    )
        <option value="{{ $id }}" @selected(in_array($id, $selected_array))>{{ $title }}</option>
    @endforeach
</select>

