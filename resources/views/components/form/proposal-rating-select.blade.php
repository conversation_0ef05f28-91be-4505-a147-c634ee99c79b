@props(['selected' => null, 'placeholder' => null])

@php
    $options = [
            '1' => '&#9733;&#9734;&#9734;&#9734;&#9734;',
            '2' => '&#9733;&#9733;&#9734;&#9734;&#9734;',
            '3' => '&#9733;&#9733;&#9733;&#9734;&#9734;',
            '4' => '&#9733;&#9733;&#9733;&#9733;&#9734;',
            '5' => '&#9733;&#9733;&#9733;&#9733;&#9733;',
        ];
@endphp

<select {{ $attributes }}>
    @if($placeholder)
        <option value="">{{ $placeholder }}</option>
    @endif
    @foreach($options as $key => $label)
        <option value="{{ $key }}" @selected(! is_null($selected) && $key == $selected)>{{ $label }}</option>
    @endforeach
</select>
