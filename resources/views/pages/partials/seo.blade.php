<form action="{{ route('admin.pages.update', $page->id) }}" method="POST" id="updateResourceForm">
@csrf
@method('put')
    <div class="panel panel-tabs">
        <div class="panel-body pa-0">
            <table class="table   table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Page Title</h2>
                        <p>This is what will show up in search engines and the browser window.</p>
                    </td>
                    <td>
                        <input type="text" name="page_title" class="form-control" value="{{ old('title', $page->page_title) }}">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Page Description</h2>
                        <p>This is what will show up in search engine results. The ideal length is about 160 characters.</p>
                    </td>
                    <td>
                        <textarea name="description" class="form-control" rows="4">{{ old('description', $page->description) }}</textarea>
                    </td>
                </tr>
                @if(!$page->isHomepage())
                <tr>
                    <td>
                        <h2>Page URL</h2>
                        <p>Be sure to update any links pointing to this page if you change this url.</p>
                    </td>
                    <td>
                        <div class="input-group">
                            <span class="input-group-addon">{{ Request()->root() }}/</span>
                            <input type="text" name="slug" class="form-control" value="{{ old('slug', $page->slug) }}"/>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Visible to Search Engines</h2>
                        <p>Should this product be indexed by search engines?</p>
                    </td>
                    <td>
                        <div class="radio">
                            <label class="mr-sm">
                                <input tabindex="1" type="radio" name="seo_visibility" value="1" @if($page->seo_visibility) checked @endif> Yes
                            </label>
                            <label>
                                <input tabindex="1" type="radio" name="seo_visibility" value="0" @if(!$page->seo_visibility) checked @endif> No
                            </label>
                        </div>
                    </td>
                </tr>
                @endif
                </tbody>
            </table>
        </div>
        <div class="panel-footer text-right">
            <button type="submit" class="btn btn-action" @click="submitForm('updateResourceForm')">Save</button>
        </div>
    </div>
</form>
