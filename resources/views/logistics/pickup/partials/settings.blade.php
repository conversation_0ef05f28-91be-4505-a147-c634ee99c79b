<div class="panel  panel-tabs pa-0">
    <div class="panel-body pa-0">
        <table class="table   table-striped table-settings">
            <tbody>
            {{--Location Status--}}
            <tr>
                <td>
                    <h2>Status</h2>
                </td>
                <td>
                    <x-form.delivery-method-status-select
                            class="form-control"
                            name="status_id"
                            :selected="(int) $pickup->status_id"
                    />
                </td>
            </tr>
            @if($pickup->isComingSoon())
                <tr>
                    <td>
                        <h2>Coming Soon Redirect URL</h2>
                        <p>The URL to redirect visitors to when this is the closest location to the postal code they provide at registration.</p>
                    </td>
                    <td>
                        <input type="text" name="settings[coming_soon_url]" value="{{ $pickup->setting('coming_soon_url') }}" class="form-control">
                    </td>
                </tr>
            @endif

            {{--Location Visibility--}}
            <tr>
                <td>
                    <h2>Visibility</h2>
                    <p>Control whether your customers can see this location.</p>
                </td>
                <td>
                    <div class="radio">
                        <label class="mr-sm">
                            <input tabindex="1" type="radio" name="visible" value="1" @if($pickup->visible) checked @endif> Visible
                        </label>
                        <label>
                            <input tabindex="1" type="radio" name="visible" value="0" @if(!$pickup->visible) checked @endif> Hidden
                        </label>
                    </div>
                </td>
            </tr>

            {{--Location Tax Rate--}}
            <tr>
                <td>
                    <h2>Tax Rate</h2>
                    <p>This will be applied to any taxable items purchased at this location.</p>
                </td>
                <td>
                    <div class="input-group" style="max-width: 100px">
                        <input
                                type="text"
                                min="0"
                                max="100"
                                name="tax_rate"
                                value="{{ old('tax_rate', $pickup->present()->taxRate()) }}"
                                class="form-control"
                        >
                        <div class="input-group-addon">%</div>
                    </div>
                </td>
            </tr>

            {{--Minimum Customer Order--}}
            <tr>
                <td>
                    <h2>Minimum Customer Order</h2>
                    <p>The minimum order total that must be reached before the order can be placed.</p>
                </td>
                <td>
                    <div class="input-group">
                        <div class="input-group-addon">$</div>
                        <input type="text" name="min_customer_orders" class="form-control" value="{{ money($pickup->min_customer_orders) }}"/>
                    </div>
                </td>
            </tr>

            {{--Payment Methods--}}
            <tr>
                <td>
                    <h2>Payment Options</h2>
                    <p>Here you can limit which payment options are available for this pickup. (If you don't select any then all enabled payment options will be
                        available by default.)</p>
                </td>
                <td>
                    <input type="hidden" name="payment_methods" value=""/>
                    <x-form.payment-method-select
                            class="form-control select2"
                            name="payment_methods[]"
                            data-placeholder="Use all"
                            multiple
                            :selected="$pickup->enabledPaymentMethods()"
                    />
                </td>
            </tr>

            {{--Pickup Latitude--}}
            <tr>
                <td>
                    <h2>Latitude</h2>
                </td>
                <td>
                    <input type="text" name="lat" class="form-control" value="{{ old('lat', $pickup->lat) }}"/>
                </td>
            </tr>
            {{--Pickup Longitude--}}
            <tr>
                <td>
                    <h2>Longitude</h2>
                </td>
                <td>
                    <input type="text" name="lng" class="form-control" value="{{ old('lng', $pickup->lng) }}"/>
                </td>
            </tr>

            {{--Location URL--}}
            <tr>
                <td>
                    <h2>URL Slug</h2>
                    <p>This is the unique identifier used in URLs for this pickup location. Use caution when changing this as any links or bookmarks pointing to
                        this location could be broken.</p>
                </td>
                <td>
                    <input type="text" name="slug" value="{{ $pickup->slug }}" class="form-control">
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Sales Channel</h2>
                    <p>All orders placed for this location will be attributed to this sales channel. Sales channels can be used for tracking in reports and for
                        accounting purposes. <em>Note: changing this will not affect orders already placed for this location.</em></p>
                </td>
                <td>
                    <x-form.channel-select
                            class="form-control"
                            name="settings[sales_channel]"
                            :selected="(int) $pickup->setting('sales_channel', 1)"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Pricing Group</h2>
                    <p>Determines which pricing customers will see when shopping the store for this location.
                        Note: this setting will be overridden by the pricing group of individual customers.</p>
                </td>
                <td>
                    <x-form.pricing-group-select
                            class="form-control"
                            name="pricing_group_id"
                            placeholder="Retail"
                            :selected="(int) $pickup->pricing_group_id"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Show Map</h2>
                    <p>Show the map on this location's details page.</p>
                </td>
                <td>
                    <div class="radio">
                        <label class="mr-sm">
                            <input tabindex="1" type="radio" name="settings[show_map]" value="1" @if($pickup->setting('show_map', true)) checked @endif> Show
                            Map
                        </label>
                        <label>
                            <input tabindex="1" type="radio" name="settings[show_map]" value="0" @if(!$pickup->setting('show_map', true)) checked @endif> Hide
                            Map
                        </label>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Order Confirmation Email</h2>
                    <p>Here you can specify which email template to use when a customer places an order for this location.</p>
                </td>
                <td>
                    <x-form.email-template-select
                            class="form-control"
                            name="settings[email_order_confirmation_template]"
                            placeholder="Use Default Template"
                            :selected="(int) $pickup->setting('email_order_confirmation_template')"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Order Packed Email</h2>
                    <p>Here you can specify which email template to use when sending order packed emails to customers who ordered for this location.</p>
                </td>
                <td>
                    <x-form.email-template-select
                            class="form-control"
                            name="settings[email_order_packed_template]"
                            placeholder="Use Default Template"
                            :selected="(int) $pickup->setting('email_order_packed_template')"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Order Processed Email</h2>
                    <p>Here you can specify which email template to use when processing an order order for this location.</p>
                </td>
                <td>
                    <x-form.email-template-select
                            class="form-control"
                            name="settings[process_order_email]"
                            placeholder="Use Default Template"
                            :selected="(int) $pickup->setting('process_order_email')"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Subscription Welcome Confirmation Override</h2>
                    <p>The notification sent to a new subscriber on their first order. Sends in replacement of the standard order confirmation email. When
                        set, this setting overrides the
                        <a href="{{ route('admin.apps.edit', ['app' => 'subscribe-save', 'tab' => 'notifications']) }}" target="_blank">global subscription
                            notification settings</a>.</p>
                </td>
                <td>
                    <x-form.email-template-select
                            class="form-control"
                            name="settings[recurring_orders_welcome_email_template_id]"
                            placeholder="Use global setting"
                            :selected="(int) $pickup->setting('recurring_orders_welcome_email_template_id')"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Subscription Re-order Confirmation Override</h2>
                    <p>The notification sent to subscribers when their ongoing subscription orders are generated. When set, this setting overrides the
                        <a href="{{ route('admin.apps.edit', ['app' => 'subscribe-save', 'tab' => 'notifications']) }}" target="_blank">global subscription
                            notification settings</a>.</p>
                </td>
                <td>
                    <x-form.email-template-select
                            class="form-control"
                            name="settings[recurring_orders_reorder_email_template_id]"
                            placeholder="Use global setting"
                            :selected="(int) $pickup->setting('recurring_orders_reorder_email_template_id')"
                    />
                </td>
            </tr>
            <tr>
                <td>
                    <h2>LTV Conversion Multiplier</h2>
                    <p>Set a value used as a multiplier when sending subscription conversion value analytics.</p>
                </td>
                <td>
                    <div class="form-group">
                        <label for="ltv_conversion_multiplier" class="sr-only">LTV Conversion Multiplier</label>
                        <input type="text" name="settings[ltv_conversion_multiplier]" value="{{ old('settings[ltv_conversion_multiplier]', $pickup->setting('ltv_conversion_multiplier') ?? 2.5) }}" class="form-control">
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="panel-footer text-right">
        <input type="hidden" name="settings[html_template]" value="{{ $pickup->setting('html_template') }}">
        <button type="submit" class="btn btn-action" @click.prevent="submitForm('updateResourceForm')">Save</button>
    </div>
</div>
