<template>
    <Modal @close="$emit('close')">
        <div>
            <div class="tw-bg-white tw-px-4 tw-pt-5 tw-pb-4 sm:tw-p-6 sm:tw-pb-4">
                <div class="sm:tw-flex sm:tw-items-start">
                    <div class="tw-mx-auto tw-flex-shrink-0 tw-flex tw-items-center tw-justify-center tw-h-12 tw-w-12 tw-rounded-full tw-bg-gray-100 tw-bg-opacity-75 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                        <MapIcon class="tw-h-6 tw-w-6 tw-gray-600" aria-hidden="true" />
                    </div>
                    <div class="tw-mt-3 tw-text-center sm:tw-mt-0 sm:tw-ml-4 sm:tw-text-left">
                        <DialogTitle as="h3" class="tw-text-lg tw-leading-6 tw-font-medium tw-font-display tw-text-gray-900">
                            Are you sure?
                        </DialogTitle>
                        <div class="tw-mt-2">
                            <p class="tw-text-sm tw-text-gray-500">
                                The address you selected doesn't look quite right. Please try another address that includes a street number.
                            </p>
                            <p class="tw-mt-4 tw-text-sm tw-text-gray-500">
                                If this is your correct address, please enter it manually.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-px-6 sm:tw-flex sm:tw-flex-row-reverse">
                <button @click="$emit('close')"  type="button" class="tw-mt-3 tw-w-full tw-inline-flex tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-shadow-sm tw-px-4 tw-py-2 tw-bg-white tw-text-base tw-font-medium tw-text-gray-700 hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2 focus:tw-ring-theme-action-color sm:tw-mt-0 sm:tw-ml-3 sm:tw-w-auto sm:tw-text-sm" ref="cancelButtonRef">Close</button>
            </div>
        </div>
    </Modal>
</template>

<script>
import Modal from "../Modal";
import { MapIcon } from "@heroicons/vue/outline";
import { DialogTitle } from "@headlessui/vue";

export default {
    name: "InvalidAutocompleteAddressModal",

    emits: ['close'],

    components: {
        DialogTitle,
        MapIcon,
        Modal,
    },

    setup() {
        return {}
    }
};
</script>
