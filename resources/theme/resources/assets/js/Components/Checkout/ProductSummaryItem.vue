<template>
    <li class="tw-flex tw-items-start tw-py-3 tw-space-x-4">
        <img v-if="item.product.cover_photo_thumbnail" :alt="item.product.title" :src="item.product.cover_photo_thumbnail" class="tw-flex-none tw-w-24 tw-h-24 tw-rounded-md tw-object-center tw-object-cover">
        <div v-else class="tw-flex-none tw-w-24 tw-h-24 tw-rounded-md tw-bg-gray-300"></div>
        <div class="tw-flex-auto tw-space-y-2">
            <div>
                <h3 class="tw-text-sm" v-text="item.product.title"></h3>
                <p class="tw-mt-px tw-text-xs tw-text-gray-500">
                    <span v-if="isPricedByWeight(item.product)">{{ item.weight }} {{ weightLabel(tenant.weight_uom) }}</span>
                </p>
            </div>

            <select :id="`quantity`" :name="`quantity`" :value="item.quantity" class="tw-max-w-full tw-rounded-md tw-border tw-border-gray-300 tw-py-1.5 tw-text-left tw-text-base tw-font-medium tw-leading-5 tw-text-gray-700 tw-shadow-sm focus:tw-border-theme-action-color focus:tw-outline-none focus:tw-ring-1 focus:tw-ring-theme-action-color sm:tw-text-sm" @input="event => cart.updateQuantity(parseInt(event.target.value))">
                <option v-for="quantity in quantityLimit" :value="quantity" v-text="quantity"></option>
            </select>
        </div>
        <p class="tw-flex-none tw-text-sm tw-font-medium" v-text="currency.centsToDollars(itemPrice(item))"></p>
    </li>
</template>

<script>
export default {
    name: 'ProductSummaryItem'
};
</script>

<script setup>
import { useTenantStore } from '../../stores/tenant';
import currency from '../../../../../../assets/js/modules/currency';
import { isPricedByWeight, weightLabel } from '../../../../../../assets/js/modules/cart';
import { itemPrice } from '../../composables/cart';
import { computed } from 'vue';
import { useCheckoutStore } from '../../stores/checkout';
import { useCartProductStore } from '../../stores/cartProduct';

const props = defineProps({
    item: { required: true }
});

const tenant = useTenantStore();
const checkout = useCheckoutStore();
const cart = useCartProductStore();

const quantityLimit = computed(() => checkout.quantity_limit ? checkout.quantity_limit : 25);
</script>
