import { defineStore } from 'pinia';

export const useSubscriptionStore = defineStore('subscription', {
    state: () => ({
        discount_incentive: 0,
        discount_incentive_applies_to_one_time_items: true,
        product_incentive_default: null,
        product_incentive_alternative_one: null,
        product_incentive_alternative_two: null,
        excluded_product_ids: [],
        available_frequencies: [],
        default_frequency: null
    }),

    getters: {
        selectedProductIncentive(state) {
            return productIdToMatch => {
                if (state.product_incentive_default && state.product_incentive_default.id === productIdToMatch) {
                    return state.product_incentive_default;
                }

                if (state.product_incentive_alternative_one && state.product_incentive_alternative_one.id === productIdToMatch) {
                    return state.product_incentive_alternative_one;
                }

                if (state.product_incentive_alternative_two && state.product_incentive_alternative_two.id === productIdToMatch) {
                    return state.product_incentive_alternative_two;
                }

                return null;
            };
        },

        availableProductIncentives(state) {
            const incentives = [];

            if (state.product_incentive_default) {
                incentives.push(state.product_incentive_default);
            }

            if (state.product_incentive_alternative_one) {
                incentives.push(state.product_incentive_alternative_one);
            }

            if (state.product_incentive_alternative_two) {
                incentives.push(state.product_incentive_alternative_two);
            }

            return incentives;
        },

        hasProductIncentive(state) {
            return this.availableProductIncentives.length > 0;
        }
    }
});
