import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
    state: () => ({
        id: null,
        first_name: null,
        last_name: null,
        street: null,
        street_2: null,
        city: null,
        state: null,
        zip: null,
        country: null,
        company_name: null,
        email: null,
        phone: null,
        order_count: 0,
        subscribed_to_sms_marketing_at: null,
        checkout_card_id: null,
        cards: [],
        addresses: [],
        exempt_from_fees: false,
        exempt_from_taxes: false,
        is_subscriber: false,
        stax_customer_id: null,
        settings: {}
    }),

    getters: {
        fullName: state => `${state.first_name} ${state.last_name}`
    },

    actions: {
        addCard(card) {
            this.cards.push(card)
        }
    }
})