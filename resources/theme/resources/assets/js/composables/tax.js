const locationFeesTaxTotal = (state) {
//     if (this.hasNoTax) return 0;
//
//     return locationFeesTaxTotal(this.location, this.locationFees, this.taxRate)
// },

const
    if (! Boolean(location.tax_delivery_fee)) return 0;

    return  Math.round(
        fees.filter(fee => Boolean(fee.is_taxable))
            .reduce((previousValue, fee) => previousValue + Math.round(fee.amount * taxRate), 0)
    )