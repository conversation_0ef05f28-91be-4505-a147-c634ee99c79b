.customerProfile__heading {
  margin-bottom: 2.5rem;
}

.customerProfile__container {
  display: flex;
}

.customerProfile__navigationContainer {
  vertical-align: top;
  width: 25%;
  padding-right: (@white-space / 2);
}

.customerProfile__contentContainer {
  vertical-align: top;
  width: 75%;
}

.customerProfile__contentContainer hr {
  margin: 15px 0;
}

.customerProfile__contentContainer b, .customerProfile__contentContainer strong {
  font-weight: 600;
}

.customerProfile__contentContainer_summary {
  padding-left: 1rem;
  background-color: #fefefe;
  border-left: solid 1px;
  border-color: #eee;
  width: 100%;
}

@media (max-width: 992px) {
  .mobileNav__list.account {
    display: none;
  }

  .customerProfile__container {
    display: block;
  }

  .customerProfile__navigationContainer {
    width: 100%;
    padding-right: 0;
  }

  .customerProfile__contentContainer {
    width: 100%;
  }

  .customerProfile__contentContainer_summary {
    border-left: none;
  }

  .customerProfile__contentContainer_summary .cartItems {
    margin-left: -15px;
  }
}

@media (max-width: @mobile-width) {
  .mobileNav__list.account {
    display: none;
  }

  .customerProfile__contentContainer {
    display: block;
    min-width: 100%;
    width: 100%;
  }
}
