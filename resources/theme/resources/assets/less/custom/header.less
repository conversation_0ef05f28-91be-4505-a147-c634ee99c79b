.header {
	width: 100%;
	height: auto;
	padding: 0;
}

.header--container {
	align-items: center;
	flex-wrap: no-wrap;
	height: auto;
	margin: 0 auto;
	justify-content: center;
	-ms-flex-pack: center;
}

.logo {
	text-align: center;
	height: auto;
}

.logo--text {
	font-size: 30px !important;
}

.logo--container {
	flex: 0 1 auto;
	height: auto;
}

.menu--container {
	flex: 1 1 auto;
	height: auto;
}

.logo--img {
	display: inline-block;
	min-width: 75px;
}

.navigationMenu--toggle {
	display: none;
	margin-left: auto;
}

@media (max-width: 768px) {
	.header--container {
		flex-direction: column !important;
	}
	.menu--container {
		width: 100% !important;
		padding: 0 !important;
	}
	.navigationMenu--toggle {
		display: block;
		flex: 1 0 auto;
	}
	.navigationMenu--toggle-button {
		display: block;
		border: none;
		background-color: #fff;
		color: #3d3d3d;
		padding: 6px 10px;
		border-radius: 2px;
		font-size: 22px;
		line-height: 1;

		margin: 6px 8px 6px auto;
	}
	.navigationMenu--toggle-button:hover {
		background-color: #3d3d3d;
		color: #fff;
	}
	.navigationMenu--toggle-button:focus {
		outline: none;
	}
	.logo--container {
		display: flex;
		align-items: center;
		width: 100%;
		padding: 0 10px;
	}
	.logo {
		flex: 1 0 auto;
		text-align: left;
	}
	.logo--img {
		max-height: 75px !important;
		max-width: 250px;
	}
}	