.productPage {
	max-width: 1000px;
}

.productPage__descriptionText, .productPage__productIngredients, .productPage__protocols {
	margin-bottom: (@white-space / 2);
}

.productPage__productDescription #productDescriptionTab {
	display: flex;
}

.productPage__descriptionText {
	display: inline-block;
	width: 75%;
	vertical-align: top;
}

.productPage__descriptionSidebar {
	display: inline-block;
	width: 25%;
	vertical-align: top;
	margin-left: @white-space;
}

.productPage__protocolsList {
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.productPage__addToCartInput {
	max-width: 60px;
}

@media (max-width: @mobile-width) {
	.productPage__productDescriptionTab {
		flex-direction: column;
	}
}

@media (max-width: @mobile-width) {
	.productPage__descriptionText {
		display: block;
		width: 100%;
		padding-right: 0;
	}

	.productPage__descriptionSidebar {
		display: block;
		width: 100%;
		margin-left: 0;
	}
}
