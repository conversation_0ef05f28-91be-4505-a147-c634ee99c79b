@extends('theme::_layouts.main', [
	'pageTitle' => getMessage('protocols_page_header')
	])
@section('content')
    <section class="pageContainer protocolsPage">

        <header class="protocolsPage__header">
            <h1 class="protocolsPage__heading h1">{{ getMessage('protocols_page_header') }}</h1>
        </header>

        @foreach($protocols as $protocol)
            <section class="protocolsPage__protocol">
                <a id="{{ $protocol->slug }}"></a>
                <h2 class="protocolsPage__protocolHeading h4">
                    {{ $protocol->title }}
                </h2>
                <div class="protocolsPage__protocolDescription">
                    <div class="tw-reset">
                        <div class="tw-prose" style="font-size: {{ theme('paragraph_font_size', '16px') }};">
                            {!! $protocol->description !!}
                        </div>
                    </div>
                </div>
            </section>
        @endforeach

    </section>
@stop
