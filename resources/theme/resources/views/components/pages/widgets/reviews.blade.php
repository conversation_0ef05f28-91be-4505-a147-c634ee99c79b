@props(['widget'])

@php
    $padding_top = match ($widget['settings']['padding']['top'] ?? '') {
        'sm' => 'tw-pt-6 lg:tw-pt-10',
        'md' => 'tw-pt-12 lg:tw-pt-16',
        'lg' => 'tw-pt-24 lg:tw-pt-32',
        'xl' => 'tw-pt-32 lg:tw-pt-48',
        default => '',
    };

    $padding_bottom = match ($widget['settings']['padding']['bottom'] ?? '') {
        'sm' => 'tw-pb-6 lg:tw-pb-10',
        'md' => 'tw-pb-12 lg:tw-pb-16',
        'lg' => 'tw-pb-24 lg:tw-pb-32',
        'xl' => 'tw-pb-32 lg:tw-pb-48',
        default => '',
    };

    $max_width = match ($widget['settings']['max_width'] ?? '') {
        'sm' => 'tw-max-w-lg',
        'md' => 'tw-max-w-4xl',
        'lg' => 'tw-max-w-6xl',
        'xl' => 'tw-max-w-7xl',
        default => '',
    };
@endphp

<div @if(!empty($widget['settings']['html_id'] ?? '')) id="{{ $widget['settings']['html_id'] }}" @endif class="tw-relative tw-w-full">
    <div class="tw-px-6 sm:tw-px-6 lg:tw-px-8 {{ $padding_top }} {{ $padding_bottom }}">
        <div class="tw-relative tw-mx-auto {{ $max_width }}">
            <h2 class="tw-m-0 tw-text-2xl tw-text-gray-900 sm:tw-text-3xl">Real Customer Reviews</h2>
            <x-theme::customer-reviews/>
            <div class="tw-mt-2 embedsocial-hashtag" data-ref="cc7ef6b5f35e6be807158093d554e97d0014166f"></div>
        </div>
    </div>
</div>
