@php use App\OrderWindow; @endphp
@php
    /** @var \App\Models\RecurringOrder $subscription */
    /** @var \App\Models\Order|null $order */
    /** @var \Carbon\Carbon $next_delivery_date */
@endphp
<x-theme::legacy-modal id="skipRecurringOrderModal">
    <div x-data="{ weekFrequency: 1 }" class="tw-relative tw-w-full tw-mx-auto tw-transform tw-rounded-lg tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full sm:tw-max-w-2xl">
        <form action="{{ !is_null($order) ? route('customers.recurring.skip', $order) : route('subscriptions.skip', [$subscription])}}"
              method="POST"
              onsubmit="submitButton.disabled = true; return true;">
            @csrf
            @if(! is_null($order))
                @method('PUT')
            @endif
            <div class="tw-rounded-t-lg tw-bg-white tw-px-4 tw-pb-4 tw-pt-5 sm:tw-p-6 sm:tw-pb-4">
                <div class="sm:tw-flex sm:tw-items-start">
                    <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-flex-shrink-0 tw-items-center tw-justify-center tw-rounded-full tw-bg-theme-brand-color/25 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                        <svg class="tw-h-6 tw-w-6 tw-text-theme-brand-color" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"/>
                        </svg>
                    </div>
                    <div class="tw-mt-3 tw-text-center sm:tw-ml-4 sm:tw-mt-0 sm:tw-text-left">
                        <h3 class="tw-m-0 tw-text-lg tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">Reschedule Next Order</h3>
                        <p class="tw-m-0 tw-mt-1 tw-text-sm tw-text-gray-500">
                            Select how many weeks would you like to skip.
                        </p>
                        <div class="tw-text-left tw-mt-4 tw-grid tw-grid-cols-1 tw-gap-x-6 tw-gap-y-8 sm:tw-grid-cols-6">
                            <div class="tw-col-span-full">
                                @foreach(range(1, 4) as $weeks)
                                    <div style="padding-bottom: 0.75rem; padding-left: 1rem;">
                                        <label style="display: flex; align-items: center;">
                                            <input
                                                    type="radio"
                                                    name="week_frequency"
                                                    value="{{ $weeks }}"
                                                    x-model="weekFrequency"
                                                    style="margin-top: 0;"
                                            >
                                            <span class="tw-pl-4 tw-font-normal tw-text-gray-900">
                                                {{ $weeks }} week{{ $weeks > 1 ? 's' : '' }}
                                                <span class="tw-text-gray-600 tw-text-xs">
                                                    ({{ $next_delivery_date?->copy()->addWeeks($weeks)->format('D, M j, Y') }})
                                                </span>
                                            </span>
                                        </label>
                                    </div>
                                @endforeach

                                <div style="padding-bottom: 0.75rem; padding-left: 1rem;">
                                    <label style="display: flex; align-items: center;">
                                        <input
                                                type="radio"
                                                name="week_frequency"
                                                value="custom"
                                                x-model="weekFrequency"
                                                style="margin-top: 0;"
                                        >
                                        <span class="tw-pl-4 tw-font-normal">
                                            Choose date
                                        </span>
                                    </label>
                                    <div class="tw-pl-8 tw-pt-1">
                                        <select
                                                name="custom_date"
                                                class="form-control"
                                                style="max-width: 200px;"
                                                :disabled="weekFrequency !== 'custom'"
                                        >
                                            @php
                                                $order_windows = $subscription->pickupSchedule()
                                                    ->activeOrderWindowCollection()
                                                    ->filter(fn(OrderWindow $order_window) =>
                                                        $order_window->deliveryDatetime()->lte(today()->addWeeks(4))
                                                    );
                                            @endphp
                                            @foreach ($order_windows as $order_window)
                                                <option value="{{ $order_window->originalDate()->pickup_date->format('Y-m-d') }}">
                                                    {{ $order_window->deliveryDatetime()->format('D, M j, Y') }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="{ submitting: false }" class="tw-rounded-b-lg tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-flex sm:tw-flex-row-reverse sm:tw-px-6">

                <button type="submit" x-on:click="submitting = true" class="tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color sm:tw-ml-3 sm:tw-w-auto">
                    <span x-show="! submitting">Confirm</span>
                    <svg x-show="submitting" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
                <button type="button" x-on:click="close" class="tw-mt-3 tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 sm:tw-mt-0 sm:tw-w-auto">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</x-theme::legacy-modal>
