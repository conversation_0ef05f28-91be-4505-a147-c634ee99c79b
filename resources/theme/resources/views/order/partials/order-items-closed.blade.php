<div class="cartItems">
    @foreach($order->items as $item)
        @php /** @var \App\Models\OrderItem $item */ @endphp
        <section class="cartItems__itemContainer">
            <div class="cartItems__itemHeading">
                @if($item['product']['cover_photo'])
                    <div class="cartItems__itemPhoto">
                        <img src="{{ \App\Models\Media::s3ToCloudfront($item['product']['cover_photo']) }}"
                             alt="{{ $item->title }}">
                    </div>
                @endif
                <div class="cartItems__itemTitle">
                    <h2 class="cartItems__itemTitleHeading h4">
                        <a href="/store/product/{{ $item['product']['slug'] }}">{{ $item->title }}</a>
                    </h2>
                    <div class="text-muted cartItems__unitDescription">{{ $item['product']['unit_description'] }}</div>
                    <div class="cartItems__itemSubtotal">&#36;{{ money($item->subtotal) }}</div>
                    @if($item->price)
                        <div class="cartItems__itemPrice">&#36;{{ money($item->price) }}/ea.</div>
                    @endif
                </div>
            </div>
        </section>
    @endforeach
    @if($order->items->isEmpty())
        <p>There are no items in your cart yet.</p>
    @endif
</div>


@if($order->confirmed)
    @include('theme::order.partials.fees--order')
@else
    @include('theme::order.partials.fees--pickup')
@endif
