@php
	/** @var \App\Models\Order $order */
    $is_recurring = $order->isRecurring();
    if ($is_recurring) {
        $oneTimeItems = $order->oneTimeItems();
        $subscriptionItems = $order->subscriptionItems();
    }
@endphp

@if(count($order->items))
	<div class="tw-reset">
		<div class="tw-space-y-6">
			@if($is_recurring)
				<div class="tw-rounded-md tw-border tw-border-gray-300 tw-px-4 tw-pt-4">
					<div>
						<h3 class="tw-font-display tw-text-gray-800">Subscription items</h3>
						<div class="tw-mt-1 tw-text-gray-700 tw-italic">
							<p class="tw-m-0 tw-text-base">Repeat on every order</p>
						</div>
					</div>

					<div>
						<ul role="list" class="tw-divide-y tw-divide-gray-200">
							@php
								[$standard_items, $promo_items] = $subscriptionItems->partition(fn(\App\Models\OrderItem $item) => $item->type !== 'promo')
							@endphp
							@foreach($promo_items as $item)
								<li>
									<x-theme-line-item :item="$item" :can_be_modified="false"/>
								</li>
							@endforeach
							@foreach($standard_items as $item)
								<li>
									<x-theme-line-item :item="$item" :can_be_modified="true" :refresh_on_change="true"/>
								</li>
							@endforeach
						</ul>
					</div>
				</div>

				@if($oneTimeItems->isNotEmpty())
					<div class="tw-rounded-md tw-border tw-border-gray-300 tw-px-4 tw-pt-4">
						<div>
							<h3 class="tw-font-display tw-text-gray-800">One-time items</h3>
							<div class="tw-mt-1 tw-text-gray-700 tw-italic">
								<p class="tw-m-0 tw-text-base">This order only</p>
							</div>
						</div>
						<div>
							<ul role="list" class="tw-divide-y tw-divide-gray-200">
								@foreach($oneTimeItems as $item)
									<li>
										<x-theme-line-item :item="$item" :can_be_modified="true" :refresh_on_change="true"/>
									</li>
								@endforeach
							</ul>
						</div>
					</div>
				@endif
			@else
				<ul role="list" class="tw-divide-y tw-divide-gray-200">
					@foreach($order->rawItems()->with('product')->get() as $item)
						<li>
							<x-theme-line-item :item="$item" :can_be_modified="true"/>
						</li>
					@endforeach
				</ul>
			@endif
		</div>

	</div>

	@if($order->confirmed)
		@include('theme::order.partials.fees--order')
	@else
		@include('theme::order.partials.fees--pickup')
	@endif

@else
	<div class="panel panel-default">
		<div class="panel-body text-center">
			<div class="form-group">
				<strong>There are no items in your cart...</strong>
			</div>
			<a href="/store" class="btn btn-default">Start shopping</a></div>
		</div>
	</div>
@endif
