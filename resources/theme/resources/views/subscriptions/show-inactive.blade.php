<div>
    @php
        /** @var \App\Models\RecurringOrder $subscription */
        /** @var \App\Models\Order|null $in_transit_order */

        $delivery_method = $subscription->fulfillment;
        $subscription_settings = app(App\Services\SubscriptionSettingsService::class);
    @endphp

    <div class="tw-reset tw-bg-white">
        <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-pt-8 tw-pb-12 sm:tw-px-6 lg:tw-max-w-7xl lg:tw-px-8">

            <div class="tw--ml-1 tw-flex tw-flex-center tw-text-theme-link-color/70">
                <svg class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd"/>
                </svg>
                <a href="{{ route('customer.profile') }}" class="tw-ml-2 tw-text-theme-link-color tw-no-underline">My account</a>
            </div>

            <main>
                <div class="tw-mt-8 tw-space-y-20 sm:tw-space-y-24">
                    <!-- Start Order information -->
                    <div>
                        <!-- Start Order -->
                        <div class="tw-pt-6 tw-bg-white">
                            <div class="sm:tw-flex sm:tw-items-baseline sm:tw-justify-between">
                                <div class="tw-flex tw-items-center">


                                    <div>
                                        <h3 id="message-heading" class="tw-m-0 tw-text-xl tw-font-body tw-font-semibold tw-leading-6 tw-text-gray-700">
                                            My Subscription
                                        </h3>
                                        @if($in_transit_order?->deadlineHasPassed() ?? true)
                                            <p class="tw-m-0 tw-mt-1 tw-truncate tw-text-sm tw-text-gray-500">
                                                Your subscription is currently inactive.
                                            </p>
                                        @endif
                                    </div>
                                </div>

                                <div class="tw-mt-4 tw-flex tw-items-center tw-justify-between sm:tw-ml-6 sm:tw-mt-0 sm:tw-flex-shrink-0 sm:tw-justify-start">
                                    {{--                            <span class="tw-inline-flex tw-items-center tw-rounded-full tw-bg-green-50 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-green-700 tw-ring-1 tw-ring-inset tw-ring-green-600/20">Open</span>--}}
                                    <div class="tw-relative sm:tw-ml-3 tw-inline-block tw-text-left">
                                        <div class="tw-flex tw-justify-between">
                                            <div class="tw-flex tw-items-center tw-space-x-2 tw-text-sm">
                                                <button type="button" wire:click="$dispatch('open-modal-subscription-resume')" class="tw-flex tw-items-center tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-2.5 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color/50 focus:tw-ring-offset-2">
                                                    Resume and Continue Saving
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tw-mt-6 lg:tw-grid lg:tw-grid-cols-12 lg:tw-items-start lg:tw-gap-x-12 xl:tw-gap-x-16">
                            <section aria-labelledby="cart-heading" class="tw-bg-white tw-h-full tw-pr-8 lg:tw-pr-0 lg:tw-col-span-7">

                                <h2 id="cart-heading" class="tw-sr-only">Items in your subscription</h2>

                                <livewire:theme.subscription-items :subscription="$subscription" :show_promotion_change="false" :can_be_modified="false"/>
                            </section>

                            <!-- Subscription summary -->
                            <section aria-labelledby="summary-heading" class="tw-bg-gray-50 tw-mt-8 lg:tw-col-span-5 lg:tw-mt-0">
                                <div class="tw-rounded-lg tw-bg-gray-50 tw-px-4 tw-py-6 sm:tw-p-6 lg:tw-p-8">

                                    <h2 id="summary-heading" class="tw-text-lg tw-font-medium tw-text-gray-900">Subscription summary</h2>

                                    <div class="tw-mt-6">
                                        <x-theme::subscription-summary :subscription="$subscription"/>

                                        <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                                            <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                                            <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($subscription->total()) }}</dd>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <!-- End Order -->
                    </div>
                    <!-- End Order information -->

                    <!-- Start Subscription information -->
                    <div class="tw-mx-auto tw-max-w-2xl tw-space-y-16 sm:tw-space-y-20 lg:tw-mx-0 lg:tw-max-w-none">
                        <div class="tw-bg-white tw-p-6 tw-border tw-border-gray-200 tw-rounded-md tw-shadow">
                            <h2 class="tw-m-0 tw-text-lg tw-font-semibold tw-font-body tw-leading-7 tw-text-gray-900">Subscription details</h2>
                            <p class="tw-m-0 tw-mt-1 tw-text-sm tw-leading-6 tw-text-gray-500">This information will be used when you resume your subscription.</p>

                            <dl class="tw-mt-6 tw-space-y-6 tw-divide-y tw-divide-gray-100 tw-border-t tw-border-gray-200 tw-text-sm tw-leading-6">
                                <div class="tw-pt-6 sm:tw-flex">
                                    <dt class="tw-font-medium tw-text-gray-900 sm:tw-w-64 sm:tw-flex-none sm:tw-pr-6">Delivery method</dt>
                                    <dd class="tw-mt-1 sm:tw-mt-0">
                                        <div class="tw-text-gray-900">
                                            @if($delivery_method->isDeliveryZone())
                                                <div>Home delivery</div>
                                                @php($address = $subscription->customer->addresses()->default()->first())
                                                <address class="tw-mt-2 tw-text-gray-500">
                                                    {{ $subscription->customer->full_name }} <br/>
                                                    {{ $address?->street }} @if($address?->location->street_2)
                                                        , {{ $address?->location->street_2 }}
                                                    @endif <br/>
                                                    {{ $address?->city }}, {{ $address?->state }} {{ $address?->postal_code }}
                                                </address>
                                            @else
                                                Pickup
                                                <address class="tw-mt-2 tw-text-gray-500">
                                                    {{ $delivery_method->present()->title() }}<br/>
                                                    {{ $delivery_method->street }} @if($delivery_method->street_2)
                                                        , {{ $delivery_method->street_2 }}
                                                    @endif <br/>
                                                    {{ $delivery_method->city }}, {{ $delivery_method->state }} {{ $delivery_method->zip }}
                                                </address>
                                            @endif
                                        </div>
                                        <div class="tw-mt-2 tw-flex">
                                            <div class="tw-flex-shrink-0">
                                                <svg class="tw-h-5 tw-w-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"/>
                                                </svg>

                                            </div>
                                            <div class="tw-ml-3 tw-flex-1 md:tw-flex md:tw-justify-between">
                                                <p class="tw-text-sm tw-text-gray-700">Please contact us to update your delivery method.</p>
                                            </div>
                                        </div>

                                    </dd>
                                </div>
                                <div class="tw-pt-6 sm:tw-flex">

                                    <dt class="tw-font-medium tw-text-gray-900 sm:tw-w-64 sm:tw-flex-none sm:tw-pr-6">Notifications</dt>
                                    <dd class="tw-mt-1 tw-flex tw-justify-between tw-gap-x-6 sm:tw-mt-0 sm:tw-flex-auto">
                                        <div class="tw-space-y-3 tw-text-gray-900">
                                            <div class="tw-flex tw-items-center">
                                                <div class="tw-flex-shrink-0">
                                                    <svg class="tw-w-5 tw-h-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"/>
                                                    </svg>
                                                </div>

                                                <p class="tw-m-0 tw-ml-3 tw-truncate tw-text-sm tw-font-medium">{{ $subscription->customer->email }}</p>
                                            </div>
                                            <div class="tw-flex tw-items-center">
                                                <div class="tw-flex-shrink-0">
                                                    <svg class="tw-w-5 tw-h-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"/>
                                                    </svg>
                                                </div>
                                                <p class="tw-m-0 tw-ml-3 tw-truncate tw-text-sm tw-font-medium">{{ $subscription->customer->phone }}</p>
                                            </div>
                                        </div>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    <!-- End Subscription information -->

                </div>

                <!-- Start help information -->
                <div class="tw-mt-6 tw-pt-6 tw-pb-5 tw-bg-white sm:tw-mt-12">
                    <div class="tw--ml-4 tw--mt-4 tw-flex tw-flex-wrap tw-items-center tw-justify-between sm:tw-flex-nowrap">
                        <div class="tw-ml-4 tw-mt-4">
                            <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">Need help?</h3>
                            <p class="tw-mt-1 tw-text-sm tw-text-gray-500">If you need help reactivating your subscription or updating your delivery details, please
                                <a href="{{ route('page.contact') }}" class="tw-font-semibold tw-text-theme-link-color tw-no-underline hover:tw-text-theme-link-color/70">contact
                                    us</a>.</p>
                        </div>
                    </div>
                </div>
                <!-- End help information -->
            </main>
        </div>
    </div>


</div>

