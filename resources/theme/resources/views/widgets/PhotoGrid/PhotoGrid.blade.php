@if(is_array($widget->setting('items')) && count($widget->setting('items')))
    <section
            class="photoGridWidget photoGridWidget--{{ $widget->id}} {{ $widget->setting('alignment', 'text-center') }}"
            id="photoGridWidget{{ $widget->id }}"
            data-widget="{{ $widget->id}}" data-element="photos"
    >
        @if($widget->setting('header_show', true))
            <header class="newsletterWidget__header">
                <h2 data-widget="{{ $widget->id}}" data-element="headingText">
                    {{ $widget->setting('header') }}
                </h2>
            </header>
        @endif

        <ul class="photoGridWidget__list photoGridWidget__list--{{ $widget->setting('orientation', 'vertical') }}">
            @foreach($widget->setting('items') as $item)
                <li class="photoGridWidget__listItem">
                    <div
                            class="photoGridWidget__listItemContainer"
                            data-widget="{{ $widget->id }}"
                            data-element="PhotoEditor"
                            data-item="{{ $loop->index }}"
                    >
                        @if($item)
                            <a href="{{ url($item->url ?? '#') }}">
                                <img src="{{ \App\Models\Media::s3ToCloudfront($item->src ?? '') }}"
                                     alt="{{ $item->caption ?? '' }}" class="photoGridWidget__listImage">
                                <div class="photoGridWidget__listCaption">
                                    <div>
                                        @if($item->subcaption)
                                            <span class="photoGridWidget__subcaption h4">{{ $item->subcaption }}</span>
                                        @endif
                                        <div class="photoGridWidget__caption h2">{{ $item->caption ?? '' }}</div>
                                    </div>
                                </div>
                            </a>
                        @endif
                    </div>
                </li>
            @endforeach
        </ul>
    </section>
@endif
