<div 
	class="productProtocolsWidget productProtocolsWidget--{{ $widget->id }}" 
	id="productProtocolsWidget{{ $widget->id }}" 
	data-widget="{{ $widget->id}}" 
	data-element="home"
>
	@if($widget->setting('header_show'))
	<header class="featuredRecipesWidget__header">
		<h2 data-widget="{{ $widget->id}}" data-element="headingText">
			{{ $widget->setting('header') }}
		</h2>
	</header>
	@endif

	<ul class="productProtocolsWidget__list">
	@foreach($themeService->getProtocols($widget) as $protocol)
	<li class="productProtocolsWidget__listItem">
		<a name="{{ $protocol->slug }}" id="{{ $protocol->slug }}"></a>
		<h2 class="productProtocolsWidget__heading h3">{{ $protocol->title }}</h2>
		<div class="productProtocolsWidget__description">
		{!! $protocol->description !!}
		</div>
		<a href="/store/protocol/{{ $protocol->slug }}" class="productProtocolsWidget__shopNowLink">Shop Now</a>
	</li>
	@endforeach
	</ul>
</div>