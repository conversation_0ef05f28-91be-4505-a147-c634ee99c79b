@php
    /** @var \App\Models\Product $product */
    /** @var \App\Models\Order|null $order */
    /** @var bool|null $has_subscription */
    /** @var string|null $cta_label */
    /** @var string|null $cta_action */
    /** @var string|null $cta_classes */
    /** @var array|null $metadata */
@endphp

@if( ! is_null($order))
    @include('theme::store._partials.add_to_order_form', [
        'product' => $product,
        'order' => $order,
        'has_subscription' => $has_subscription ?? false,
        'cta_label' => $cta_label ?? null,
        'cta_action' => $cta_action ?? null,
        'cta_classes' => $cta_classes ?? null,
        'metadata' => $metadata ?? [],
    ])
@elseif($has_subscription)
    @include('theme::store._partials.add_to_subscription_form', [
        'product' => $product,
        'order' => $order,
        'cta_label' => $cta_label ?? null,
        'cta_action' => $cta_action ?? null,
        'cta_classes' => $cta_classes ?? null,
        'metadata' => $metadata ?? []
    ])
@else
    @include('theme::store._partials.add_to_cart_form', [
        'product' => $product,
        'has_subscription' => false,
        'cta_label' => $cta_label ?? null,
        'cta_action' => $cta_action ?? null,
        'cta_classes' => $cta_classes ?? null,
        'metadata' => $metadata ?? [],
    ])
@endif
