@extends('theme::_layouts.main', [
	'pageTitle' => $pageTitle ?? 'Store',
	'pageDescription' => isset($pageDescription) ? $pageDescription : '',
	'pageCanonical' => $pageCanonical ?? null,
	'robots' => $robots ?? null,
])

@php
    /**
     * @var \App\Models\Order|null $openOrder
     * @var \App\Contracts\Cartable $cart
     */
@endphp

@section('pageMetaTags')
    {!! $headTags ?? null !!}

    <style>
        .product-list ::-webkit-scrollbar {
            display: none;
        }

        @media (min-width: 640px) {
            .product-list ::-webkit-scrollbar {
                display: block;
                height: 8px; /* height of horizontal scrollbar */
            }
        }
    </style>
@stop

@section('content')
    <div class="tw-reset">
        <div class="tw-max-w-7xl sm:tw-text-center sm:tw-mx-auto ">
            <div class="tw-py-4 tw-px-4 sm:tw-py-8 sm:tw-px-6 lg:tw-px-8">
                <h1 class="tw-text-2xl tw-font-bold tw-text-gray-900 lg:tw-text-4xl">
                    Shop 100% Grass-Fed <br class="sm:tw-hidden"/> and Pastured-Raised Meat Online
                </h1>
                <div class="tw-mt-3 tw-max-w-xl tw-mx-auto tw-text-sm tw-text-gray-500 tw-prose lg:tw-text-base lg:tw-mt-4">
                    Welcome to Seven Sons, where ethical farming meets exceptional flavor. Shop 200+ regeneratlively raised meats and more &horbar; delivered to your door with transparency, integrity, and care.
                </div>
            </div>
        </div>

        @if(!is_null($featured_collection))
            <div class="tw-pt-4 tw-pb-12 tw-mx-auto tw-max-w-7xl tw-bg-white tw-space-y-12">
                <livewire:theme.collection-product-list
                        :collection="$featured_collection"
                        :lazy="false"
                        type="standard"
                        :show_about="false"
                        :url="$featured_collection_url"
                        :add_breadcrumb_spacing="false"
                />
            </div>
        @endif

        @auth
            @if(auth()->user()->orders()->exists())
                <div class="tw-pt-4 tw-pb-12 tw-mx-auto tw-max-w-7xl tw-bg-white tw-space-y-12">
                    <livewire:theme.purchased-product-list
                            :lazy="false"
                            type="standard"
                            :show_about="false"
                            :add_breadcrumb_spacing="false"
                    />
                </div>
            @endif
        @endauth

        <div class="tw-pt-4 tw-pb-12 tw-mx-auto tw-max-w-7xl tw-bg-white tw-space-y-12">
            @foreach($categories as $index => $category)
                <livewire:theme.category-product-list
                        :category="$category"
                        :lazy="$index > 1"
                        :type="$category->slug === 'value-bundles' ? 'bundle' : 'standard' "
                        :show_about="false"
                        :add_breadcrumb_spacing="false"
                />
            @endforeach
        </div>
    </div>

    @if(auth()->check())
        <ul class="cartMenu">
            @if(auth()->user()->hasRecurringOrder())
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.subscription-item-count/>
                    </a>
                </li>
                <li>
                    <a @click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });" class="btn btn-brand btn-sm">
                        Edit Subscription <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @elseif( ! is_null($openOrder))
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Order {{ $openOrder->id }}', component: 'theme.order-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.order-item-count/>
                        <small>(edit)</small>
                    </a>
                </li>
            @else
                <li class="tw-reset" id="cartContainer">
                    <a @click="$dispatch('openPanel', { title: 'Shopping cart', component: 'theme.cart-side-panel' });" class="tw-cursor-pointer hover:tw-no-underline">
                        <i class="fa fa-shopping-basket" style="margin-right: 0.25rem;"></i>
                        <livewire:theme.cart-item-count/>
                    </a>
                </li>
                <li>
                    <a href="/checkout" class="btn btn-action btn-sm">
                        @lang('Checkout') <i class="fa fa-chevron-right hidden-xs"></i>
                    </a>
                </li>
            @endif
        </ul>
    @else
        <ul class="cartMenu">
            <li id="cartContainer">
                <a href="/login">Sign-in</a>
            </li>
            <li>
                <a href="/register" class="btn btn-action btn-sm">Create Account</a>
            </li>
        </ul>
    @endif
@endsection

@push('scripts')

    {!! $bodyTags ?? null !!}
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'ViewContent', {
                content_name: 'Store',
                content_category: '{{ request()->segment(2, 'storefront') }}'
            });
        }
    </script>
@endpush
