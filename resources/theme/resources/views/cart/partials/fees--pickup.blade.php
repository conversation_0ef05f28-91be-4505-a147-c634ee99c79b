@php
	/** @var App\Contracts\Cartable $cart */

	$delivery_method = $cart->cartLocation();
@endphp

@if($cart->cartDeliveryTotal() > 0 || $cart->cartLocationFeeTotal() > 0)
	<div class="cartItems__feesContainer">
		<table class="table cartItems__feesTable">
			<thead>
				<tr>
					<th>Fee</th>
					<th>Qty</th>
					<th>Rate</th>
					<th>Subtotal</th>
				</tr>
			</thead>

		{{-- Price per pound --}}
		@if($delivery_method->setting('delivery_fee_type') == 1)
			<tbody>
				<tr>
					<td>{{ getMessage('delivery_fee_title') }}</td>
					<td>{{ weight($cart->cartWeight()) }}</td>
					<td>
						&#36;{{ money($delivery_method->delivery_rate) }}/{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}
					</td>
					<td>&#36;{{ money($cart->cartDeliveryTotal()) }}</td>
				</tr>
				@foreach($delivery_method->fees as $fee)
					<tr>
						<td>{{ $fee->title }}</td>
						<td>1</td>
						<td>&#36;{{ money($fee->amount) }}</td>
						<td>&#36;{{ money($fee->amount) }}</td>
					</tr>
				@endforeach
			</tbody>
		@else
			<tbody>
				<tr>
					<td>{{ getMessage('delivery_fee_title') }}</td>
					<td>1</td>
					<td>
						&#36;{{ money($delivery_method->delivery_rate) }}
					</td>
					<td>&#36;{{ money($cart->cartDeliveryTotal()) }}</td>
				</tr>
				@foreach($delivery_method->fees as $fee)
					<tr>
						<td>{{ $fee->title }}</td>
						<td>1</td>
						<td>&#36;{{ money($fee->amount) }}</td>
						<td>&#36;{{ money($fee->amount) }}</td>
					</tr>
				@endforeach
			</tbody>
		@endif
		</table>
	</div>
@endif
