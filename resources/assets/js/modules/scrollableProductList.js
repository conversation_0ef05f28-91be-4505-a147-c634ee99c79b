import axios from 'axios';

axios.defaults.headers.common = {
    'X-Requested-With': 'XMLHttpRequest'
};

export default () => ({
    atStart: true,

    atEnd: false,

    hasShadows: true,

    init() {
        this.hasShadows = this.$refs.list?.clientWidth > this.$refs.container?.clientWidth;

        this.$refs.container?.addEventListener('scroll', () => {
            this.atStart = this.$refs.container.scrollLeft === 0;
            this.atEnd = this.$refs.container.scrollLeft >= (this.$refs.list.clientWidth - this.$refs.container.clientWidth);
        });
    },

    handleProductLoad() {
        this.atStart = this.$refs.container.scrollLeft === 0;
        this.atEnd = this.$refs.container.scrollLeft >= (this.$refs.list.clientWidth - this.$refs.container.clientWidth);
    },

    scrollLeft(selector) {
        const container = this.$refs.container;
        const containerLeft = container.scrollLeft;
        const elements = container.querySelectorAll(selector);

        let firstFullyShownIndex = null;
        elements.forEach((el, index) => {
            if (!firstFullyShownIndex && el.offsetLeft >= containerLeft) {
                firstFullyShownIndex = index;
            }
        });

        const lastElementInView = elements[Math.max(1, firstFullyShownIndex) - 1];
        const elRightEdge = lastElementInView.offsetLeft + lastElementInView.offsetWidth;
        this.left = Math.max(elRightEdge - container.clientWidth);

        container.scrollTo({
            left: this.left,
            behavior: 'smooth'
        });
    },

    scrollRight(selector) {
        const el = this.$refs.container;
        const containerScrollLeft = el.scrollLeft;
        const containerWidth = el.clientWidth;
        const elements = el.querySelectorAll(selector);

        let lastFullyVisible = null;

        elements.forEach((el, index) => {
            const elOffsetLeft = el.offsetLeft;
            const elOffsetRight = elOffsetLeft + el.offsetWidth;

            if (
                elOffsetLeft >= containerScrollLeft &&
                elOffsetRight <= containerScrollLeft + containerWidth
            ) {
                lastFullyVisible = index;
            }
        });

        if (lastFullyVisible + 1 === elements.length) {
            lastFullyVisible--;
        }

        this.$refs.container?.scrollTo({ left: elements[lastFullyVisible + 1].offsetLeft, behavior: 'smooth' });
    }
});
