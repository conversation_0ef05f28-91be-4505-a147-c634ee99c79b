<?php

return [

    'file_upload_prefix' => env('FILE_UPLOAD_PREFIX', ''),

	'cloud' => 's3',

    'disks' => [
        'tenants' => [
            'driver' => 'local',
            'root'   => storage_path('tenants'),
        ],

        'client' => [
            'driver' => 'local',
            'root'   => public_path(),
        ],

        'pages' => [
            'driver' => 'local',
            'root'   => null,
        ],

		'seeds' => [
			'driver' => 'local',
			'root'   => resource_path('seeds'),
		],

        'stubs' => [
            'driver' => 'local',
            'root'   => resource_path('stubs'),
        ],

        's3' => [
            'driver' => 's3',
            // Laravel 10 defaults are commented out
//            'key' => env('AWS_ACCESS_KEY_ID'),
//            'secret' => env('AWS_SECRET_ACCESS_KEY'),
//            'region' => env('AWS_DEFAULT_REGION'),
//            'bucket' => env('AWS_BUCKET'),

            'key'    => env('S3_KEY'),
            'secret' => env('S3_SECRET'),
            'region' => 'us-east-1',
            'bucket' => env('S3_BUCKET'),
            'options' => [
                'CacheControl' => 'max_age=31536000,public'
            ],
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],
    ],

];
