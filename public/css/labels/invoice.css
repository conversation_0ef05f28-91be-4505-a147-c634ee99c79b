body {
    margin: 0px;
    padding: 0px;
    font-family: Trebuchet MS,Lucida Grande,Lucida Sans Unicode,Lucida Sans,Tahoma,sans-serif; 
    font-size: 13px;
    color: #4d4d4d;
}

header {
    padding: 0.75cm 0.75cm 0.25cm 0.75cm;
    width: 100%;
    background-color: #fafafa;
    border-bottom: solid 1px #f3f3f3;
    box-sizing: border-box;
}

main {
    padding: 0.25cm 0.75cm;
}

#customer {
    border-bottom: solid 1px #f3f3f3;
    padding: 0.25cm 0;
}

.invoice {
    page-break-after: always;
    break-after: always;
}

.company-details {
    font-size: 14px;
}

.invoice-details {
    width: auto !important;
    margin-left: auto;
}

.invoice-details tr td:first-of-type {
    padding-right: 5px;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

table {
    width: 100%;
    border-collapse: collapse;
}

a[href]::after {
    content: none;
}

h1,h2,h3,h4,h5,h6,th,strong {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    font-weight: bold;
    margin: 0;
    padding: 0;
}

h1 {
    font-size: 40px;
    font-weight: normal;
}

h3 {
    margin: 0 0 5px 0;
}

ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

th {
    text-transform: uppercase;
}

.customer-details ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

#items {
    margin: 2em 0px 0.5em 0px;
}

#items table {
    width: 100%;
    text-align: left;
    margin-bottom: 0.5em;
    border-collapse: collapse;
}

#items table thead {
    background-color: #f3f3f3;
    padding: 5px;
}

#items table thead tr th {
    padding: 5px;
    font-weight: bold;
}

#items table tbody::before {
    content: '';
    display: block;
    height: 15px;
}

#items table tbody tr td {
    color: #3d3d3d;
    border-bottom: solid 1px #f3f3f3;
    padding: 0.20cm 0;
}

#items table tbody tr td {
    width: 18%;
}

#items table tbody tr td:first-of-type {
    width: 28%;
}

.totals {
    margin-top: 20px;
    display: block;
    text-align: right;
}

.final-total {
    display: block;
    margin-top: 5px;
    font-weight: bold;
    // padding: 6px 12px;
    // font-size: 20px;
    // background-color: #eee;
    // border-radius: 6px;
    // width: auto;
}

.text-right {
    text-align: right;
}

.notes {
    max-width: 75%;
}

.signature {
    font-family: 'Great Vibes', cursive;
    font-size: 30px;
}