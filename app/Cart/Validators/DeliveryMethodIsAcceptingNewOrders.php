<?php

namespace App\Cart\Validators;

use App\Contracts\Cartable;

class DeliveryMethodIsAcceptingNewOrders
{
    /**
     * @throws CartValidationException
     */
    public function handle(Cartable $cart, \Closure $next): Cartable
    {
        $delivery_method = $cart->cartLocation();

        if (is_null($delivery_method)) {
            return $next($cart);
        }

        if ( ! $delivery_method->isCurrentlyAcceptingOrders()) {
            throw new CartValidationException(
                rule: DeliveryMethodIsAcceptingNewOrders::class,
                message: "Ordering is currently closed for {$delivery_method->present()->title()}"
            );
        }

        return $next($cart);
    }
}