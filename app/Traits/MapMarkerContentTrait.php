<?php

namespace App\Traits;

use App\Models\Location;

trait MapMarkerContentTrait
{
    public function getMapMarkerContentAttribute(): string
    {
        $type = get_class($this);

        if ($this instanceof Location) { // it's an "other" location (retail, market, or restaurant)
            $buttonText = 'Shop Here';
            $buttonLinkOrAction = url($this->getAttribute('type') . '-locations/' . $this->getAttribute('slug'));
        } elseif (auth()->guest()) { // it's a pickup location and the user is not signed in
            $buttonText = 'Shop Here';
            $buttonLinkOrAction = url('/register?location_id=' . $this->getAttribute('id'));
        } else { // it's a pickup location and the user is signed in
            $buttonText = 'Shop Here';
            $buttonLinkOrAction = url('/account/delivery-options/' . $this->getAttribute('id'));
        }

        return view('map-marker-popup')
            ->with([
                'location' => $this,
                'type' => $type,
                'buttonText' => $buttonText,
                'buttonLinkOrAction' => $buttonLinkOrAction,
            ])
            ->render();
    }
}