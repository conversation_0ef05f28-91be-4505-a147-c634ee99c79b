<?php

namespace App\Mail;

use App\Models\GiftCertificate;
use App\Models\Template;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class GiftCardIssued extends TenantAwareMailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $subject;
    public $content;
    public $text;
    public $metadata;

    public function __construct(
        public int $gift_card_id
    ) { }

    public function build(): GiftCardIssued
    {
        $gift_card = GiftCertificate::findOrFail($this->gift_card_id);
        $template = Template::find(setting('email_issued_gift_card_template'));

        if (is_null($template)) {
            $template = $this->defaultTemplate();
        }

        $mergedContent = $template->mergeWithGiftCard($gift_card); // ensure the gift card tags are available
        $this->subject = $template->subject;
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();

        $this->from($template->getFromEmail(), $template->getFromName());
        $this->replyTo($template->getReplyToEmail(), $template->getFromName());

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'GiftCardIssued');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function defaultTemplate(): Template
    {
        $html = view('emails.default.gift-card-issued')
            ->render();

        $template = new Template;
        $template->title = 'Gift Card Confirmation';
        $template->slug ='gift-card-issued';
        $template->subject = 'e-Gift Card info!';
        $template->from_name = null;
        $template->from_email = null;
        $template->settings = [
            'backgroundColor' => '#FAFAFA',
            'fontFamily' => 'Arial',
            'track_links' => false,
            'campaign_name' => 'e-Gift Card info!',
            'campaign_content' => null
        ];
        $template->body = $html;
        $template->preview = $html;
        $template->needs_published = false;
        $template->plain_text = view('emails.default.gift-card-issued-plaintext')
            ->render();
        $template->group = 'order';
        $template->type = 'email';
        $template->created_at = now();
        $template->updated_at = now();

        return $template;
    }

    public function tags(): array
    {
        return ['email', 'gift-card-issued'];
    }
}
