<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\Template;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;

class RecurringOrderDeadline extends TenantAwareMailable implements ShouldQueue // done
{
    use Queueable, SerializesModels;

    public $order_id;
    public $from;
    public $subject;
    public $content;
    public $text;
    public $styles;
    public $metadata;
    protected $template_id;

    public function __construct(int $order_id, ?int $template_id = null)
    {
        $this->order_id = $order_id;
        $this->template_id = $template_id;
    }

    public function build()
    {
        $order = Order::forEmail()->findOrFail($this->order_id);

        $template = Template::find($this->template_id);

        if(!$template) {
            $template = $this->defaultTemplate();
        }

        $this->metadata['order_id'] = $order->id;
        $this->metadata['customer_id'] = $order->customer_id;

        $mergedContent = $template->mergeWithOrder($order);
        $this->subject = $template->subject;
        $this->content = $mergedContent->getHTML();
        $this->text = $mergedContent->getText();

        $this->from($template->getFromEmail(), $template->getFromName());
        $this->replyTo($template->getReplyToEmail(), $template->getFromName());

        if ($order->hasSecondaryEmail()) {
            $this->cc($order->secondaryEmail());
        }

        return $this->withSymfonyMessage(function (Email $m) {
            $headers = $m->getHeaders();
            $headers->addTextHeader('X-Mailgun-Variables', json_encode($this->metadata));
            $headers->addTextHeader('X-Mailgun-Tag', 'RecurringOrderReminder');
            $headers->addTextHeader('X-Mailgun-Tag', 'Order Packed');
        })
            ->view('emails.transactional-html')
            ->text('emails.transactional-text');
    }

    public function defaultTemplate()
    {
        $html = view('emails.default.subscription-deadline-reminder')->render();

        $template = new Template;
        $template->title = 'Subscription Deadline Reminder';
        $template->slug ='subscription-order-deadline-reminder';
        $template->subject = 'Need to update your order?';
        $template->from_name = null;
        $template->from_email = null;
        $template->settings = [
            'backgroundColor' => '#FAFAFA',
            'fontFamily' => 'Arial',
            'track_links' => false,
            'campaign_name' => 'Upcoming subscription reminder!',
            'campaign_content' => ''
        ];
        $template->body = $html;
        $template->preview = $html;
        $template->needs_published = false;
        $template->plain_text = view('emails.default.subscription-deadline-reminder-plaintext')->render();
        $template->group = 'order';
        $template->type = 'email';
        $template->created_at = now();
        $template->updated_at = now();

        return $template;
    }

    public function tags()
    {
        return ['email', 'recurring-order-reminder'];
    }
}
