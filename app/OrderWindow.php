<?php

namespace App;

use App\Models\Date;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Carbon;

class OrderWindow
{
    public function __construct(
        private Date $date
    ) {}


    public function generatesAtDatetime(): Carbon
    {
        return $this->deadlineDatetime()
            ->copy()
            ->subDays(app(SubscriptionSettingsService::class)->inventoryManagementDayCount());
    }

    public function deadlineDatetime(): Carbon
    {
        $orderDeadlineCutoff = $this->deadlineHour();

        $date = $this->date->order_end_date->copy();

        return $this->deadlineHour() === 24
            ? $date->endOfDay()
            : $date->addHours($orderDeadlineCutoff);
    }

    public function startDatetime(): Carbon
    {
        return $this->date->order_start_date->copy()->startOfDay();
    }

    public function deliveryDatetime(): Carbon
    {
        $date = $this->date->pickup_date->copy();

        return $date->endOfDay();
    }

    public function readyAtDatetime(): Carbon
    {
        $date = $this->date->pickup_date->copy();

        return $date->startOfDay();
    }

    public function dateId(): int
    {
        return $this->date->id;
    }

    public function originalDate(): Date
    {
        return $this->date;
    }

    public function scheduleId(): int
    {
        return $this->date->schedule_id;
    }

    private function deadlineHour(): int
    {
        return app(SettingsService::class)->deadlineHour();
    }

    public function toDateArray(): array
    {
        return [
            'id' => $this->dateId(),
            'order_start_date' => $this->startDatetime()->format('Y-m-d H:i:s'),
            'order_end_date' => $this->deadlineDatetime()->format('Y-m-d H:i:s'),
            'pickup_date' => $this->deliveryDatetime()->format('Y-m-d H:i:s'),
            'delivery_date' => $this->deliveryDatetime()->format('D, M jS'),
        ];
    }
}