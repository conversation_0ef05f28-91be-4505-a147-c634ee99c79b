<?php

namespace App\PickupManager;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Pickup;
use App\Models\Schedule;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PickupManagerController extends Controller
{
    public function index(): View
    {
        $schedules = Schedule::select(['id', 'slug', 'title'])
            ->with('nextDate', 'pickups')
            ->get();

        return view('pickupManager::schedules')
            ->with(['schedules' => $schedules->sortBy('nextDate.pickup_date')]);
    }

    public function schedule(string $schedule): View
    {
        if ($schedule === 'tba') {
            $schedule = new Schedule([
                'title' => 'No Schedule (TBA)',
                'id' => 'tba',
                'slug' => 'tba',
                'pickups' => Pickup::where('schedule_id', 0)->get()
            ]);
        } else {
            $schedule = Schedule::select(['id', 'slug', 'title'])
                ->where('slug', $schedule)
                ->with('nextDate', 'pickups')
                ->first();
        }

        return view('pickupManager::schedule')
            ->with(compact('schedule'));
    }

    /**
     * Show the orders page for the given pickup
     */
    public function pickup(string $schedule, string $pickup): View
    {
        if ($schedule === 'tba') {
            $schedule = new Schedule([
                'title' => 'No Schedule (TBA)',
                'id' => 'tba',
                'slug' => 'tba',
                'pickups' => Pickup::where('schedule_id', 0)->get()
            ]);
        } else {
            $schedule = Schedule::select(['id', 'slug', 'title'])
                ->where('slug', $schedule)->first();
        }

        $pickup = Pickup::query()
            ->select(['id', 'slug', 'title', 'street', 'city', 'state', 'zip', 'lat', 'lng', 'delivery_instructions'])
            ->where('slug', $pickup)
            ->first();

        return view('pickupManager::pickup')
            ->with([
                'paymentProcessor' => 'stripe',
                'schedule' => $schedule,
                'pickup' => $pickup
            ]);
    }

    public function orders(Request $request, int $locationId): JsonResponse
    {
        $pickup = Pickup::with('nextActiveDate')->findOrFail($locationId);

        $orders = Order::with(['items', 'fees', 'customer', 'discounts'])
            ->where('confirmed', true)
            ->where('pickup_id', $pickup->id)
            ->where('canceled', false)
            ->whereIn('status_id', (array) $request->input('order_status', [3, 4]))
            ->orderBy($request->input('order_by', 'customer_last_name'))
            ->get();

        return response()->json(['data' => $orders]);
    }

    public function order(int $locationId, int $orderId): JsonResponse
    {
        try {
            $order = Order::with(['items', 'fees', 'customer', 'discounts'])->findOrFail($orderId);
        } catch (ModelNotFoundException $exception) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json(['data' => $order]);
    }
}
