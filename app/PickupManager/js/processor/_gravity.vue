<template>
	<div class="gc-modal gc-modal-mask" id="updatePaymentModal" @click="hideModal('updatePaymentModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" style="max-width: 323px;" @click.stop>
				<form id="payment-form" :action="'/admin/users/' + order.customer_id + '/cards'" method="POST" novalidate>
					<input type="hidden" name="_token" :value="CSRF_TOKEN" />
					<div class="gc-modal-header">
						Add Credit Card
					</div>
	
					<div class="gc-modal-body">
					
						<div class="form-group">
                            <label for="cardholder_name">
                                Name on card
                            </label>
                            <input name="cardholder_name" class="field is-empty form-control" id="cardholder_name" />
                        </div>

						<div id="cardNumberContainer" class="cardInput">
                            <label for="card_number" style="display: block; width: 100%;">Card Number</label>
                        </div>

						<div id="expirationDateContainer" class="cardInput">
                            <label for="card_expiration_date" style="display: block;">Expiration Date</label>
                        </div>

                        <div id="securityCodeContainer" class="cardInput">
                            <label style="display: block;">Security Code</label>
                        </div>

					</div>
	
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('updatePaymentModal')">Cancel</button>
		                <button 
		                	type="submit" 
		                	class="btn btn-action" 
							id="payBtn"
		                >Add Card</button>
					</div>
				</form>	
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		props: ['order', 'processor'],

		data: function() {
			return {
				CSRF_TOKEN: document.getElementById('token').getAttribute('value'),
				saving: false,
				card: null,
				cardholder_name: ''
			}
		},

		created: function()
		{
			eventHub.on('updatePaymentModal:opened', function()
			{
				this.initCard()
			}.bind(this));
		},

		methods: {

			initCard() {
				let inputStyle = {"height": "36px", "padding": "0px 12px", "border": "solid 1px #e5e6e6", "max-width": "100%"}
				this.getToken().then(function(transactionToken) {
					// Initialize the hosted fields
					var hosted = emergepayFormFields.init({
						// (required) Used to set up each field
						transactionToken: transactionToken,
						// (required) The type of transaction to run
						transactionType: "CreditSaveCard",
						// (optional) Configure which fields to use and the id's of the elements to append each field to
						fieldSetUp: {
								// These fields are valid for credit card transactions
								cardNumber: {
									appendToSelector: "cardNumberContainer",
									useField: true,
									// optional, see styles section above for more information
									styles: inputStyle
								},
								cardExpirationDate: {
									appendToSelector: "expirationDateContainer",
									useField: true,
									// optional, see styles section above for more information
									styles: inputStyle
								},
								cardSecurityCode: {
									appendToSelector: "securityCodeContainer",
									useField: true,
									// optional, see styles section above for more information
									styles: inputStyle
								},
								// These fields are valid for all transaction types
								totalAmount: {
									appendToSelector: "amountContainer",
									useField: false,
									// optional, see styles section above for more information
									styles: {}
								},
								externalTranId: { 
									useField: false,
									// optional, see styles section above for more information
									styles: {}
								}
							},
						// (optional) If there is a validation error for a field, the styles set in this object will be applied to the field
						fieldErrorStyles: {
							"border-color": "red"
						},
						// (optional) This callback function will be called when there is a validation error for a field.
						onFieldError: function (data) {
							$('#payBtn').prop('disabled', false);
							$('#payBtn').html('Add Card')
						},
						// (optional) Callback function that gets called after a successful transaction
						onTransactionSuccess: function (approvalData) {
								var form = document.getElementById('payment-form');

								var token = document.createElement('input');
								token.setAttribute('type', 'hidden');
								token.setAttribute('name', 'token');
								token.setAttribute('value', approvalData.uniqueTransId);
								form.appendChild(token);

								var accountCardType = document.createElement('input');
								accountCardType.setAttribute('type', 'hidden');
								accountCardType.setAttribute('name', 'accountCardType');
								accountCardType.setAttribute('value', approvalData.accountCardType);
								form.appendChild(accountCardType);

								var accountExpiryDate = document.createElement('input');
								accountExpiryDate.setAttribute('type', 'hidden');
								accountExpiryDate.setAttribute('name', 'accountExpiryDate');
								accountExpiryDate.setAttribute('value', approvalData.accountExpiryDate);
								form.appendChild(accountExpiryDate);

								var maskedAccount = document.createElement('input');
								maskedAccount.setAttribute('type', 'hidden');
								maskedAccount.setAttribute('name', 'maskedAccount');
								maskedAccount.setAttribute('value', approvalData.maskedAccount);
								form.appendChild(maskedAccount);

								// Submit the form
								form.submit();

								$('#payBtn').prop('disabled', false);
								$('#payBtn').html('Add Card')
						},
						// (optional) Callback function that gets called after a failure occurs during the transaction (such as a declined card)
						onTransactionFailure: function (failureData) {
							$('#payBtn').html('Add Card')
						}
					});

					$('#payBtn').on("click", function (event) {
						event.preventDefault();
						$(this).prop('disabled', true);
						$(this).html('Saving Card... <i class="fa fa-spinner fa-spin"></i>');
						//run the transaction if all fields are valid
						hosted.process();
					});
				});
			},

			getToken() {
				let CSRF_TOKEN = this.CSRF_TOKEN
				return new Promise(function (resolve, reject) {
					$.ajax({
						url: '/payments/gravity/start-transaction',
						type: 'POST',
						dataType: 'json',
						contentType: 'application/json',
						headers: {
							"X-CSRF-TOKEN": CSRF_TOKEN
						}
					})
					.done(function(data) {
						if (data.transactionToken) resolve(data.transactionToken);
						else reject('Error getting transaction token');
					})
					.fail(function(err) {
						reject(err);
					});
				});
			}
		}
	}
</script>

<style scoped>
.cardInput {
	height: 74px; 
	margin-bottom: 1rem; 
	width: 100%;
	flex: 1 1 auto;
	max-width: 100%;
}
.cardInput iframe {
	width: 100% !important;
}
</style>