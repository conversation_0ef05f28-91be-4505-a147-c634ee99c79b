@extends('pickupManager::layout')

@section('content')
    <nav class="navigation-container hidden-print navigationBorder-top">
        <div class="toolbar shadow-none mb-0 pa-md pt-sm pb-sm b-none">
            <div class="toolbar__breadcrumbs flex align-items-m">
                <a href="/admin" class="flex-item mr-md">
                    <svg width="32" height="26" viewBox="0 0 32 26" xmlns="http://www.w3.org/2000/svg"><title>GrazeCart</title><g fill="none" fill-rule="evenodd"><path d="M28 18H11.3l-7-16H0V0h5.6l7 16H28v2" fill="#4D4D4D"/><path d="M18 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2zM14 14h14l.8-2 .8-2 .8-2h-4.8v2h1.7l-.7 2H16.3L14 5H31l.8-2.2H10L14 14M26.7 22.4c0 1.7-1.4 3-3 3-1.7 0-3-1.3-3-3 0-1.6 1.3-3 3-3 1.6 0 3 1.4 3 3zm-1 0c0 1-1 2-2 2s-2-1-2-2 1-2 2-2 2 1 2 2z" fill="#34B393"/></g></svg>
                </a>
                <ol class="breadcrumb-toolbar flex-item mt-0">
                    <li><a href="/admin/pickup-manager">Schedules</a></li>
                    <li>{{ $schedule->title }}</li>
                </ol>
            </div>
            <div class="toolbar__buttons flex">

            </div>
        </div>
    </nav>
    <ul class="border br-right-0 br-left-0">
    @foreach($schedule->pickups as $pickup)
        <li class="list-group-item border-light flex align-items-m br-0">
            <a href="/admin/pickup-manager/{{ $schedule->slug }}/{{ $pickup->slug }}" class="block pa-md flex-item-fill">
                {{ $pickup->title }}
                <div class="fs-sm text-gray-medium mt-xs">
                    {{ $pickup->street }}, {{ $pickup->city}} {{ $pickup->state }} {{ $pickup->zip }}
                </div>
            </a>
            <a href="/admin/pickup-manager/{{ $schedule->slug }}/{{ $pickup->slug }}" class="text-gray block pa-md flex-item push-right">
                <i class="fas fa-chevron-right"></i>
            </a>
            
        </li>
    @endforeach
    </ul>
@stop
