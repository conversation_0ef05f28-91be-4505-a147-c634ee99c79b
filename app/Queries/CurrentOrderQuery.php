<?php

namespace App\Queries;

use App\Models\Order;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Collection;

class CurrentOrderQuery
{
    public function run(User $user, array $select = ["*"]): ?Order
    {
        /** @var Collection<int, Order> $open_orders */
        $open_orders = $user->orders()
            ->open()
            ->select($select)
            ->where('status_id', '<>', OrderStatus::preOrder())
            ->get();

        if ($open_orders->isEmpty()) {
            return null;
        }

        // try to find a subscription order
        $order = $open_orders->first(function (Order $order) {
            return $order->isFromBlueprint()
                && ! $order->deadlineHasPassed();
        });

        if (is_null($order)) {
            // try to find the most-recent, open one-off order
            $order = $open_orders->last(function (Order $order) {
                return ! $order->isFromBlueprint()
                    && ! $order->deadlineHasPassed();
            });
        }

        return $order;
    }
}
