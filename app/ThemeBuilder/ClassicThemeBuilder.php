<?php

namespace App\ThemeBuilder;

use App\Contracts\ThemeBuilder as Themeable;
use App\Models\Collection;
use App\Models\Media;
use App\Models\Page;
use App\Models\Post;
use App\Models\Product;
use App\Models\Protocol;
use App\Models\Recipe;
use App\Models\Vendor;
use App\Models\Widget;
use App\Repositories\Widgets\BlogWidgetRepository;
use App\Repositories\Widgets\ProductWidgetRepository;
use App\Repositories\Widgets\ProtocolWidgetRepository;
use App\Repositories\Widgets\RecipeWidgetRepository;
use App\Repositories\Widgets\VendorWidgetRepository;
use Illuminate\Support\Collection as IlluminateCollection;

class ClassicThemeBuilder implements Themeable
{
    protected int $count = 0;

    /**
     * @return IlluminateCollection<int, Post>
     */
    public function getFeaturedPosts(Widget $widget): IlluminateCollection
    {
        return (new BlogWidgetRepository($widget))->get();
    }

    /**
     * @return IlluminateCollection<int, Recipe>
     */
    public function getFeaturedRecipes(Widget $widget): IlluminateCollection
    {
        return (new RecipeWidgetRepository($widget))->get();
    }

    /**
     * @return IlluminateCollection<int, Product>
     */
    public function getFeaturedProducts(Widget $widget, ?int $delivery_method_id = null, ?int $price_group_id = null): IlluminateCollection
    {
        return (new ProductWidgetRepository($widget, $delivery_method_id, $price_group_id))->get();
    }


    /**
     * @return IlluminateCollection<int, Protocol>
     */
    public function getProtocols(Widget $widget): IlluminateCollection
    {
        return (new ProtocolWidgetRepository($widget))->get();
    }

    /**
     * @return IlluminateCollection<int, Vendor>
     */
    public function getVendors(Widget $widget): IlluminateCollection
    {
        return (new VendorWidgetRepository($widget))->get();
    }

    public function renderPageHTML(Page $page, bool $authenticated): string
    {
        $rowCount = 0;
        $startRow = null;
        $widgets = [];

        foreach ($page->widgets as $widget) {
            if (
                ($widget->setting('show_when_auth', false) === $authenticated)
                || ($widget->setting('show_when_guest', false) === ! $authenticated)
            ) {
                $width = $widget->setting('layout_width', 'full-width');

                if ($width == 'half-width') {
                    if (is_null($startRow) || $rowCount >= 2) {
                        $rowCount = 0;
                        $startRow = $widget->id;
                    }

                    $widgets[$startRow][] = $widget;
                    $rowCount++;
                }

                if ($width == 'full-width') {
                    $startRow = null;
                    $widgets[$widget->id][] = $widget;
                }
            }
        }

        $output = '';

        foreach ($widgets as $widget) {
            $output .= count($widget) > 1 ? $this->buildRow($widget) : $this->buildWidget($widget[0]);
        }

        return $output;
    }

    /**
     * Build the widget rows.
     * @param array<int, Widget> $widgets
     */
    private function buildRow(array $widgets): string
    {
        $background = $widgets[0]->setting('background', $widgets[1]->setting('background', 'transparent'));

        $output = '<!-- Start Page Widget Row --><div class="pageWidget__row" id="pageWidgetRow--' . $widgets[0]->id . '" style="background-color:' . $background . ';"><div class="pageWidget__rowInnerContainer">';

        // Build the first widget.
        $output .= $this->buildWidget($widgets[0], 'half-width', 1);
        // Build second widget
        $output .= $this->buildWidget($widgets[1], 'half-width', 2);

        $output .= '</div></div><!-- End Page Widget Row -->';
        return $output;
    }

    private function buildWidget(Widget $widget, string $class = 'full-width', int $index = 0): string
    {
        $view = "theme::widgets.{$widget->template}.{$widget->template}";

        if ( ! view()->exists($view))  return '';

        $output = '<!-- Start ' . $widget->title . ' Widget -->';
        $output .= '<div data-block="' . $widget->id . '" class="contentWidget pageWidget__slot pageWidget__slot--' . $class . ' pageWidget__slot--' . $index . '" id="pageWidget--' . $widget->id . '" style="background-color:' . Media::s3ToCloudfront($widget->setting('background', 'transparent')) . ';">';
        $output .= view($view)
            ->with(['widget' => $widget])
            ->render();

        $output .= '</div><!-- End ' . $widget->title . ' Widget -->';

        return $output;
    }

    public function buildFooterWidgets(): string
    {
        $output = '';
        $widgets = Widget::query()
            ->where('page_id', 0)
            ->where('template', '!=', '')
            ->orderBy('sort')
            ->get();

        foreach ($widgets as $widget) {
            if (auth()->check() == $widget->setting('show_when_auth') || auth()->guest() == $widget->setting('show_when_guest')) {
                $width = $widget->setting('layout_width', 'full-width');
                $output .= '<!-- Start ' . $widget->title . ' Widget -->';
                $output .= '<div class="footerWidget__slot footerWidget__slot--' . $width . '" id="footerWidget--' . $widget->id . '">';
                $output .= view('theme::widgets.' . $widget->template . '.' . $widget->template)->with(['widget' => $widget, 'themeService' => $this])->render();
                $output .= '</div><!-- End ' . $widget->title . ' Widget -->';
            }
        }

        return $output;
    }

    public function renderPageStyles(Page $page): string
    {
        $output = '';

        foreach ($page->widgets as $widget) {
            $output .= $widget->renderStyles();
        }

        return $output;
    }

    /**
     * @return IlluminateCollection<int, array{
     *     id: int,
     *     url: string,
     *     src: string,
     *     visible: bool,
     *     show_caption: bool,
     *     caption: string,
     *     alt_text: string,
     *     click_action: string,
     *     grid_span: int,
     *     height: int,
     *     width: int,
     *     orientation: string
     * }>
     */
    public function buildPhotoGalleryWidget(): IlluminateCollection
    {
        return Media::query()
            ->orderBy('created_at')
            ->limit(3)
            ->get()
            ->map(function (Media $photo): array {
                return [
                    'id' => $photo->id,
                    'url' => $photo->path,
                    'src' => $photo->path,
                    'visible' => true,
                    'show_caption' => true,
                    'caption' => $photo->title,
                    'alt_text' => $photo->title,
                    'click_action' => 'lightbox',
                    'grid_span' => 3,
                    'height' => $photo->height,
                    'width' => $photo->width,
                    'orientation' => $photo->layout
                ];
            });
    }

    /**
     * @return IlluminateCollection<int, array{
     *     id: int,
     *     caption: string,
     *     subcaption: null,
     *     url: string,
     *     src: string,
     *     visible: bool,
     *     show_caption: bool,
     *     click_action: string,
     *     grid_span: int,
     *     height: string,
     *     width: string
     * }>
     */
    public function buildWhatWeOfferWidget(): IlluminateCollection
    {
        return Collection::query()
            ->with(['products' => fn($query) => $query->limit(1)])
            ->orderBy('created_at')
            ->limit(6)
            ->get()
            ->map(fn(Collection $collection) => [
                'id' => $collection->id,
                'caption' => $collection->title,
                'subcaption' => null,
                'url' => '/store/' . $collection->slug,
                'src' => $collection->products->isNotEmpty()
                    ? $collection->products->first()->cover_photo
                    : 'https://s3.amazonaws.com/grazecart/stockphotos/photo-grid-placeholder.jpg',
                'visible' => true,
                'show_caption' => true,
                'click_action' => 'link',
                'grid_span' => 3,
                'height' => 'auto',
                'width' => 'auto'
            ]);
    }

    public function addToCartLabel(): string
    {
        return __('messages.cart.add_to_cart');
    }

    public function addToOrderLabel(): string
    {
        return __('messages.cart.add_to_order');
    }

    public function addToSubscriptionLabel(): string
    {
        return __('messages.cart.add_to_subscription');
    }
}
