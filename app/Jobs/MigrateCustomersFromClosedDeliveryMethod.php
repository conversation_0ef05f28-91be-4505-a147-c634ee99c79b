<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class MigrateCustomersFromClosedDeliveryMethod implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public function __construct(
        public int $delivery_method_id
    ) {}

    public function handle()
    {
        $users = User::query()
            ->where('pickup_point', $this->delivery_method_id)
            ->chunkById(250, function (Collection $users) {
                $users->each(fn(User $user) =>
                    MigrateCustomerFromClosedDeliveryMethod::dispatch(
                        $this->delivery_method_id,
                        $user->id
                    )
                );
            });
    }
}
