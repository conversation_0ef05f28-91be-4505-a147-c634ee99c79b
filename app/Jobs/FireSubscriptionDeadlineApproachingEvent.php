<?php

namespace App\Jobs;

use App\Events\Subscription\RecurringOrderApproachingDeadline;
use App\Models\Order;
use App\Models\Schedule;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FireSubscriptionDeadlineApproachingEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $order_id,
        public int $schedule_id,
    ) {}

    public int $tries = 1;

    public function handle(): void
    {
        $order = Order::find($this->order_id);
        $schedule = Schedule::find($this->schedule_id);

        if (is_null($order) || $order->isCanceled() || is_null($schedule)) return;

        event(new RecurringOrderApproachingDeadline($order, $schedule));
    }

    public function tags(): array
    {
        return ['subscriptions'];
    }
}
