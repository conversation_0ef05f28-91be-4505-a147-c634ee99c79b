<?php

namespace App\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Traits\TenantContextMiddleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordBatchOfOrdersInDrip implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, TenantContextMiddleware;

    public function __construct(
        public array $orders
    ) {}

    public function handle(): void
    {
        if (empty($this->orders)) return;

        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $drip->shopperActivity()?->recordBatchOfOrders($this->orders);
    }

    public function tags(): array
    {
        return ['subscriptions', 'drip'];
    }
}
