<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class GenerateSubscriptionDemandView implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        DB::statement('CREATE OR REPLACE VIEW subscription_demand_by_pack_deadline AS
            SELECT
                pack_deadline_at,
                CASE
                    WHEN bundle_product.product_id IS NOT NULL THEN bundle_product.product_id
                    ELSE roi.product_id
                END AS product_id,
                SUM(
                    CASE
                        WHEN bundle_product.product_id IS NOT NULL THEN (roi.qty * bundle_product.qty)
                        ELSE roi.qty
                    END
                ) AS total_product_qty
            FROM
                recurring_order_items roi
                JOIN recurring_orders ON recurring_orders.id = roi.order_id
                LEFT JOIN bundle_product ON bundle_product.bundle_id = roi.product_id
                JOIN (
                    SELECT
                        ro.id AS recurring_order_id,
                        CASE
                            WHEN s.pack_deadline_hours_before IS NULL THEN 0
                            ELSE s.pack_deadline_hours_before
                        END + CASE
                            WHEN s.pack_deadline_days_before IS NULL THEN 0
                            ELSE s.pack_deadline_days_before * 24
                        END AS total_pack_hours,
                        DATE_SUB(
                            ready_at,
                            INTERVAL(
                                CASE
                                    WHEN s.pack_deadline_hours_before IS NULL THEN 0
                                    ELSE s.pack_deadline_hours_before
                                END + CASE
                                    WHEN s.pack_deadline_days_before IS NULL THEN 0
                                    ELSE s.pack_deadline_days_before * 24
                                END
                            ) HOUR
                        ) AS pack_deadline_at
                    FROM
                        recurring_orders ro
                        JOIN pickups pu ON pu.id = ro.fulfillment_id
                        JOIN schedules s ON s.id = pu.schedule_id
                    WHERE
                        ro.deleted_at IS NULL
                ) pack_deadlines ON pack_deadlines.recurring_order_id = roi.order_id
            WHERE
                recurring_orders.deleted_at IS NULL
            GROUP BY
                pack_deadlines.pack_deadline_at,
                product_id
            ORDER BY
                pack_deadlines.pack_deadline_at,
                product_id;
        ');
    }
}
