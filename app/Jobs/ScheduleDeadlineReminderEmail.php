<?php

namespace App\Jobs;

use App\Models\Schedule;
use App\Services\LastChanceService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class ScheduleDeadlineReminderEmail
{
    public $settings;
    public $schedule;

    public function handle(int $schedule_id): void
    {
        /** @var Schedule|null $schedule */
        $schedule = Schedule::with(['pickups', 'nextOrderWindow'])
            ->where('active', true)
            ->find($schedule_id);

        if (is_null($schedule)) {
            return;
        }

        // Check for reminder date
        if ($date = $schedule->reminderDate()) {
            $scheduleDate = $schedule->getFormattedReminderDate($date);

            app(LastChanceService::class)
                ->sendEmail(
                    $schedule,
                    $schedule->template_id,
                    $scheduleDate->toRfc2822String()
                );

            $date->scheduled_at = $scheduleDate;
            $date->save();

        } elseif ($schedule->secondary_reminder_enabled && $date = $schedule->secondaryReminderDate()) {
            $scheduleDate = $schedule->getFormattedSecondaryReminderDate($date);
            app(LastChanceService::class)->sendEmail(
                $schedule,
                $schedule->secondary_template_id ?? $schedule->template_id,
                $scheduleDate->toRfc2822String()
            );
            $date->secondary_scheduled_at = $scheduleDate;
            $date->save();
        }
    }
}
