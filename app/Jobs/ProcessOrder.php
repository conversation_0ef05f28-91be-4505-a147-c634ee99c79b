<?php

namespace App\Jobs;

use App\Models\Order;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $order_id
    ) {}

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        /** @var Order|null $order */
        $order = Order::find($this->order_id);

        if (is_null($order)) return;

        $order->process();
    }
}
