<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use App\Models\Template;
use App\Rules\CommaSeparatedPhoneNumbers;
use App\Rules\ValidPhoneNumber;
use Illuminate\Validation\Rule;

class UpdateSettingsRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function sanitizeInput()
    {
        $settings = $this->settings;
        foreach ($settings as $key => $value) {
            if (method_exists($this, $key)) {
                $settings[$key] = $this->{$key}($value);
            }
        }

        $this->merge(['settings' => $settings]);

        return $this->all();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'settings.farm_name' => ['sometimes', 'required',],
            'settings.map_center_latitude' => ['nullable', 'numeric'],
            'settings.map_center_longitude' => ['nullable', 'numeric'],
            'settings.map_center_zoom' => ['nullable', 'numeric'],
            'settings.email_general' => ['sometimes', 'required', 'email',],
            'settings.email_contact' => ['sometimes', 'required', 'email',],
            'settings.ordering_mode' => ['nullable', 'boolean'],
            'settings.estimated_weight_check' => ['nullable', 'numeric', 'min:0'],
            'settings.layout_page_break' => ['nullable'],
            'settings.print_template_packing' => ['nullable'],
            'settings.show_customer_notes_on_invoice' => ['nullable', 'boolean'],
            'settings.print_template_invoice' => ['nullable'],
            'settings.order_items_sort_order' => ['nullable'],
            'settings.order_items_sort_order_2' => ['nullable'],
            'settings.treat_custom_sort_as_integer' => ['nullable', 'boolean'],
            'settings.default_parcel_count' => ['nullable', 'numeric', 'min:0', 'max:10'],
            'settings.process_order_show' => ['nullable', 'boolean'],
            'settings.process_order_status' => ['nullable'],
            'settings.process_order_email' => ['nullable'],
            'settings.process_order_charge' => ['nullable', 'boolean'],
            'settings.store_products_per_page' => ['integer', 'min:0',],
            'settings.user_registration_credit' => ['numeric',],
            'settings.blog_posts_per_page' => ['numeric', 'min:1',],
            'settings.pickup_results_count' => ['integer','min:0'],
            'settings.pickup_results_radius' => ['integer','min:0'],
            'settings.referral_bonus' => ['numeric','min:0'],
            'settings.referral_payout' => ['numeric','min:0'],
            'settings.email_issued_gift_card_template' => ['nullable', 'integer', Rule::exists(Template::class, 'id')],
            'settings.email_gift_purchased_template' => ['nullable', 'integer', Rule::exists(Template::class, 'id')],
            'settings.out_of_stock_sms_notifications' => ['nullable', 'boolean'],
            'settings.out_of_stock_sms_notifications_contacts' => ['nullable', 'string', new CommaSeparatedPhoneNumbers],
        ];
    }

    public function messages(): array
    {
        return [
            'settings.farm_name.required' => 'Farm Name is required',
            'settings.store_products_per_page.integer' => 'Products Per Page must be a number',
            'settings.store_products_per_page.min' => 'Products Per Page can not be negative',
            'settings.email_general.email' => 'Contact Email Address must be a valid email',
            'settings.email_contact.email' => 'Contact Email Address must be a valid email',
            'settings.user_registration_credit.numeric' => 'Registration Incentive must be a number',
            'settings.blog_posts_per_page.numeric' => 'Blog Posts must be a number',
            'settings.blog_posts_per_page.min' => 'Blog Posts must be at least 1',
            'settings.pickup_results_radius.integer' => 'Registration search radius must be a whole number',
            'settings.pickup_results_radius.min' => 'Registration search radius can not be negative',
            'settings.pickup_results_count.integer' => 'Registration location count must be a whole number',
            'settings.pickup_results_count.min' => 'Registration location count can not be negative',
            'settings.map_center_latitude.numeric' => 'Latitude must be a number',
            'settings.map_center_longitude.numeric' => 'Longitude must be a number',
            'settings.map_center_zoom.numeric' => 'Zoom level must be a number',
            'settings.ordering_mode.boolean' => 'Allow customers to modify orders field can only be true or false',
            'settings.estimated_weight_check.numeric' => 'Estimated weight warning field must be a number',
            'settings.estimated_weight_check.min' => 'Estimated weight warning field can not be negative',
            'settings.show_customer_notes_on_invoice.boolean' => 'Customer notes on invoice field can only be true or false',
            'settings.default_parcel_count.min' => 'Default parcel count field can not be negative',
            'settings.default_parcel_count.max' => 'Default parcel count field can not exceed 10',
            'settings.default_parcel_count.numeric' => 'Default parcel count field must be a number between 0 - 10',
            'settings.process_order_show.boolean' => 'Enable process order button field can only be true or false',
            'settings.process_order_charge.boolean' => 'Charge customer\'s card field can only be true or false',
        ];
    }

    protected function map_center_longitude($value)
    {
        return trim($value);
    }

    // Input mutations
    protected function referral_bonus($value)
    {
        return formatCurrencyForDB($value);
    }

    protected function referral_payout($value)
    {
        return formatCurrencyForDB($value);
    }
}
