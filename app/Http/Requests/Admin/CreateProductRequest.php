<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use App\Support\Enums\ProductType;
use Illuminate\Validation\Rule;

class CreateProductRequest extends Request
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $regex = '/^[0-9,.]*$/';

        return [
            'title' => ['required'],
            'type_id' => ['nullable', 'integer', Rule::in(ProductType::all())],
            'unit_of_issue' => ['sometimes', 'required'],
            'unit_price' => ['sometimes', 'required', 'min:0', 'regex:' . $regex],
            'weight' => ['sometimes', 'required', 'numeric', 'min:0'],
            'fulfillment_instructions' => ['nullable', 'string', 'max:255'],
        ];
    }
}
