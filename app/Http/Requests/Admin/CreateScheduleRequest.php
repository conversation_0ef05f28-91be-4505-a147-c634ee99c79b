<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;

class CreateScheduleRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
            ],
        ];
    }
}
