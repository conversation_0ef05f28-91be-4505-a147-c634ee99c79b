<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCouponRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'discount_type' => ['required', Rule::in(['fixed', 'percentage', 'delivery'])],
            'application_type' => ['required'],
            'code' => ['required', 'unique:coupons,code,' . $this->input('id'), 'max:15'],
            'discount_amount_formatted' => ['required_if:discount_type,fixed', 'numeric', 'min:0'],
            'discount_percentage' => [Rule::when($this->input('discount_type') === 'percentage', ['required', 'integer', 'min:1', 'max:100'])],
            'min_order' => ['nullable', 'boolean'],
            'min_order_amount_formatted' => ['required_if:min_order,true', 'numeric', 'min:0'],
            'limit_usage' => ['nullable', 'boolean'],
            'max_uses' => ['required_if:limit_usage,true', 'integer', 'min:1'],
            'expires' => ['nullable', 'boolean'],
            'expires_at_formatted' => ['nullable', 'required_if:expires,true', 'date', 'date_format:Y-m-d'],
            'settings' => ['nullable', 'array'],
            'once_per_customer' => ['nullable', 'boolean'],
        ];

    }

    public function messages(): array
    {
        return [
            'discount_amount_formatted.required_if' => 'The discount amount field is required.',
            'discount_amount_formatted.min' => 'The discount amount field must be at least 0.',
            'discount_amount_formatted.numeric' => 'The discount amount field must be a number.',
            'discount_percentage.required_if' => 'The discount percentage field is required.',
            'discount_percentage.min' => 'The discount percentage field must be at least 1.',
            'discount_percentage.max' => 'The discount percentage field must be less than 100.',
            'max_uses.required_if' => 'The maximum usage limit field is required.',
            'max_uses.min' => 'The maximum usage limit field must be at least 1.',
            'max_uses.integer' => 'The maximum usage limit field must be an integer.',
            'min_order_amount_formatted.required_if' => 'The minimum order amount field is required.',
            'min_order_amount_formatted.min' => 'The minimum order amount field must be at least 0.',
            'min_order_amount_formatted.numeric' => 'The minimum order amount field must be a number.',
            'expires_at_formatted.required_if' => 'The expires at field is required.',
            'expires_at_formatted.date' => 'The expires at field must be a valid date.',
            'expires_at_formatted.date_format' => 'The expires at field must match the format Y-m-d.',
        ];
    }
}
