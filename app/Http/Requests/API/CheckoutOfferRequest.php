<?php

namespace App\Http\Requests\API;

use App\Cart\Offer;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CheckoutOfferRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', Rule::in([Offer::SUBSCRIPTION])],
            'params' => ['array'],
            'params.purchase_type' => ['required_if:name,'.Offer::SUBSCRIPTION, Rule::in(['one_time_purchase', 'recurring'])],
            'params.frequency' => ['nullable', 'integer', Rule::in([7,14,28,42,56])],
            'params.product_incentive_id' => ['nullable', Rule::exists('products', 'id')],
        ];
    }
}
