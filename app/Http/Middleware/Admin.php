<?php

namespace App\Http\Middleware;

use Symfony\Component\HttpFoundation\Response;
use Closure;
use Illuminate\Http\Request;

class Admin
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user()->isOwner()) {
            error('Only administrators have access to this function.');
            return redirect('/admin');
        }

        return $next($request);
    }
}
