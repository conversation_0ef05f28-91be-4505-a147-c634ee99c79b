<?php

namespace App\Http\Middleware\Theme;

use App\Contracts\CartService;
use App\Livewire\Theme\FetchesCart;
use App\Services\OfferService;
use Illuminate\Http\Request;

class EnsureCartHasOffers
{
    use FetchesCart;

    public function handle(Request $request, \Closure $next): \Symfony\Component\HttpFoundation\Response
    {
        $shopper = request()->shopper();

        $cart = app(CartService::class)
            ->find(shopper_type: $shopper['type'], shopper_id: $shopper['id']);

        $offers = app(OfferService::class)->forCart($cart);

        if ($offers->isEmpty()) {
            return redirect(route('checkout.confirm.show'));
        }

        return $next($request);
    }
}
