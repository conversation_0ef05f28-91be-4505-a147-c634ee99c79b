<?php

namespace App\Http\Resources;

use App\Models\Event;
use Illuminate\Http\Resources\Json\JsonResource;

class UserCreditResource extends JsonResource
{
    public function toArray($request): array
    {
        /** @var Event $event */
        $event = $this->resource;

        return [
            'id' => $event->id,
            'amount' => $event->metadata->amount,
            'amount_formatted' => money($event->metadata->amount),
            'description' => $event->description,
            'created_at' => $event->created_at->format('m/d/Y g:i A'),
            'event_id' => $event->event_id,
            'who' => $event->user->full_name ?? null
        ];
    }
}
