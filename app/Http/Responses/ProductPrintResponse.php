<?php

namespace App\Http\Responses;

class ProductPrintResponse extends PrintResponse
{
    public function __construct(public $products, string $printType)
    {
        $this->template = $printType;
        $this->setStyle();
        $this->getView();
    }

    private function setStyle(): void
    {
        $this->style = config('print_template_' . $this->template, 'default');
    }

    public function toResponse($request)
    {
        return $this->view
            ->withType($this->template)
            ->withViewPath($this->viewPath)
            ->withCssPath($this->cssPath)
            ->withProducts($this->products);
    }
}
