<?php

namespace App\Http\Controllers\Theme;

use Illuminate\View\View;
use App\Events\ContactFormSubmitted;
use App\Http\Controllers\Controller;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use Illuminate\Http\Request;

class ContactFormController extends Controller
{
    public function __construct()
    {
        if (!app()->environment('testing')) {
            $this->middleware('throttle:5,10')->only('post');
        }
    }

    public function show(): View
    {
        return view('theme::pages.contact');
    }

    public function post(Request $request)
    {
        $request->validate(
            [
                'full_name' => 'required',
                'topic' => 'required',
                'email' => 'required|email',
                'body' => 'required',
                'subject' => [new HoneyPot],
                'timestamp' => [new HoneyTime]
            ],
            [
                'full_name.required' => 'Full name is required',
                'topic.required' => 'Subject is required',
                'email.required' => 'Email is required',
                'body.required' => 'Message is required'
            ]
        );

        $request['full_name'] = htmlentities($request['full_name']);
        $request['subject'] = htmlentities($request['subject']);
        $request['body'] = htmlentities($request['body']);

        event(new ContactFormSubmitted(
            request('topic'), request('email'), request('full_name'), request('body')
        ));

        flash('Thank you! Your message has been sent.');
        return back();
    }
}
