<?php

namespace App\Http\Controllers\Theme\Store;

use App\Events\Product\ProductWasViewed;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Theme\Concerns\FetchesCustomPage;
use App\Repositories\StoreRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\View\View;

class StoreController extends Controller
{
    use FetchesCustomPage;

    public function index(Request $request): View
    {
        session(['store_url' => $request->fullUrl()]);

        $repository = new StoreRepository($request);

        $pageTitle = setting('store_page_title', 'Seven Sons Farm Store');
        $heading = null;
        $subheading = setting('store_page_description');
        $collection = null;
        $tags = collect();

        $repository = app(StoreRepository::class);
        $categories = $repository->categoriesAndProducts();

        $featured_collection = $repository->getFeaturedCollection();
        $featured_collection_url = !empty(setting('featured_store_collection_url'))
            ? setting('featured_store_collection_url')
            : null;

        return view('theme::store.index', [
            'categories' =>  $categories,
            'featured_collection' => $featured_collection,
            'featured_collection_url' => $featured_collection_url,
            'pageTitle' => $pageTitle,
            'heading' => $heading,
            'subheading' => $subheading,
            'pageDescription' => $subheading,
            'tags' => $tags,
            'pageCanonical' => null,
        ]);

    }

    public function show(Request $request, string $slug)
    {
        try {
            $product = (new StoreRepository($request))
                ->getIndividualProduct($slug)
                ->firstOrFail();

        } catch (ModelNotFoundException $e) {
            error('The product you are looking for could not be found.');
            return back(301);
        }

        if ($user = auth()->user()) {
            ProductWasViewed::dispatch($user, $product);
        }

        return view('theme::store.show')
            ->with(compact('product'));
    }
}
