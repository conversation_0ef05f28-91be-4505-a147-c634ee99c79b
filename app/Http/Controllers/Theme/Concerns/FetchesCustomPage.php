<?php

namespace App\Http\Controllers\Theme\Concerns;

use App\Models\Page;

trait FetchesCustomPage
{
    /**
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function fetchCustomPage(string $slug)
    {
        $is_public = auth()->user()?->isCustomer() ?? true;

//        $page_cache_key = Page::cacheKeyForSlug(slug: $slug, is_public: $is_public);
//        $page = Cache::get($page_cache_key);

//        if (is_null($page)) {
            $page = Page::query()
                ->custom(slug: $slug, is_public: $is_public)
                ->firstOrFail();

//            Cache::put($page_cache_key, $page);
//        }

//        $html_cache_key = Page::htmlCacheKeyForSlug(slug: $slug, is_authenticated: $is_authenticated, is_public: $is_public);
//        $html = Cache::get($html_cache_key);

//        if (is_null($html)) {
            $html = $page->renderHTML($is_public);
//            Cache::put($html_cache_key, $html);
//        }

        return [$page, $html];
    }
}
