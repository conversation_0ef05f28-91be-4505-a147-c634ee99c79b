<?php

namespace App\Http\Controllers\Theme\Customer;

use Illuminate\View\View;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserSubscribedToSmsMarketing;
use App\Events\User\UserUnsubscribedFromNewsletter;
use App\Events\User\UserUnsubscribedFromSmsMarketing;
use App\Http\Controllers\Controller;
use App\Models\User;

class CustomerNotificationController extends Controller
{
    public function show(): View
    {
        return view('theme::customers.notifications')
            ->with('customer', auth()->user());
    }

    public function update()
    {
        request()->validate([
            'order_deadline_email_reminder' => ['boolean'],
            'newsletter' => ['boolean'],
            'sms_marketing' => ['boolean'],
        ]);

        /** @var User $user */
        $user = auth()->user();

        $user->order_deadline_email_reminder = request('order_deadline_email_reminder') ?? 0;
        $user->newsletter = request('newsletter') ?? 0;
        $user->subscribed_to_sms_marketing_at = request('sms_marketing') ? now() : null;

        $user->save();

        if ($user->wasChanged('newsletter')) {
            $user->newsletter ?
                event(new UserSubscribedToNewsletter($user)) :
                event(new UserUnsubscribedFromNewsletter($user));
        }

        if ($user->wasChanged('subscribed_to_sms_marketing_at')) {
            ! is_null($user->subscribed_to_sms_marketing_at)
                ? event(new UserSubscribedToSmsMarketing($user, 'on_customer_notification_preferences'))
                : event(new UserUnsubscribedFromSmsMarketing($user));
        }

        flash('Your notification settings have been saved.');

        return back();
    }
}
