<?php

namespace App\Http\Controllers\Theme\Order;

use Illuminate\Http\JsonResponse;
use App\Models\Coupon;
use App\Exceptions\CouponInvalidException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class OrderCouponController extends Controller
{
    public function index(Order $order)
    {
        if ($order->customer_id !== auth()->id()) {
            throw new ModelNotFoundException;
        }

        return $order->discounts()->get();
    }

    public function store(Order $order): JsonResponse
    {
        if ($order->customer_id !== auth()->id()) {
            throw new ModelNotFoundException;
        }

        $validated = request()->validate([
            'coupon_code' => ['required', 'exists:coupons,code']
        ]);

        $order->load('discounts');

        $coupon = Coupon::where('code', $validated['coupon_code'])->first();

        try {
            $coupon = $order->applyCoupon($coupon);
        } catch (CouponInvalidException $e) {
            return response()->json('The coupon could not be applied.', 409);
        }

        $order->updateTotals();

        return response()->json([
            'coupon' => $coupon,
            'summary' => view('theme::order.partials.summary')->with(compact('order'))->render(),
            'tax' => money($order->tax),
            'order_discount' => money($order->order_discount),
            'fees_subtotal' => money($order->fees_subtotal),
            'delivery_fee' => money($order->delivery_fee),
            'delivery_rate' => money($order->delivery_rate),
            'subtotal' => money($order->subtotal),
            'total' => money($order->total)
        ]);
    }

    public function destroy(Order $order, int $coupon): JsonResponse
    {
        if ($order->customer_id !== auth()->id()) {
            throw new ModelNotFoundException;
        }

        $order->removeCoupon($coupon)
            ->updateTotals();

        return response()->json([
            'summary' => view('theme::order.partials.summary')->with(compact('order'))->render(),
            'tax' => money($order->tax),
            'order_discount' => money($order->order_discount),
            'fees_subtotal' => money($order->fees_subtotal),
            'delivery_fee' => money($order->delivery_fee),
            'delivery_rate' => money($order->delivery_rate),
            'subtotal' => money($order->subtotal),
            'total' => money($order->total)
        ]);
    }
}
