<?php

namespace App\Http\Controllers\Theme;

use App\Events\User\LeadCreated;
use App\Events\User\UserSubscribedToNewsletter;
use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use App\Models\User;

class NewsletterController extends Controller
{
    public function store()
    {
        request()->validate([
            'email_address' => 'required|email',
            'full_name' => [new HoneyPot],
            'timestamp' => [new HoneyTime(2)]
        ]);

        if ($user = User::where('email', request('email_address'))->first()) {
            event(new UserSubscribedToNewsletter($user));
        } else {
            event(new LeadCreated($this->createLead()));
        }

        flash('Thank you for subscribing.');
        return back();
    }

    protected function createLead()
    {
        return Lead::create([
            'email' => request('email_address'),
            'ip_address' => request()->ip(),
            'first_name' => request('first_name', ''),
            'last_name' => request('last_name', ''),
            'city' => request('city', ''),
            'state' => request('state', ''),
            'zip' => request('postal_code', ''),
            'has_address' => false,
        ]);
    }
}
