<?php

namespace App\Http\Controllers\Theme;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\View\View;

class BlogAuthorController extends Controller
{
    public function show(string $author_slug): View
    {
        $author = $this->author($author_slug);

        $post_query = is_null($author)
            ? $this->anonymousPosts()
            : $this->authoredPosts($author);

        $posts = $post_query
            ->with(['author'])
            ->where('status', 'published')
            ->orderBy('published_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(app(SettingsService::class)->blogPostsPerPage());

        return view('theme::blog.index')
            ->with(compact('posts',  'author'));
    }

    private function author(string $author_slug): ?User
    {
        if ($author_slug === 'anonymous') {
            return null;
        }

        /** @var User|null $author */
        $author = User::query()
            ->staff()
            ->whereHas('profile', fn($query) => $query->where('slug', $author_slug))
            ->with('profile')
            ->select(['id', 'first_name', 'last_name', 'role_id'])
            ->first();

        if (!is_null($author)) {
            return $author;
        }

        $author = User::query()
            ->with('profile')
            ->staff()
            ->get(['id', 'first_name', 'last_name', 'role_id'])
            ->first(fn(User $user) => $user->authorSlug() === $author_slug);

        abort_if(is_null($author), 404);

        return $author;
    }

    private function anonymousPosts(): Builder
    {
        return Post::query()
            ->whereNull('user_id');
    }

    private function authoredPosts(User $author)
    {
        return $author->posts();
    }
}
