<?php

namespace App\Http\Controllers\Admin\Recipe;

use App\Http\Controllers\Controller;
use App\Models\Ingredient;
use App\Models\Recipe;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RecipeIngredientController extends Controller
{
    public function index(int $recipeId)
    {
        return Ingredient::where('recipe_id', $recipeId)->get();
    }

    public function store(Recipe $recipes, Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => ['nullable'],
            'title' => ['required', 'string'],
            'amount' => ['nullable', 'string'],
        ]);

        $ingredient = new Ingredient($validated);
        if (! $ingredient->amount) {
            $ingredient->amount = '';
        }

        /** @var Ingredient $ingredient */
        $ingredient = $recipes->ingredients()->save($ingredient);

        return response()->json([
            'id' => $ingredient->id,
            'title' => $ingredient->title,
            'amount' => $ingredient->amount,
        ]);
    }

    public function destroy(int $recipeId, int $ingredientId): JsonResponse
    {
        Ingredient::where('recipe_id', $recipeId)
            ->where('id', $ingredientId)
            ->delete();

        return response()->json(['responseText' => 'Ingredient dissociated from recipe.']);
    }
}
