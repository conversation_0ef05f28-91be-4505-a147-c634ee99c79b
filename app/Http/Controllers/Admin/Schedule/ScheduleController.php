<?php

namespace App\Http\Controllers\Admin\Schedule;

use App\Actions\ActivateMultiDaySchedule;
use App\Actions\ActivateSchedule;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateScheduleRequest;
use App\Http\Requests\Admin\UpdateScheduleRequest;
use App\Models\Schedule;
use App\Repositories\ScheduleRepository;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\View\View;

class ScheduleController extends Controller
{
    public function index(Request $request, ScheduleRepository $repository): View
    {
        $schedules = $repository->all($request);

        $view = 'schedules.index';
        if ($request->filled('print')) {
            $view = 'schedules.print';
        }

        return view($view)->with(compact('schedules'));
    }

    public function store(CreateScheduleRequest $request)
    {
        $schedule = Schedule::create(array_merge($request->all(), ['active' => ! ($request->type_id == 2)]));

        return to_route('admin.schedules.edit', $schedule->id);
    }

    public function show(Schedule $schedule)
    {
        return to_route('admin.schedules.edit', $schedule);
    }

    public function edit(Schedule $schedule): View
    {
        $active_tab = request('tab', 'dates');

        if ($active_tab === 'dates') {
            $schedule->load(['upcomingDates']);
        }

        if ($active_tab === 'delivery-methods') {
            $schedule->load(['pickups' => fn ($query) => $query->select('id', 'schedule_id', 'fulfillment_type', 'title')->orderBy('title')]);
        }

        return view('schedules.edit')
            ->with(compact('schedule'));
    }

    public function update(UpdateScheduleRequest $request, Schedule $schedule)
    {
        $schedule->update(array_merge(
            $schedule->active
                ? $request->safe()->except(['subscription_reminder_settings_level', 'delivery_frequency', 'reorder_frequency'])
                : $request->safe()->except(['subscription_reminder_settings_level']),
            $request->only(['active', 'first_delivery_date', 'first_delivery_deadline'])
        ));

        return back();
    }

    public function destroy(Schedule $schedule)
    {
        if ($schedule->pickups()->exists()) {
            error('The schedule cannot be deleted because it is currently in use by the following delivery methods: <br/><br/>'.$schedule->pickups()->pluck('title')->join('<br/>'));

            return back();
        }

        if ($schedule->products()->exists()) {
            error('The schedule cannot be deleted because it is currently in use by the following products: <br/><br/>'.$schedule->products()->pluck('title')->join('<br/>'));

            return back();
        }

        $schedule->delete();

        return to_route('admin.schedules.index');
    }

    public function activate(Request $request, Schedule $schedule)
    {
        if ($request->boolean('multi_date')) {
            $validated = $request->validate([
                'days_of_week' => ['array', 'required'],
                'first_delivery_date' => ['sometimes', 'required', 'date'],
                'order_cutoff' => ['integer', 'required', 'min:0', 'max:7'],
                'ordering_in_advance' => ['sometimes', 'integer', Rule::in([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])],
            ]);

            try {
                ActivateMultiDaySchedule::run($validated, $schedule);
            } catch (Exception $exception) {
                error($exception->getMessage());

                return back();
            }

            return back();
        }

        $validated = $request->validate([
            'first_delivery_date' => ['sometimes', 'required', 'date'],
            'order_cutoff' => ['integer', 'required', 'min:0', 'max:7'],
            'ordering_in_advance' => ['sometimes', 'integer', Rule::in([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])],
        ]);

        try {
            ActivateSchedule::run($validated, $schedule);
        } catch (Exception $exception) {
            error($exception->getMessage());

            return back();
        }

        return back();
    }
}
