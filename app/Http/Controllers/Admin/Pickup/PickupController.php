<?php

namespace App\Http\Controllers\Admin\Pickup;

use App\Exceptions\NoGeocodeResultsException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreatePickupRequest;
use App\Http\Requests\Admin\UpdatePickupRequest;
use App\Models\Filter;
use App\Models\Pickup;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PickupController extends Controller
{
    public function index(Request $request): View
    {
        session(['pickups-filtered' => $request->all()]);

        $filters = Filter::where('type', 'pickup')->get();

        $request->mergeIfMissing(['orderBy' => 'title', 'sort' => 'asc']);

        return view('logistics.pickup.index')
            ->with([
                'savedFilters' => $filters,
                'appliedFilters' => $request->appliedFilters(),
                'appliedFilter' => $filters->where('id', $request->get('filter_id'))->first(),
                'pickups' => Pickup::filter($request->all())->pickup()->get(),
            ]);
    }

    public function store(CreatePickupRequest $request)
    {
        $pickup = Pickup::add($request);

        if ($request->expectsJson()) {
            return response()->json(['redirect' => "/admin/logistics/pickups/{$pickup->id}/edit"]);
        }

        return redirect(route('admin.pickups.edit', compact('pickup')));
    }

    public function show(Request $request, Pickup $pickup)
    {
        return to_route('admin.pickups.edit', $pickup);
    }

    public function edit(Pickup $pickup): View
    {
        $pickup->load(['schedule.dates', 'products']);

        return view('logistics.pickup.edit', compact('pickup'));
    }

    public function update(UpdatePickupRequest $request, Pickup $pickup)
    {
        $pickup = $pickup->fill($request->validated());

        if ($pickup->isDirty(['street', 'city', 'state', 'zip', 'country'])) {
            $pickup = $this->geocodePickupLocation($pickup);
        }

        $pickup->save();

        return back();
    }

    private function geocodePickupLocation(Pickup $pickup)
    {
        try {
            $geocoded_address = geocoder()->fromAddressParts($pickup->toAddressParts());

            $pickup->lat = $geocoded_address->lat;
            $pickup->lng = $geocoded_address->lng;

            if ($geocoded_address->isInaccurate()) {
                error('We were not able to get a very accurate geo coordinates for this location. You might need to adjust the latitude and longitude manually under settings.');
            }

        } catch (NoGeocodeResultsException $exception) {
            error('The address you entered could not be found. You may need to manually set the latitude and longitude.');
        }

        return $pickup;
    }
}
