<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\PasswordResetTokenException;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Traits\ResetPasswordTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PasswordController extends Controller
{
    use ResetPasswordTrait;

    public function getEmail()
    {
        if (auth()->check()) {
            return to_route('admin.login');
        }

        return view('passwords.forgot');
    }

    public function postEmail(Request $request)
    {
    }

    public function getReset($token)
    {
        if ($this->tokenIsInvalid($token)) {
            error('Your password reset has expired. Please try again.');

            return redirect()->to('/admin/password/forgot');
        }

        return view('passwords.reset')
            ->with(compact('token'));
    }

    public function postReset(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required', 'confirmed'],
        ]);

        try {
            $this->resetPassword(
                User::where('email', $request->input('email'))->firstOrFail(),
                $request->input('token'),
                $request->input('password')
            );

        } catch (PasswordResetTokenException $e) {
            error('Your password reset token is invalid. Please try resetting your password again.');

            return back();
        } catch (ModelNotFoundException $e) {
            error('No user could be found with the email you entered.');

            return back();
        }

        flash('Your password has been updated.');

        return redirect('/admin/login');
    }
}
