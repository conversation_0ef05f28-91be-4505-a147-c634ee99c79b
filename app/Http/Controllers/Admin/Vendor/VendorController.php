<?php

namespace App\Http\Controllers\Admin\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\View\View;

class VendorController extends Controller
{
    public function index(Request $request): View
    {
        $vendors = Vendor::orderBy(
            $request->input('orderBy', 'title'),
            $request->input('sort', 'asc')
        )->get();

        return view('vendors.index')
            ->with(compact('vendors'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required',
            'email' => ['nullable', 'email'],
            'website' => ['nullable', 'url'],
        ]);

        $vendor = Vendor::create($validated);

        return to_route('admin.vendors.edit', compact('vendor'));
    }

    public function show(Request $request, Vendor $vendor)
    {
        return to_route('admin.vendors.edit', $vendor);
    }

    public function edit(int $id): View
    {
        $vendor = Vendor::findOrFail($id);

        return view('vendors.edit')
            ->with(compact('vendor'));
    }

    public function update(Request $request, int $id)
    {
        if ($request->has('hide_products')) {
            return $this->hideProducts($request, $id);
        }

        $request->validate([
            'title' => ['required'],
            'email' => ['nullable', 'email'],
            'website' => ['nullable', 'url'],
        ], [
            'website.url' => 'Websites must start with: http:// or https://',
        ]);

        Vendor::findOrFail($id)->update($request->all());

        return back();
    }

    private function hideProducts(Request $request, int $vendor_id)
    {
        $visible = (bool) $request->input('hide_products');

        Product::where('vendor_id', $vendor_id)
            ->update(compact('visible'));

        Vendor::findOrFail($vendor_id)
            ->update(compact('visible'));

        flash('All products for this vendor have been hidden from the store.');

        return back();
    }

    public function destroy($id)
    {
        $vendor = Vendor::findOrFail($id);

        if ($vendor->products()->withoutTrashed()->exists()) {
            error('Please remove this vendor from products before deleting it.');

            return back();
        }

        $vendor->products()->onlyTrashed()->update(['vendor_id' => null]);

        $vendor->delete();

        return redirect(route('admin.vendors.index'));
    }
}
