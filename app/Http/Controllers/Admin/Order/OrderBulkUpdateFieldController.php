<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderBulkUpdateFieldController extends Controller
{
    public function update(Request $request, string $value)
    {
        $request->validate([
            'orders' => ['required', 'array'],
        ], [
            'orders.required' => 'Please select some orders and try again.',
        ]);

        $orderIds = $request->get('orders');

        $orders = Order::whereIn('id', $orderIds)->get();

        foreach ($orders as $order) {
            if (isset($order[$value])) {
                $order[$value] = ! (bool) $order[$value];
                $order->save();
            }
        }

        return back();
    }
}
