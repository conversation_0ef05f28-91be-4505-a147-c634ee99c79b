<?php

namespace App\Http\Controllers\Admin\Order;

use App\Exceptions\CouponInvalidException;
use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class OrderDiscountController extends Controller
{
    public function store(Order $order): JsonResponse
    {
        request()->validate([
            'code' => ['required', Rule::exists(Coupon::class)],
        ]);

        if ($order->is_paid) {
            return response()->json('A discount cannot be added to an order that is already paid.', 400);
        }

        $coupon = Coupon::where('code', request('code'))->first();

        try {
            $coupon = $order->applyCoupon($coupon);
        } catch (CouponInvalidException $e) {
            return response()->json($e->getMessage(), 422);
        }

        $order->updateTotals();

        return response()->json([
            'order' => $order,
            'coupon' => $coupon,
            'responseText' => 'Coupon added.',
        ]);
    }

    public function destroy(Order $order, int $discountId): JsonResponse
    {
        if ($order->is_paid) {
            return response()->json('A discount cannot be removed from an order that is already paid.', 400);
        }

        $order->removeCoupon($discountId);

        $order->updateTotals();

        return response()->json([
            'order' => $order,
            'responseText' => 'Discount removed.',
        ]);
    }
}
