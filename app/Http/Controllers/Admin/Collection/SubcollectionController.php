<?php

namespace App\Http\Controllers\Admin\Collection;

use App\Http\Controllers\Controller;
use App\Models\Subcollection;
use Cache;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SubcollectionController extends Controller
{
    public function index(): View
    {
        return view('subcollections.index')
            ->with(['collections' => Subcollection::with('tag')->get()]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'collection_id' => 'required',
            'tag_id' => 'required',
        ]);

        $collection = Subcollection::create($request->only([
            'title', 'collection_id', 'tag_id',
        ]));

        if ($request->ajax()) {
            return response()->json(['redirect' => '/admin/subcollections/'.$collection->id.'/edit']);
        }

        flash('Subcollection successfully created!');

        return redirect()->to('/admin/subcollections/'.$collection->id.'/edit');
    }

    public function edit($id): View
    {
        return view('subcollections.edit')->with(['collection' => Subcollection::findOrFail($id)]);
    }

    public function show(Request $request, Subcollection $subcollection)
    {
        return to_route('admin.subcollections.edit', $subcollection);
    }

    public function update(Request $request, int $id)
    {
        $request->validate([
            'title' => ['sometimes', 'required'],
            'collection_id' => ['required'],
            'tag_id' => ['required'],
            'settings.summary' => ['nullable', 'string'],
        ]);

        $fields = $request->all();

        // Check empty settings field
        if (! empty($fields['settings']) && empty($fields['settings']['display_name'])) {
            $fields['settings']['display_name'] = null;
        }

        Subcollection::findOrFail($id)->update($fields);

        Cache::tags(['store', 'collection', 'product'])->flush();
        if ($request->ajax()) {
            return response()->json('Success');
        }

        flash('Subcollection successfully updated!');

        return back();
    }

    public function destroy(int $id): RedirectResponse
    {
        Subcollection::findOrFail($id)->delete();

        return redirect()->to('/admin/subcollections');
    }
}
