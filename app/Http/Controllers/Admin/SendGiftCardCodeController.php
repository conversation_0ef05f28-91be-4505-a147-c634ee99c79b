<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Mail\GiftCardIssued;
use App\Models\GiftCertificate;
use App\Models\Product;
use Illuminate\Support\Facades\Mail;

class SendGiftCardCodeController extends Controller
{
    public function __invoke(Product $product, GiftCertificate $code)
    {
        Mail::to($code->orderItem->order->routeNotificationForMail())
            ->send(new GiftCardIssued($code->id));

        flash('The gift card was emailed to the customer!');

        return back();
    }
}
