<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\Pickup;
use App\Models\Product;
use Illuminate\Validation\Rule;

class LocationCartEligibilityController extends Controller
{
    public function store(Pickup $location): JsonResponse
    {
        request()->validate([
            'product_ids' => ['required', 'array'],
            'product_ids.*' => [Rule::exists(Product::class, 'id')]
        ]);

        $excluded_products = $location->products()
            ->whereIn('products.id', request('product_ids'))
            ->pluck('title');

        if ($excluded_products->isNotEmpty()) {
            return response()->json([
                'eligible' => false,
                'ineligible_reason' => 'PRODUCT_ELIGIBILITY',
                'products' => $excluded_products
            ]);
        }

        return response()->json(['eligible' => true]);
    }
}
