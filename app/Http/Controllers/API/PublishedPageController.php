<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Services\ThemeService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class PublishedPageController extends Controller
{
    public function store(ThemeService $themeService, int $pageId): JsonResponse
    {
        $page = Page::findOrFail($pageId);
        Storage::disk('pages')->put('page_' . $page->id . '.html', $themeService->buildWidgets($page));
        return response()->json();
    }

    public function rendered(ThemeService $themeService, int $pageId): View
    {
        try {
            /** @var Page $page */
            $page = Page::select(['id', 'title', 'page_title', 'description', 'body', 'layout', 'settings'])
                ->with('widgets')
                ->where('id', $pageId)
                ->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            abort(404);
        }

        return view('theme::pages.preview')
            ->with([
                'page'=> $page,
                'html'=> $page->renderHTML(auth()->check()),
                'content' => $themeService->buildWidgets($page)
            ]);
    }
}
