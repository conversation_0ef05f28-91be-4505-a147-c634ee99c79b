<?php

namespace App\Http\Controllers\API\Pages;

use App\Http\Controllers\Controller;
use App\Http\Middleware\Theme\IdentifyGuestShopper;
use App\Models\Page;
use Illuminate\Http\JsonResponse;

class PageContentController extends Controller
{
    public function __construct()
    {
        // this ensures feature product widget, which needs cart info, can render correctly
        $this->middleware([IdentifyGuestShopper::class, 'order']);
    }

    public function index(Page $page): JsonResponse
    {
        return response()->json([
            'timestamp' => $page->updated_at->timestamp,
            'content' => $page->renderHTML(auth()->check()),
        ]);
    }
}
