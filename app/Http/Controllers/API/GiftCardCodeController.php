<?php

namespace App\Http\Controllers\API;

use App\Actions\IssueGiftCard;
use App\Http\Controllers\Controller;
use App\Models\OrderItem;
use App\Support\Enums\ProductType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class GiftCardCodeController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_item_id' => ['required', 'exists:order_items,id']
        ]);

        /** @var OrderItem $item */
        $item = OrderItem::with('order')->find($validated['order_item_id']);

        abort_if($item->order->canceled ,409, 'Gift card codes cannot be generated for canceled orders.');

        abort_if($item->product->type_id !== ProductType::GIFT_CARD->value ,409, 'Gift card codes can only be generated for gift card products.');

        abort_if( ! is_null($item->giftCard) ,409, 'Gift card code has already been generated.');

        $card = app(IssueGiftCard::class)->handle($item, [
            'active' => true,
            'issuer_id' => auth()->id() ?? $item->order->customer_id,
        ]);

        return response()->json($card->only(['code', 'amount', 'balance']), 201);
    }
}
