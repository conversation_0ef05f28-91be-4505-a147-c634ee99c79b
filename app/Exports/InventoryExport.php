<?php

namespace App\Exports;

use Illuminate\Support\Collection;

class InventoryExport
{
    protected $chunkSize = 500;
    protected $fileName = 'inventory_report.csv';

    public function export(Collection $builder)
    {
        $rowHeaders = [
            'product',
            'sku',
            'on_order',
            'upcoming',
            'on_site_inventory',
            'off_site_inventory',
            'total_items',
            'item_cost',
            'total_cost'
        ];

        return response()->stream(function () use ($rowHeaders, $builder) {
            $export = fopen('php://output', 'w');
            fputcsv($export, $rowHeaders);
            foreach ($builder as $resource) {
                fputcsv($export, [
                    $resource->title,
                    $resource->sku,
                    $resource->count_on_order,
                    $resource->upcoming,
                    $resource->inventory,
                    $resource->other_inventory,
                    $resource->total_inventory,
                    money($resource->cost()),
                    $resource->subtotal
                ]);
            }
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }
}
