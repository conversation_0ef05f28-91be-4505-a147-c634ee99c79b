<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class SubscriptionInventoryExport implements FromCollection, WithHeadings, WithStrictNullComparison
{
    use Exportable;

    public function __construct(
        protected Collection $report,
    ) {}

    public function collection(): Collection
    {
        return $this->report;
    }

    public function headings(): array
    {
        return [
            'Product ID',
            'Product title',
            'Product SKU',
            'On order',
            'Current inventory'
        ];
    }
}
