<?php

namespace App\Exports;

use App\Events\Product\InventoryDecreasedToThresholdOrBelow;
use App\Events\Product\InventoryDecreasedToZeroOrBelow;
use App\Events\Product\InventoryIncreasedAboveZero;
use App\Models\Event;
use App\Models\Product;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class RestockLogExport implements FromCollection, WithMapping, WithHeadings
{
    public function __construct(
        public Collection $products
    ) {}

    public function collection(): Collection
    {
        return $this->products;
    }

    /**
     * @param Product $product
     */
    public function map($product): array
    {
        return $product->events
            ->map(fn(Event $event) => [
                $product->title,
                (string) $product->sku,
                (string) $product->barcode,
                $product->accounting_class,
                match($event->event_id) {
                    InventoryDecreasedToThresholdOrBelow::class => 'Inventory Decreased To Threshold Or Below',
                    InventoryDecreasedToZeroOrBelow::class => 'Inventory Decreased To Zero Or Below',
                    InventoryIncreasedAboveZero::class => 'Inventory Increased Above Zero',
                    default => 'Inventory Increased Above Threshold',
                },
                $event->created_at,
                $product->packingGroup->title,
                (string) $event->metadata->weight,
                (string) $event->metadata->inventory,
                (string) $event->metadata->other_inventory,
                (string) $event->metadata->oos_threshold_inventory,
                (string) $event->metadata->id,
                money($event->metadata->unit_price),
                money($event->metadata->sale_unit_price),
                (string) $event->metadata->unit_description,
                (string) $event->metadata->unit_of_issue,
                money($event->metadata->item_cost),
                (string) ($event->metadata->vendor['title'] ?? ''),
                (string) $event->metadata->custom_sort,
            ])
            ->toArray();
    }

    public function headings(): array
    {
        return [
            'Title',
            'SKU',
            'Barcode',
            'Accounting Class',
            'Event Name',
            'Event Datetime',
            'Packing Group',
            'Weight',
            'On-Site Inventory',
            'Off-site Inventory',
            'Subscription Reserve',
            'Product ID',
            'Unit Price',
            'Sale Unit Price',
            'Unit Description',
            'Unit Of Issue',
            'Item Cost',
            'Vendor',
            'Shelf Location'
        ];
    }
}
