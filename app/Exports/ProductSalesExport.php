<?php

namespace App\Exports;

use Illuminate\Database\Eloquent\Builder;

class ProductSalesExport
{
    protected $chunkSize = 500;
    protected $fileName = 'product_sales_report.csv';
    protected $select = [
        'products.unit_of_issue as unit_of_issue',
        'products.inventory as inventory',
        'products.unit_price as unit_price',
    ];

    public function export(Builder $builder)
    {
        $rowHeaders = [
            'orders',
            'id',
            'product_id',
            'title',
            'sku',
            'sort',
            'vendor',
            'on_order',
            'weight',
            'total',
            'on_site_inventory',
            'unit_description',
            'unit_price',
            'item_cost',
            'unit_type',
            'item_cost_total',
        ];

        return response()->stream(function () use ($rowHeaders, $builder) {
            $export = fopen('php://output', 'w');
            fputcsv($export, $rowHeaders);
            $builder
                ->selectRaw(implode(',', $this->select))
                ->chunk($this->chunkSize, function ($resourceChunk) use ($export) {
                    foreach ($resourceChunk as $resource) {
                        fputcsv($export, [
                            $resource['orders'],
                            $resource['id'],
                            $resource['product_id'],
                            $resource['title'],
                            $resource['sku'],
                            $resource['sort'],
                            $resource['vendor'],
                            $resource['on_order'],
                            $resource['weight'],
                            money($resource['total']),
                            $resource['inventory'],
                            $resource['unit_description'],
                            money($resource['unit_price']),
                            money($resource['item_cost']),
                            $resource['unit_of_issue'] ?? null,
                            money($this->getTotalItemCost($resource))
                        ]);
                    }
                });
            fclose($export);
        }, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $this->fileName . '"',
        ]);
    }

    private function getTotalItemCost($item)
    {
        return $item['unit_of_issue'] == 'weight'
            ? $item['weight'] * $item['item_cost']
            : $item['on_order'] * $item['item_cost'];
    }
}
