<?php

namespace App\Repositories;

use App\Exceptions\NoGeocodeResultsException;
use App\Models\Location;
use App\Models\Pickup;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LocationRepository
{
    /**
     * @return Collection<int, Location>
     */
    public function all(Request $request, string $type = 'market'): Collection
    {
        return Location::query()
            ->where('type', $type)
            ->orderBy(
                $request->get('orderBy', 'title'),
                $request->get('sort', 'asc')
            )
            ->get();
    }

    /**
     * @return Collection<int, Location>|null
     */
    public function getLocationByAddress(float $lat, float $lng, string $type): ?Collection
    {
        $query = Location::query()
            ->select([
                'title',
                'slug',
                'lat',
                'lng',
                'street',
                'city',
                'state',
                'zip',
                DB::raw('id, ( 3959 * acos( cos( radians(' . $lat . ') ) * cos( radians( lat ) ) * cos( radians( lng ) - radians(' . $lng . ') ) + sin( radians(' . $lat . ') ) * sin( radians( lat ) ) ) ) AS distance')
            ])
            ->where('visible', true)
            ->when( ! empty($type), fn($query) => $query->where('type', $type))
            ->orderBy('distance');

        $response = null;

        try {
            $response = $query->get();
        } catch (Exception $e) {
            error('Sorry, the address you entered could not be found.');
        }

        return $response;
    }

    /**
     * @return Collection<int, Location>|null
     * @throws NoGeocodeResultsException
     */
    public function getLocationByStreetAddress(string $address, string $type): ?Collection
    {
        $geocoded_address = geocoder()->fromAddress($address);

        return $this->getLocationByAddress($geocoded_address->lat, $geocoded_address->lng, $type);
    }

    /**
     * @return Collection<int, Location>|null
     * @throws NoGeocodeResultsException
     */
    public function getLocationByZipCode(string $zip, string $type): ?Collection
    {
        $geocoded_address = geocoder()->fromZipcode($zip);

        return $this->getLocationByAddress($geocoded_address->lat, $geocoded_address->lng, $type);
    }
}
