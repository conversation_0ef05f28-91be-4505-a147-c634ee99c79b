<?php

namespace App\Repositories\Widgets;

use App\Models\Recipe;
use App\Models\Widget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class RecipeWidgetRepository
{
    public int $count;

    public ?string $source;

    public string $orderBy = 'published_at';

    public string $sort = 'desc';

    public array $select = ['title', 'slug', 'published_at', 'created_at', 'cover_photo', 'description'];
    protected int $max_count = 15;
    protected string $cacheKey;

    public function __construct(
        public Widget $widget
    ) {
        $this->count = $widget->settings->count ?? 4;
        $this->source = $widget->settings->source ?? null;
        $this->orderBy = $widget->setting('order_by', 'published_at');
        $this->sort = $widget->setting('sort', 'desc');
        $this->cacheKey = 'recipe_widget:' . $widget->id;
    }

    /**
     * @return Collection<int, Recipe>
     */
    public function get(): Collection
    {
        return Cache::tags(['setting', 'recipe', 'widget', 'recipe_widget'])
            ->remember($this->cacheKey, now()->addMinutes(120), function () {
                return Recipe::query()
                    ->select($this->select)
                    ->when($this->source !== 'most_recent', function ($q) {
                        return $q->whereHas('tags', fn($query) => $query->where('tags.id', $this->source));
                    })
                    ->orderBy($this->orderBy, $this->sort)
                    ->take($this->getCount())
                    ->get();
            });
    }

    private function getCount(): int
    {
        return min($this->count, $this->max_count);
    }
}
