<?php

namespace App\Repositories\Widgets;

use App\Models\Protocol;
use App\Models\Widget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class ProtocolWidgetRepository
{
    public int $count;

    public ?string $source;

    public string $orderBy = 'published_at';

    public string $sort = 'desc';

    public array $select = ['title', 'slug', 'description'];

    protected int $max_count = 100;

    protected string $cacheKey;

    public function __construct(
        public Widget $widget
    ) {
        $this->count = $widget->settings->count ?? $this->max_count;
        $this->source = $widget->settings->source ?? null;
        $this->orderBy = $widget->setting('order_by', 'title');
        $this->sort = $widget->setting('sort', 'desc');
        $this->cacheKey = 'protocols_widget:' . $widget->id;
    }

    /**
     * @return Collection<int, Protocol>
     */
    public function get(): Collection
    {
        return Cache::tags(['setting', 'protocol', 'widget', 'protocol_widget'])
            ->remember($this->cacheKey, now()->addMinutes(120), function () {
                return Protocol::query()
                    ->select(['title', 'slug', 'description'])
                    ->orderBy($this->orderBy, $this->sort)
                    ->take($this->getCount())
                    ->get();
            });
    }

    private function getCount(): int
    {
        return min($this->count, $this->max_count);
    }
}
