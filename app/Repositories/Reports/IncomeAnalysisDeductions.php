<?php

namespace App\Repositories\Reports;

use App\Models\Filters\IncomeAnalysisFilter;
use App\Models\Order;
use Illuminate\Support\Collection;
use stdClass;

class IncomeAnalysisDeductions
{
    protected array $select = [
        'SUM(orders.order_discount) AS discount_total',
        'SUM(orders.credit_applied) AS credit_total',
        'SUM(orders.coupon_subtotal) AS coupon_total',
        'SUM(orders.subscription_savings) AS subscription_savings_total',
    ];

    /**
     * @return Collection<int, (object{accounting_class: 'Discounts', total: int}&stdClass)|(object{accounting_class: 'Credits', total: int}&stdClass)|(object{accounting_class: 'Coupons', total: int}&stdClass)|(object{accounting_class: 'Subscription savings', total: int}&stdClass)>
     */
    public function handle(array $filters): Collection
    {
        $result = Order::query()
            ->selectRaw(implode(',', $this->select))
            ->filter($filters, IncomeAnalysisFilter::class)
            ->toBase()
            ->first();

        return collect([
            (object) ['accounting_class' => 'Discounts', 'total' => (int) $result->discount_total],
            (object) ['accounting_class' => 'Credits', 'total' => (int) $result->credit_total],
            (object) ['accounting_class' => 'Coupons', 'total' => (int) $result->coupon_total],
            (object) ['accounting_class' => 'Subscription savings', 'total' => (int) $result->subscription_savings_total],
        ]);
    }
}
