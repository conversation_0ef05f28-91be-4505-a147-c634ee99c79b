<?php

namespace App\Repositories\Reports\MarketingReview;

use App\Models\User;
use App\Repositories\Reports\MarketingReview\Concerns\OrderScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class OrdersAndAOV
{
    use OrderScope;

    public function handle(Carbon $delivery_start, Carbon $delivery_end): array
    {
        $result = DB::table(function ($sub_query) use ($delivery_start, $delivery_end) {
            return $this->applyScope(
                $sub_query
                    ->selectRaw('COUNT(*) AS order_count, SUM(total) AS order_total')
                    ->from('orders')
                    ->whereBetween('pickup_date', [$delivery_start, $delivery_end])
            );
        }, 'sub')
            ->selectRaw('sum(order_count) as order_count, cast((sum(order_total) / sum(order_count)) AS DECIMAL (8, 0)) AS average_order_value')
            ->first();

        $first_order_result = DB::table(function ($sub_query) use ($delivery_start, $delivery_end) {
            return $this->applyScope(
                $sub_query
                    ->selectRaw('COUNT(*) AS order_count, SUM(total) AS order_total')
                    ->from('orders')
                    ->whereBetween('pickup_date', [$delivery_start, $delivery_end])
                    ->where('first_time_order', true)
            );
        }, 'sub')
            ->selectRaw('sum(order_count) as order_count, cast((sum(order_total) / sum(order_count)) AS DECIMAL (8, 0)) AS average_order_value')
            ->first();

        return [
            'delivery_start_date' => $delivery_start,
            'delivery_end_date' => $delivery_end,
            'order_count' => $result->order_count,
            'average_order_value' => $result->average_order_value,
            'first_time_order_average_order_value' => $first_order_result->average_order_value
        ];
    }
}