<?php

namespace App\Repositories\Reports;

class IncomeAnalysisReport
{
    public function handle(array $filters, bool $by_channel = false): array
    {
        $line_item_revenue = app(IncomeAnalysisRevenueByAccountingClass::class)->handle($filters, $by_channel);
        $fee_revenue = collect();
        $deductions = collect();

        if ($line_item_revenue->isNotEmpty()) {
            $fee_revenue = app(IncomeAnalysisRevenueFromFees::class)->handle($filters);
            $deductions = app(IncomeAnalysisDeductions::class)->handle($filters);
        }

        return [
            $line_item_revenue,
            $fee_revenue,
            $deductions
        ];
    }
}
