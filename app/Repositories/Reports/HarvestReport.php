<?php

namespace App\Repositories\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Traits\DateRangeTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HarvestReport
{
    use DateRangeTrait;

    protected array $select = [
        '0 as orders',
        'order_items.id',
        'order_items.title as item_title',
        'order_items.product_id as product_id',
        'sum(order_items.qty * bundle_product.qty) as count_on_order',
        'sum(order_items.subtotal) as total',
        'sum(order_items.weight) as weight',
        'bundle_product.qty as bundle_qty',
        'bundle_product.product_id as bundle_id',
        'products.title as title',
        'products.sku',
        'products.custom_sort as sort',
        'products.item_cost as item_cost',
        'products.inventory as inventory',
        'products.stock_out_inventory as stock_out_inventory',
    ];

    public function __construct(
        protected Request $request
    ) {}

    /**
     * @return Builder<OrderItem>
     */
    public function query(): Builder
    {
        $hasProductFilters = array_filter($this->request->only([
            'products', 'sku', 'inventory_type', 'vendor_id', 'collection_id', 'accounting_class', 'show_deleted'
        ]));

        $orderIds = $this->getOrdersQuery(Order::query())->limit($limit = 3000)->pluck('id');

        return OrderItem::query()
            ->join('products', 'products.id', '=', 'order_items.product_id')
            ->selectRaw(implode(',', [
                '0 as orders',
                'order_items.id as id',
                'order_items.title as item_title',
                'order_items.product_id as product_id',
                'sum(order_items.qty) as count_on_order',
                'sum(order_items.weight) as weight',
                'products.title as title',
                'products.sku',
                'products.custom_sort as sort',
                'products.item_cost as item_cost',
                'products.inventory as inventory',
                'products.stock_out_inventory as stock_out_inventory',
            ]))
            ->with(['bundleProduct.product'])
            ->when(count($orderIds) === $limit, function ($q) {
                $q->whereHas('order', function ($o) {
                    /** @var Builder<Model> $o */
                    return $this->getOrdersQuery($o);
                });
            })
            ->when(count($orderIds) < $limit, function ($q) use ($orderIds) {
                $q->whereIn('order_id', $orderIds);
            })
            ->when(count($hasProductFilters), function (Builder $p) {
                $query = $this->getProductsQuery()->getQuery();
                $p->mergeWheres($query->wheres, $query->getRawBindings()['where']);
            })
            ->groupBy('order_items.product_id');
    }

    /**
     * @return Builder<Order>
     */
    public function getOrdersQuery(Builder $builder): Builder
    {
        return $builder
            ->where('orders.confirmed', true)
            ->where('orders.canceled', false)
            ->whereIn('orders.status_id', $this->request->get('order_status', [1, 2, 3]))
            ->when($this->request->filled('order_type_id'), function ($q) {
                return $q->whereIn('type_id', (array) $this->request->get('order_type_id'));
            })
            ->when($this->request->filled('packed_by'), function ($q) {
                return $q->where('staff_id', (int) $this->request->get('packed_by'));
            })
            ->when($this->request->filled('pickup_id') || $this->request->filled('schedule_id'), function ($q) {
                if ($this->request->filled('pickup_id')) {
                    return $q->whereIn('orders.pickup_id', $this->request->get('pickup_id'));
                }

                /** @var array<int, int> $schedule_ids */
                $schedule_ids = $this->request->get('schedule_id', []);

                return $q->whereIn(
                    'orders.pickup_id',
                    Pickup::query()
                        ->whereIn('schedule_id', collect($schedule_ids))
                        ->pluck('id')
                );
            })
            ->when($this->request->filled('pickup_date'), function ($q) {
                $pickup_date_range = $this->getDateRange($this->request->get('pickup_date'));

                $start = $pickup_date_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.pickup_date', '>=', $start);
                }

                $end = $pickup_date_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.pickup_date', '<=', $end);
                }

                return $q;
            })
            ->when($this->request->filled('pack_deadline_at'), function ($q) {
                $pack_deadline_at_range = $this->getDateRange($this->request->get('pack_deadline_at'));

                $start = $pack_deadline_at_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.pack_deadline_at', '>=', Carbon::parse($start)->startOfDay());
                }

                $end = $pack_deadline_at_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.pack_deadline_at', '<=', Carbon::parse($end)->endOfDay());
                }

                return $q;
            })
            ->when($this->request->filled('confirmed_date'), function ($q) {
                $confirmed_date_range = $this->getDateRange($this->request->get('confirmed_date'));

                $start = $confirmed_date_range->get('start');
                if (!is_null($start)) {
                    $q->where('orders.confirmed_date', '>=', $start);
                }

                $end = $confirmed_date_range->get('end');
                if (!is_null($end)) {
                    $q->where('orders.confirmed_date', '<=', $end);
                }

                return $q;
            })
            ->when($this->request->filled('order_tags'), function ($q) {
                return $q->whereIn('orders.id', function (\Illuminate\Database\Query\Builder $query) {
                    $query->select('order_id')
                        ->from('order_tag')
                        ->whereIn('tag_id', $this->request->get('order_tags', []));
                });
            })->when($this->request->filled('customer'), function ($q) {
                return $this->getCustomerQuery($this->request->get('customer'), $q);
            });
    }

    /**
     * @return Builder<Order>
     */
    private function getCustomerQuery(string $term, Builder $query): Builder
    {
        if (filter_var($term, FILTER_VALIDATE_INT)) {
            return $query->where('orders.customer_id', $term);
        }

        if (filter_var($term, FILTER_VALIDATE_EMAIL)) {
            return $query->where('orders.customer_email', $term);
        }

        return $query->where(function ($query) use ($term) {
            $query->where(DB::raw('CONCAT(customer_first_name, " ", customer_last_name)'), 'LIKE', '%' . $term . '%')
                ->orWhere('customer_phone', $term)
                ->orWhere('accounting_id', $term);
        });
    }

    /**
     * @return Builder<Product>
     */
    private function getProductsQuery(): Builder
    {
        return Product::query()
            ->filter($this->request->only([
                'products', 'sku', 'inventory_type', 'vendor_id', 'collection_id', 'accounting_class', 'show_deleted'
            ]));
    }
}
