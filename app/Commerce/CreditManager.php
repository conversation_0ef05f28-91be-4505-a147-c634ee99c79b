<?php

namespace App\Commerce;

use App\Models\Order;

class CreditManager
{
    protected $creditableBalance;

    public function ApplyCreditToOrder(Order $order)
    {
        if (!$order->customer) return $order;

        $creditable_amount = $order->getCreditableBalance();

        if ($creditable_amount > 0) {
            $available_customer_credit = $order->customer->getCredit();

            if ($available_customer_credit > 0) {
                $this->applyCredit($order, min($creditable_amount, $available_customer_credit));
            }
        } elseif ($creditable_amount < 0) {
            $this->removeCredit($order, abs($creditable_amount));
        }

        return $order;
    }

    private function applyCredit(Order $order, $amount)
    {
        if ($order->customer->removeCredit($amount, 'Order #' . $order->id . ' adjusted.')) {
            $order->applyCredit($amount, money($amount) . ' in credit applied.');
        }
    }

    private function removeCredit(Order $order, $amount)
    {
        if ($order->customer->applyCredit($amount, 'Order #' . $order->id . ' adjusted.')) {
            $order->removeCredit($amount, money($amount) . ' in credit removed.');
        }
    }
}
