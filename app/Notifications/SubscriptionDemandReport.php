<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SubscriptionDemandReport extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public string $filepath
    ) {}

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $filename = Str::of($this->filepath)->afterLast('/');

        return (new MailMessage)
            ->subject('Subscription Demand Report')
            ->line('The subscription demand report is attached.')
            ->attachData(
                Storage::disk('s3')->get($this->filepath),
                $filename,
                ['mime' => 'text/csv']
            );
    }
}
