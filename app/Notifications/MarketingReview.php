<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class MarketingReview extends TenantAwareNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public array $marketing_review
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage)
            ->view('emails.notifications.marketing-review', [
                'marketing_review' => $this->marketing_review,
            ])
            ->subject('GrazeCart Marketing Review')
            ->from(config('mail.from.address'), config('mail.from.name'));
    }

    public function tags(): array
    {
        return ['notification'];
    }
}
