<?php

namespace App\Actions\Order;

use App\Models\Order;
use App\Models\OrderItem;

class Remove
{
    public function handle(Order $order): void
    {
        if ($order->isConfirmed()) {
            $order->load(['items.product', 'fees', 'customer', 'paymentMethod', 'pickup', 'blueprint']);

            $order->releaseInventory();
            $order->refundCustomerCredit();
            $order->decrementCustomerOrderCount();

            foreach ($order->items as $item) {
                $item->delete();
            }

            // if this is the only order on the blueprint
            if ($order->blueprint?->orders()->count() === 1) {
                $order->blueprint->delete();
            }
        }

        $order->delete();
    }
}