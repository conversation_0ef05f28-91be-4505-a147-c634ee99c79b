<?php

namespace App\Actions\Order;

use App\Actions\BaseAction;
use App\Models\Order;
use App\Models\Product;
use App\Services\SubscriptionSettingsService;

class AddPromoItem extends BaseAction
{
    /**
     * @throws \Exception
     */
    public function handle(Order $order, ?int $promo_product_id = null): Order
    {
        app(RemovePromoItems::class)->handle($order);

        $service = app(SubscriptionSettingsService::class);

        $promo_product_id = $promo_product_id ?: $service->defaultProductIncentiveId();

        if (is_null($promo_product_id)) {
            return $order;
        }

        $product = Product::query()
            ->with(['price' => function ($q) use ($order) {
                return $q->where('group_id', $order->getPricingGroup());
            }])
            ->find($promo_product_id);

        if (is_null($product)) {
            return $order;
        }

        $order->addPromoItem($product);

        $order->updateTotals();

        return $order;
    }
}
