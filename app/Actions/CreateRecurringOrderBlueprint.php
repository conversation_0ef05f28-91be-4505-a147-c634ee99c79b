<?php

namespace App\Actions;

use App\Events\Subscription\CustomerSubscribed;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;

class CreateRecurringOrderBlueprint
{
    public function execute(Order $order, int $reorder_frequency, ?int $promo_product_id = null)
    {
        $order->is_recurring = true;
        $order->save();
        $order->refresh();

        $order_window = $order->nextSubscriptionOrderWindow($reorder_frequency);

        $subscription = RecurringOrder::updateOrCreate([
            'customer_id' => $order->customer_id
        ], [
            'fulfillment_id' => $order->pickup_id,
            'schedule_id' => $order->pickup->schedule_id,
            'reorder_frequency' => $reorder_frequency,
            'ready_at' => $order_window->originalDate()->pickup_date,
            'generate_at' => $order_window->generatesAtDatetime(),
            'updated_by' => auth()->user()->id ?? null
        ]);

        if ($subscription->wasRecentlyCreated) {
            event(new CustomerSubscribed($subscription));
        }

        $order->blueprint_id = $subscription->id;
        $order->save();

        if ( ! is_null($promo_product_id)) {
            $this->addPromoItem($subscription, $promo_product_id);
        }

        $order->items
            ->filter(fn(OrderItem $item) => $item->product_id !== $promo_product_id)
            ->each(function(OrderItem $item) use ($order, $subscription) {
                RecurringOrderItem::create([
                    'order_id' => $subscription->id,
                    'customer_id' => $order->customer->id ?? auth()->user()->id,
                    'product_id' => $item->product_id,
                    'qty' => $item->qty,
                    'type' => 'recurring',
                    'unit_price_override' => null
                ]);
            });

        return $subscription->refresh()->load(['items']);
    }

    private function addPromoItem(RecurringOrder $recurringOrder, int $promo_product_id): void
    {
        $product_is_valid = Product::where('id', $promo_product_id)->exists();

        if ( ! $product_is_valid) return;

        RecurringOrderItem::create([
            'order_id' => $recurringOrder->id,
            'customer_id' => $recurringOrder->customer_id ?? auth()->user()->id,
            'product_id' => $promo_product_id,
            'qty' => 1,
            'type' => 'promo'
        ]);
    }
}
