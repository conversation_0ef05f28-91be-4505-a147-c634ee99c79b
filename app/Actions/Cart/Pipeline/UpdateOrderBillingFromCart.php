<?php

namespace App\Actions\Cart\Pipeline;

use App\Cart\Cart;
use App\Models\Card;
use App\Models\Order;
use App\Models\Payment;
use Closure;

class UpdateOrderBillingFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /** 
         * @var Order $order
         * @var Cart $cart
         */
        list($order, $cart) = $passable;

        $billing = $cart->params['billing'];

        $payment = Payment::where('key', $billing['method'])->select(['id', 'key'])->first();

        $order->payment_id = $payment->id;

        if ($payment->isCard()) {
            $card = Card::where('source_id', $billing['source_id'])->first();
            $order->payment_source_id = $card?->id;

            if ($billing['save_for_later']) {
                $order->customer->setting(['default_payment_method' => $payment->id]);
                $order->customer->checkout_card_id = $card?->id;
                Card::where('user_id', $order->customer_id)->update(['default' => false]);
                $card?->update(['default' => true]);
            }
        }

        return $next([$order, $cart]);
    }
}
