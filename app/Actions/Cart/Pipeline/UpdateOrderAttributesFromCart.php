<?php

namespace App\Actions\Cart\Pipeline;

use App\Cart\Cart;
use App\Models\Date;
use App\Models\Order;
use Closure;

class UpdateOrderAttributesFromCart
{
    public function handle(array $passable, Closure $next): Order
    {
        /**
         * @var Order $order
         * @var Cart $cart
         */
        list($order, $cart) = $passable;

        $order->customer_notes = $cart->params['notes'] ?? '';
        $order->recipient_email = $cart->params['recipient_email'] ?? null;
        $order->recipient_notes = $cart->params['recipient_notes'] ?? '';

        if ($cart->params['date_id'] ?? null) {

            /** @var Date|null $selected_date */
            $selected_date = Date::find($cart->params['date_id']);

            if ($selected_date) {
                $order_window = $selected_date->toOrderWindows();

                $order->date_id = $order_window->dateId();
                $order->pickup_date = $order_window->deliveryDatetime();
                $order->original_pickup_date = $order_window->deliveryDatetime();
                $order->deadline_date = $order_window->deadlineDatetime();
                $order->schedule_id = $order_window->scheduleId();
            }
        }

        return $next([$order, $cart]);
    }
}
