<?php

namespace App\Billing\Stripe;

use Stripe\Account;
use Stripe\Card;
use Stripe\Charge;
use Stripe\Collection;
use Stripe\Coupon;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\OAuth\OAuthErrorException;
use Stripe\Invoice;
use Stripe\OAuth;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Plan;
use Stripe\Refund;
use Stripe\SetupIntent;
use Stripe\StripeObject;
use Stripe\Subscription;
use Stripe\SubscriptionItem;
use Stripe\Terminal\ConnectionToken;
use Stripe\Terminal\Location;
use Stripe\Terminal\Reader;
use Stripe\Token;

class Stripe
{
    public function __construct()
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.secret'));
        \Stripe\Stripe::setApiVersion('2020-08-27');
    }

    public function overrideApiKey(string $api_key)
    {
        \Stripe\Stripe::setApiKey($api_key);

        return $this;
    }

    public function retrieveAccount(string $id): Account
    {
        return Account::retrieve($id);
    }

    /**
     * @throws ApiErrorException
     */
    public function createCharge(?array $params = null, ?array $options = null): Charge
    {
        return Charge::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createToken(?array $params = null, ?array $options = null): Token
    {
        return Token::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createPaymentMethod(?array $params = null, ?array $options = null): PaymentMethod
    {
        return PaymentMethod::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createRefund(?array $params = null, ?array $options = null): Refund
    {
        return Refund::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveCustomer(string $id): Customer
    {
        return Customer::retrieve($id);
    }

    /**
     * @throws ApiErrorException
     */
    public function createCustomer(?array $params = null, ?array $options = null): Customer
    {
        return Customer::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function updateCustomer(string $id, ?array $params = null): Customer
    {
        return Customer::update($id, $params);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveCustomerPaymentMethods(string $customer_id)
    {
        return PaymentMethod::all([
            'type' => 'card',
            'customer' => $customer_id,
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrievePaymentMethod(string $payment_method_id)
    {
        return PaymentMethod::retrieve($payment_method_id);
    }

    /**
     * @throws ApiErrorException
     */
    public function updatePaymentMethod(PaymentMethod $paymentMethod, array $params)
    {
        return PaymentMethod::update($paymentMethod->id, $params);
    }

    /**
     * @throws ApiErrorException
     */
    public function createCustomerSource(string $id, ?array $params = null, ?array $opts = null): Card
    {
        return Customer::createSource($id, $params, $opts);
    }

    /**
     * @throws ApiErrorException
     */
    public function updateCustomerCard(Card $card, array $params): Card
    {
        $card->name = $params['cardholder_name'] ?? null;
        $card->exp_month = $params['exp_month'] ?? null;
        $card->exp_year = $params['exp_year'] ?? null;

        $card->save();

        return $card;
    }

    /**
     * @throws ApiErrorException
     */
    public function deleteCustomerSource(string $customer_id, string $source_id)
    {
        return Customer::deleteSource($customer_id, $source_id);
    }

    /**
     * @throws ApiErrorException
     */
    public function detachPaymentMethod(string $payment_method_id)
    {
        return PaymentMethod::retrieve($payment_method_id)->detach();
    }

    /**
     * @throws ApiErrorException
     */
    public function saveDefaultCustomerSource(string $customer_id, string $source_id): Customer
    {
        return Customer::update($customer_id, [
            'default_source' => $source_id,
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function saveDefaultCustomerPaymentMethod(string $customer_id, string $payment_method_id): Customer
    {
        return Customer::update($customer_id, [
            'invoice_settings' => [
                'default_payment_method' => $payment_method_id
            ]
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveDefaultSource(string $customer_id): ?Card
    {
        $stripe_customer = Customer::retrieve($customer_id);

        if ( ! $stripe_customer->default_source) {
            return null;
        }

        return $this->retrieveCustomerSource($customer_id, $stripe_customer->default_source);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveCustomerSource(string $customer_id, ?string $source_id): Card
    {
        return Customer::retrieveSource($customer_id, $source_id);
    }

    /**
     * @throws ApiErrorException
     */
    public function createSubscription(array $params): Subscription
    {
        return Subscription::create($params);
    }

    /**
     * @throws ApiErrorException
     */
    public function updateSubscription(string $subscription_id, array $params): Subscription
    {
        return Subscription::update($subscription_id, $params);
    }

    /**
     * @throws ApiErrorException
     */
    public function cancelSubscription(string $subscription_id, array $params): Subscription
    {
        return $this->retrieveSubscription($subscription_id)->cancel($params);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveSubscription(string $subscription_id): Subscription
    {
        return Subscription::retrieve($subscription_id);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveSubscriptionItem(string $subscription_item_id): SubscriptionItem
    {
        return SubscriptionItem::retrieve($subscription_item_id);
    }

    /**
     * @throws ApiErrorException
     */
    public function updateSubscriptionItem(string $existing_subscription_item_id, string $new_plan_id): SubscriptionItem
    {
        return SubscriptionItem::update($existing_subscription_item_id, [
            'price' => $new_plan_id
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveCoupon(string $couponId): Coupon
    {
        return Coupon::retrieve($couponId);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveSubscriptionInvoices(string $subscriptionId, array $params = []): Collection
    {
        return Invoice::all(array_merge(['subscription' => $subscriptionId], $params));
    }

    /**
     * @throws ApiErrorException
     */
    public function retrieveInvoice(string $invoiceId): Invoice
    {
        return Invoice::retrieve($invoiceId);
    }

    /**
     * @throws ApiErrorException
     */
    public function retrievePlan(string $planId): Plan
    {
        return Plan::retrieve($planId);
    }

    /**
     * @throws ApiErrorException
     */
    public function createTerminalLocation(array $params, array $options = []): Location
    {
        return Location::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function registerTerminalReader(array $params, array $options = []): Reader
    {
        return Reader::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function fetchTerminalReaders(array $params, array $options = []): Collection
    {
        return Reader::all($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createPaymentIntent(array $params, array $options = []): PaymentIntent
    {
        return PaymentIntent::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function capturePaymentIntent(string $payment_intent_id, array $options = []): PaymentIntent
    {
        return PaymentIntent::retrieve($payment_intent_id, $options)->capture([], $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createConnectionToken(array $params, array $options = []): ConnectionToken
    {
        return ConnectionToken::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function createSetupIntent(array $params, array $options = []): SetupIntent
    {
        return SetupIntent::create($params, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function fetchSetupIntent(string $id, array $options = []): SetupIntent
    {
        return SetupIntent::retrieve($id, $options);
    }

    /**
     * @throws ApiErrorException
     */
    public function fetchPaymentMethod(string $id, array $options = []): PaymentMethod
    {
        return PaymentMethod::retrieve($id, $options);
    }

    /**
     * @throws OAuthErrorException
     */
    public function disconnect(array $credentials): StripeObject
    {
        return OAuth::deauthorize($credentials);
    }
}
