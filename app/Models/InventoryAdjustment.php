<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\InventoryAdjustment
 *
 * @property int $id
 * @property int $product_id
 * @property int $old_amount
 * @property int $new_amount
 * @property string|null $description
 * @property int $user_id
 * @property int $type_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment forProduct($productId)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment query()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereNewAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereOldAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryAdjustment whereUserId($value)
 * @mixin \Eloquent
 */
class InventoryAdjustment extends Model
{
    protected $table = 'inventory_adjustments';

    protected $guarded = ['id'];

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
