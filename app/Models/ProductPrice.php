<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\ProductPrice
 *
 * @property int $id
 * @property int $group_id
 * @property int $product_id
 * @property int|null $unit_price
 * @property int|null $sale_unit_price
 * @property string $unit_of_issue
 * @property string $unit_description
 * @property string|null $weight
 * @property-read string $sale_unit_price_formatted
 * @property-read string $unit_price_formatted
 * @property-read \App\Models\ProductPriceGroup|null $group
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereSaleUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereUnitDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereUnitOfIssue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPrice whereWeight($value)
 * @method static \Database\Factories\ProductPriceFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class ProductPrice extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $guarded = [];
    protected $table = 'product_prices';
    protected $appends = [
        'unit_price_formatted', 'sale_unit_price_formatted'
    ];

    public function savings(): ?int
    {
        return $this->unit_price - $this->sale_unit_price;
    }

    public function group(): HasOne
    {
        return $this->hasOne(ProductPriceGroup::class, 'id', 'group_id');
    }

    public function getUnitPriceFormattedAttribute(): string
    {
        return number_format($this->unit_price / 100, 2);
    }

    public function getSaleUnitPriceFormattedAttribute(): string
    {
        return number_format($this->sale_unit_price / 100, 2);
    }
}
