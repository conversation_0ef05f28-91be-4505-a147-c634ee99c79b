<?php 

namespace App\Models\Filters;

use EloquentFilter\ModelFilter;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;

class LocationFilter extends ModelFilter
{
    /**
    * Related Models that have ModelFilters as well as the method on the ModelFilter
    * As [relationMethod => [input_key1, input_key2]].
    *
    * @var array
    */
    public $relations = [];

    public function sort($direction): void
    {
        $this->when( ! empty($this->input('orderBy')), function ($query) use ($direction) {
            return $query->orderBy(
                column: $this->input('orderBy'),
                direction: in_array($direction, ['asc', 'desc']) ? $direction : 'asc'
            );
        })
            ->orderBy('created_at', 'desc');
    }

    public function locations(string $query): void
    {
        $this->where(function ($q) use ($query) {
            $q->where('title', 'LIKE', '%' . $query . '%');
        });
    }

    public function pickup($id): void
    {
        $this->whereIn('id', $id);
    }

    public function locationStatus($status): void
    {
        $this->where('status_id', $status);
    }

    public function scheduleId($id): void
    {
        $this->schedule($id);
    }

    public function schedule($id): void
    {
        $this->whereIn('schedule_id', Arr::wrap($id));
    }

    public function state(string $state): void
    {
        $this->where('state', $state);
    }

    public function showDeleted(): void
    {
        /** @phpstan-ignore-next-line  */
        $this->withTrashed();
    }

    public function locationVisibility($visibility): void
    {
        $this->where('visible', $visibility);
    }

    public function postalCode(string $postalCode): void
    {
        $this->whereHas('zips', function ($query) use ($postalCode) {
            return $query->where('zip', trim($postalCode));
        });
    }

    public function shippingState(string $state): void
    {
        $this->whereHas('states', function ($query) use ($state) {
            return $query->where('state', trim($state));
        });
    }

    public function orderTags(array $tags): void
    {
        $this->whereIn('orders.id', function (Builder $query) use ($tags) {
            $query->select('order_id')
                ->from('order_tag')
                ->whereIn('tag_id', $tags);
        });
    }
}
