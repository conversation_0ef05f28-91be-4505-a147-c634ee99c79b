<?php

namespace App\Models;

use App\Jobs\SendWebhook;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use \Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

/**
 * App\Models\Webhook
 *
 * @method static \Database\Factories\WebhookFactory factory($count = null, $state = [])
 * @method static Builder|Webhook forDeliveryMethod(\App\Models\Pickup $method)
 * @method static Builder|Webhook forSalesChannel(?int $channel_id)
 * @method static Builder|Webhook forTopic(string $topic)
 * @method static Builder|Webhook newModelQuery()
 * @method static Builder|Webhook newQuery()
 * @method static Builder|Webhook query()
 * @property string $id
 * @property string $topic
 * @property string $target_url
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array|null $settings
 * @method static Builder|Webhook whereCreatedAt($value)
 * @method static Builder|Webhook whereId($value)
 * @method static Builder|Webhook whereSettings($value)
 * @method static Builder|Webhook whereTargetUrl($value)
 * @method static Builder|Webhook whereTopic($value)
 * @method static Builder|Webhook whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Webhook extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $keyType = 'string';

    public $incrementing = false;

    protected function casts(): array
    {
        return [
            'settings' => 'array',
        ];
    }

    protected static function booted(): void
    {
        static::creating(function ($model) {
            $model->id = Str::uuid()->toString();
        });
    }

    /**
     * @param  Builder<Webhook>  $query
     * @param  string  $topic
     * @return Builder<Webhook>
     */
    public function scopeForTopic(Builder $query, string $topic): Builder
    {
        return $query->where('topic', $topic);
    }

    /**
     * @param  Builder<Webhook>  $query
     * @param  Pickup  $method
     * @return Builder<Webhook>
     */
    public function scopeForDeliveryMethod(Builder $query, Pickup $method): Builder
    {
        return $query->where(function ($query) use ($method) {
            $query->whereJsonContains('settings->filters->delivery_methods', $method->id)
                ->orWhereJsonContains('settings->filters->delivery_methods', 0)
                ->orWhereJsonDoesntContainKey('settings->filters->delivery_methods')
                ->orWhereJsonLength('settings->filters->delivery_methods', 0);
        });
    }

    /**
     * @param  Builder<Webhook>  $query
     * @param  int|null  $channel_id
     * @return Builder<Webhook>
     */
    public function scopeForSalesChannel(Builder $query, ?int $channel_id): Builder
    {
        if (is_null($channel_id)) {
            return $query;
        }

        return $query->where(function ($query) use ($channel_id) {
            $query->whereJsonContains('settings->filters->sales_channels', $channel_id)
                ->orWhereJsonContains('settings->filters->sales_channels', 0)
                ->orWhereJsonDoesntContainKey('settings->filters->sales_channels')
                ->orWhereJsonLength('settings->filters->sales_channels', 0);
        });
    }

    public function send(array $payload = []): void
    {
        SendWebhook::dispatch($this, $payload);
    }
}