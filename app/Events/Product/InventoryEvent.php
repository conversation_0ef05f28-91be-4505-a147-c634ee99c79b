<?php

namespace App\Events\Product;

use App\Models\Product;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

abstract class InventoryEvent
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public int $product_id,
        public int $user_id,
        public array $attributes
    ){}

    abstract public function description(): string;
}
