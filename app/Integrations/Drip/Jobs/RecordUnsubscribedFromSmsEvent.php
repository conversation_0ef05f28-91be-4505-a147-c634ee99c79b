<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordUnsubscribedFromSmsEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user_id;

    public function __construct(int $user_id)
    {
        $this->user_id = $user_id;
    }

    public function handle()
    {
        $instance = DripIntegration::getInstance();

        if (is_null($instance)) return;

        $instance->recordEvent(
            User::findOrFail($this->user_id)->email,
            'Unsubscribed from SMS marketing'
        );
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
