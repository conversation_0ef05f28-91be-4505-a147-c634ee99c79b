<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateSubscriber implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;

    public $oldEmail;

    public $tags;

    public function __construct($userId, $oldEmail, $tags = [])
    {
        $this->userId = $userId;
        $this->oldEmail = $oldEmail;
        $this->tags = $tags;
    }

    public function handle()
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $drip->updateSubscriber(User::findOrFail($this->userId), $this->tags, $this->oldEmail);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
