<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordRecurringOrderCreated implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $recurring_order_id
    ) {}

    public function handle()
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        $recurringOrder = RecurringOrder::findOrFail($this->recurring_order_id);

        $customer = User::findOrFail($recurringOrder->customer_id);

        $drip->recordEvent($customer->email, 'New Subscription', [
            'reorder_frequency' => $recurringOrder->reorder_frequency,
            'next_deadline' => $recurringOrder->next_deadline,
            'next_delivery' => $recurringOrder->next_delivery,
            'schedule_id' => $recurringOrder->schedule_id,
            'fulfillment_id' => $recurringOrder->fulfillment_id,
        ]);

        $drip->patchSubscriber($customer, [
            'subscription_status' => 'Active',
            'subscription_order_count' => $customer->recurring_order_count ?? 1
        ]);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}
