<?php

namespace App\Services\Geocoding;

use App\Contracts\Geocoder;
use App\Exceptions\NoGeocodeResultsException;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class GeocoderCa implements Geocoder
{
    protected string $baseUrl = 'https://geocoder.ca';

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddress(string $address) : GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'locate' => $this->formatAddress($address),
                'json' => '1',
                'showcountry' => '1',
                'country' => 'canada',
                'auth' => $this->apiKey()
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromAddressParts(array $addressParts = []) : GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'locate' => $this->formatAddress($addressParts),
                'json' => '1',
                'showcountry' => '1',
                'country' => 'canada',
                'auth' => $this->apiKey()
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    public function fromZipcode(string $zip) : GeocodedAddress
    {
        try {
            $response = Http::get($this->baseUrl, [
                'locate' => trim($zip),
                'json' => '1',
                'showcountry' => '1',
                'country' => 'canada',
                'auth' => $this->apiKey()
            ])
                ->throw();
        } catch (RequestException $exception) {
            Bugsnag::notifyException($exception);
            throw new NoGeocodeResultsException;
        }

        return $this->parseResponse($response->json());
    }

    /**
     * @throws NoGeocodeResultsException
     */
    private function parseResponse(array $response) : GeocodedAddress
    {
        if ( ! $response) {
            throw new NoGeocodeResultsException('Unknown GeocoderCa failure. No response.');
        }

        if (isset($response['error'])) {
            throw new NoGeocodeResultsException($response['error']['description'] ?? 'GeocoderCa failed');
        }

        return new GeocodedAddress(
            $response['latt'],
            $response['longt'],
            $response['standard']['city'] ?? null,
            $response['standard']['prov'] ?? null,
            $response['postal'] ?? null,
            $response['country'] ?? null,
            $response['standard']['confidence'] ?? 1
        );
    }

    private function formatAddress(string|array $address): string
    {
        return trim(
            collect(Arr::wrap($address))
                ->map(fn($value) => trim($value))
                ->implode(', ')
        );
    }

    private function apiKey(): ?string
    {
        return config('services.geocoderCa.key');
    }
}
