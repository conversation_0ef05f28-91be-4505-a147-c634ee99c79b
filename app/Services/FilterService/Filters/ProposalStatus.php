<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class ProposalStatus extends Filter
{
    public static string $query_param = 'proposal_status';

    protected string $label = 'Status:';

    public function setValue(Request $request): void
    {
        $status = $request->get(static::$query_param);

        if (empty($status)) return;

        $this->value = \App\Support\Enums\ProposalStatus::whereIn(\Arr::wrap($status))->implode(', ');
    }
}
