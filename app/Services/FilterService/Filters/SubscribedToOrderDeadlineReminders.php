<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class SubscribedToOrderDeadlineReminders extends Filter
{
    public static string $query_param = 'order_deadline_email_reminder';

    protected string $label = 'Deadline Reminders:';

    public function setValue(Request $request): void
    {
        if ( ! $request->has(static::$query_param)) return;

        $this->value = $request->get(static::$query_param) ? 'Subscribed' : 'Not Subscribed';
    }
}