<?php

namespace App\Services\FilterService;

use App\Services\FilterService\Filters;
use Illuminate\Pipeline\Pipeline;
use Illuminate\Support\Collection;

class FilterService
{
    /**
     * @return Collection<int, string>
     */
    public function handle(): Collection
    {
        return app(Pipeline::class)
            ->send([request(), collect()])
            ->through(
                $this->pipes()
                    ->filter(fn ($filter) => request()->has($filter::$query_param))
                    ->toArray()
            )
            ->then(function ($passable) {
                list($request, $collection) = $passable;
                return $collection;
            });
    }

    /**
     * @return Collection<int, string>
     */
    private function pipes(): Collection
    {
        return collect([
            Filters\Orders::class,
            Filters\OrderStatus::class,
            Filters\Schedule::class,
            Filters\Location::class,
            Filters\ConfirmedDate::class,
            Filters\PickupDate::class,
            Filters\PaymentDate::class,
            Filters\DeliveryDate::class,
            Filters\Paid::class,
            Filters\Flagged::class,
            Filters\FulfillmentError::class,
            Filters\PickedUp::class,
            Filters\Exported::class,
            Filters\FirstTimeOrder::class,
            Filters\Payment::class,
            Filters\ConfirmationStatus::class,
            Filters\SalesChannel::class,
            Filters\OrderTags::class,
            Filters\Products::class,
            Filters\Sku::class,
            Filters\Vendor::class,
            Filters\PackingGroup::class,
            Filters\UnitOfIssue::class,
            Filters\TrackInventory::class,
            Filters\OnSale::class,
            Filters\Collection::class,
            Filters\ShowDeleted::class,
            Filters\OutOfStock::class,
            Filters\ProductVisibility::class,
            Filters\IsBundle::class,
            Filters\LastPurchase::class,
            Filters\CreatedAt::class,
            Filters\State::class,
            Filters\ShippingState::class,
            Filters\WasReferred::class,
            Filters\SubscribedToNewsletter::class,
            Filters\Active::class,
            Filters\SubscribedToOrderDeadlineReminders::class,
            Filters\Role::class,
            Filters\Locations::class,
            Filters\LocationStatus::class,
            Filters\ProposalStatus::class,
            Filters\Rating::class,
            Filters\Tags::class,
            Filters\Customer::class,
            Filters\AccountingClass::class,
            Filters\PackedBy::class,
            Filters\Query::class,
            Filters\Leads::class,
            Filters\PostalCode::class,
            Filters\SubscriptionStatus::class,
            Filters\DateRange::class,
            Filters\DateType::class,
            Filters\PackDeadlineAt::class,
        ]);
    }
}
