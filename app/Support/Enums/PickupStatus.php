<?php

namespace App\Support\Enums;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum PickupStatus: int
{
    case OPEN = 1;
    case PROPOSED = 2;
    
    case CLOSED = 3;
    case COMING_SOON = 4;

    /**
     * @return Collection<int, string>
     */
    public static function whereIn(mixed $keys): Collection
    {
        return PickupStatus::all()
            ->filter(fn ($value, $key) => in_array($key, Arr::wrap($keys)));
    }

    /**
     * @return Collection<int, string>
     */
    public static function all(): Collection
    {
        return collect(PickupStatus::cases())
            ->mapWithKeys(fn (PickupStatus $status): array => PickupStatus::mappedToLabel($status));
    }

    /**
     * @return array<int, string>
     */
    private static function mappedToLabel(PickupStatus $status): array
    {
        return [$status->value => $status->label()];
    }

    public function label(): string
    {
        return match ($this) {
            PickupStatus::OPEN => 'Open',
            PickupStatus::PROPOSED => 'Proposed',
            PickupStatus::CLOSED => 'Closed',
            PickupStatus::COMING_SOON => 'Coming Soon',
        };
    }

    public static function get(int $status_id): ?string
    {
        return PickupStatus::tryFrom($status_id)?->label();
    }

    public static function open(): int
    {
        return PickupStatus::OPEN->value;
    }

    public static function proposed(): int
    {
        return PickupStatus::PROPOSED->value;
    }

    public static function closed(): int
    {
        return PickupStatus::CLOSED->value;
    }

    public static function comingSoon(): int
    {
        return PickupStatus::COMING_SOON->value;
    }
}
