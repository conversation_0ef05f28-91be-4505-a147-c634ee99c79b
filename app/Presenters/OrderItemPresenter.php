<?php

namespace App\Presenters;

use App\Models\OrderItem;
use Laracasts\Presenter\Presenter;

class OrderItemPresenter extends Presenter
{
    public function orderItem(): OrderItem
    {
        return $this->entity;
    }

    /**
     * Present the unit of issue.
     */
    public function unitOfIssue(): string
    {
        return match ($this->orderItem()->unit_of_issue) {
            'weight' => '/' . __("messages.uom." . setting('weight_uom', 'pounds')),
            'package' => '/ea',
            default => '',
        };
    }
}
