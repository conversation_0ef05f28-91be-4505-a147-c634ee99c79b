<?php

namespace App\Presenters;

use App\Models\Pickup;
use App\Traits\PickupDateSelectBuilder;
use Lara<PERSON>s\Presenter\Presenter;

class PickupPresenter extends Presenter
{
    use PickupDateSelectBuilder;

    public function title(): ?string
    {
        if ($this->pickup()->display_name) {
            return $this->pickup()->display_name;
        }

        return $this->pickup()->isDeliveryZone()
            ? 'Home Delivery'
            : $this->pickup()->title;
    }

    public function pickup(): Pickup
    {
        return $this->entity;
    }

    public function fullAddress(string $break = '<br>'): string
    {
        if (!$this->pickup()->hasAddress()) {
            return '';
        }

        $address = addslashes($this->pickup()->street);
        if ($this->pickup()->street_2) {
            $address .= ' ' . addslashes($this->pickup()->street_2);
        }
        $address .= $break . addslashes($this->pickup()->city) . ', ' . $this->pickup()->state . ' ' . $this->pickup()->zip;

        return  $address;
    }

    public function nextPickupDate()
    {
        $next_date = $this->pickup()->nextDate;

        if ( is_null($next_date)) {
            return 'Pickup Date TBD';
        }

        return $next_date->toOrderWindows()
            ->deliveryDatetime()
            ->format('D F jS');
    }

    /**
     * Present the direction's URL.
     */
    public function directionsUrl(): string
    {
        $baseUrl = 'https://maps.google.com/maps/dir/?api=1&destination=';
        $address = $this->pickup()->street . '+' . $this->pickup()->city . '+' . $this->pickup()->state . '+' . $this->pickup()->zip;
        return $baseUrl . urlencode($address);
    }

    public function taxRate(): float
    {
        return $this->pickup()->tax_rate * 100;
    }

    public function openPickupDateSelect(?string $selected = null): string
    {
        $orderingInAdvance = null;

        $schedule = $this->pickup()->schedule;

        if ( ! is_null($schedule) && $schedule->ordering_in_advance >= 1) {
            $orderingInAdvance = min($schedule->ordering_in_advance, 365);
        }

        return $this->buildPickupDateSelect(
            $this->pickup()->activeOrderWindowCollection($orderingInAdvance),
            $selected
        );
    }
}
