<?php

namespace App\Console\Commands;

use App\Events\Product\InventoryDecreasedToThresholdOrBelow;
use App\Events\Product\InventoryDecreasedToZeroOrBelow;
use App\Events\Product\InventoryIncreasedAboveThreshold;
use App\Events\Product\InventoryIncreasedAboveZero;
use App\Models\Event;
use App\Models\Product;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class CheckInventoryLevels extends Command
{
    protected $signature = 'products:check-inventory-levels';

    protected $description = 'Fire events for any products that have moved between configured inventory levels.';

    public function handle(): void
    {
        $cron_user = User::cron();

        $this->baseQuery()
            ->whereRaw('inventory + other_inventory > 0')
            ->whereRaw('inventory + other_inventory <= oos_threshold_inventory')
            ->whereNotNull('oos_threshold_inventory')
            ->get()
            ->filter(fn(Product $product) => is_null($product->most_recent_event) || $product->most_recent_event !== InventoryDecreasedToThresholdOrBelow::class)
            ->each(function (Product $product) use ($cron_user) {
                event(new InventoryDecreasedToThresholdOrBelow(
                    product_id: $product->id,
                    user_id: $cron_user->id,
                    attributes: $product->toArray()
                ));
            });

        $this->baseQuery()
            ->where(function ($query) {
                $query
                    ->whereNull('oos_threshold_inventory')
                    ->orWhereNot('oos_threshold_inventory', 0);
            })
            ->whereRaw('inventory + other_inventory <= 0')
            ->get()
            ->filter(fn(Product $product) => $product->most_recent_event !== InventoryDecreasedToZeroOrBelow::class)
            ->each(function (Product $product) use ($cron_user) {

                event(new InventoryDecreasedToZeroOrBelow(
                    product_id: $product->id,
                    user_id: $cron_user->id,
                    attributes: $product->toArray()
                ));
            });

        $this->baseQuery()
            ->whereRaw('inventory + other_inventory > 0')
            ->get()
            ->filter(fn(Product $product) => $product->most_recent_event === InventoryDecreasedToZeroOrBelow::class)
            ->each(function (Product $product) use ($cron_user) {
                event(new InventoryIncreasedAboveZero(
                    product_id: $product->id,
                    user_id: $cron_user->id,
                    attributes: $product->toArray()
                ));
            });

        $this->baseQuery()
            ->whereNotNull('oos_threshold_inventory')
            ->where('oos_threshold_inventory', '>', 0)
            ->whereRaw('inventory + other_inventory > oos_threshold_inventory')
            ->get()
            ->filter(fn(Product $product) => in_array($product->most_recent_event, [InventoryDecreasedToZeroOrBelow:: class, InventoryDecreasedToThresholdOrBelow:: class]))
            ->each(function (Product $product) use ($cron_user) {
                event(new InventoryIncreasedAboveThreshold(
                    product_id: $product->id,
                    user_id: $cron_user->id,
                    attributes: $product->toArray()
                ));
            });
    }

    /**
     * @return Builder<Product>
     */
    protected function baseQuery(): Builder
    {
        return Product::query()
            ->select(['title', 'sku', 'barcode', 'accounting_class', 'inventory_type', 'weight', 'oos_threshold_inventory', 'inventory', 'id', 'unit_price', 'sale_unit_price', 'unit_description', 'unit_of_issue', 'item_cost', 'processor_inventory', 'other_inventory', 'vendor_id', 'custom_sort'])
            ->where('track_inventory', 'yes')
            ->addSelect([
                'most_recent_event' => Event::select('event_id')
                    ->where('model_type', Product::class)
                    ->whereColumn('model_id', 'products.id')
                    ->orderByDesc('created_at')
                    ->limit(1)
            ])
            ->with(['vendor' => fn($query) => $query->select('id', 'title')]);
    }
}
