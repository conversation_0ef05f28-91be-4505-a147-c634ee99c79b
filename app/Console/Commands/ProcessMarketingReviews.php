<?php

namespace App\Console\Commands;

use App\Actions\CaptureMetrics;
use App\Jobs\SendMarketingReview;
use App\Repositories\Reports\MarketingReview\MarketingReview;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ProcessMarketingReviews extends Command
{
    protected $signature = 'grazecart:marketing-review 
        {--year= : The year the review is processed for. Defaults to the current year} 
        {--month= : The month the review is processed for. Defaults to the current month} 
        {--tenant= : The tenant_id the review is processed for. Defaults to all enabled tenants}';

    protected $description = 'Process marketing reviews.';

    public function handle(): void
    {
        $marketing_review = app(MarketingReview::class)->handle(anchor_date: Carbon::createFromDate(
            year: (int) ($this->option('year') ?? today()->format('Y')),
            month: (int) ($this->option('month') ?? today()->format('m')),
            day: 1
        ));

        app(CaptureMetrics::class)->handle(name: 'marketing_review', values: $marketing_review);

        SendMarketingReview::dispatch($marketing_review);
    }
}
