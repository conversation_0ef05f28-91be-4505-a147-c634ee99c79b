<?php

namespace App\API\V2\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Resources\CustomerResource;
use App\Models\User;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        return CustomerResource::collection(
            User::query()->customers()->latest()->paginate(10)
        );
    }

    public function show(User $customer): JsonResource
    {
        return new CustomerResource($customer);
    }
}
