<?php

namespace App\API\V1\Actions;

use App\Models\Order;
use App\Support\Enums\OrderStatus;

class UpdateOrderStatus
{
    protected $statuses;

    public function __construct()
    {
        $this->getListOfOrderStatuses();
    }

    public function run(Order $order, $orderStatus)
    {
        return $order->update([
            'status_id' => $this->statuses->get($orderStatus) ?? $order->status_id
        ]);
    }

    private function getListOfOrderStatuses()
    {
        $this->statuses = collect(OrderStatus::all())->map(function ($status, $key) {
            return strtolower(str_replace([' ', '-'], '_', $status));
        })->flip();
    }
}
