<?php

namespace App\API\V1\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Sanctum\Guard;

class Authenticate
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!$request->expectsJson()) {
            return response()->json([
                'message' => 'Please set the accept type to json.',
            ], 400);
        }

        $token = $request->bearerToken();
        $tokenPrefix = Str::substr($token, 0, 5);
        $request['sanctum_authenticated'] = false;

        try {
            $apiKey = ApiKey::wherePrefix($tokenPrefix)->firstOrFail();
            $request['_apiKey'] = $apiKey;

            if (!Hash::check($token, $apiKey->key)) {
                return response()->json([
                    'message' => 'You are not authorized',
                ], 401);
            }

            $apiKey->last_used_at = now();
            $apiKey->save();

        } catch (ModelNotFoundException $exception) {
            if ( ! app(Guard::class)($request)) {
                return response()->json([
                    'message' => 'You are not authorized',
                ], 401);
            }

            $request['sanctum_authenticated'] = true;
        }



        return $next($request);
    }
}
