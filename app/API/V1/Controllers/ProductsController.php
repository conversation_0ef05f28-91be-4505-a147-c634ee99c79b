<?php

namespace App\API\V1\Controllers;

use App\API\V1\Resources\ProductResource;
use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class ProductsController extends Controller
{
    public function index(Request $request)
    {
        if ( ! $request['sanctum_authenticated'] && $request['_apiKey']->doesNotHaveScope('products:index')) {
            return response()->json([
                'error' => 'You do not have permission to view products.'
            ], 403);
        }

        $limit = min($request->input('limit', 100), 100);
        return ProductResource::collection(
            Product::filter($request->all())->paginate($limit)
        );
    }

    public function show(Request $request, $productId)
    {
        if ( ! $request['sanctum_authenticated'] && $request['_apiKey']->doesNotHaveScope('products:show')) {
            return response()->json([
                'error' => 'You do not have permission view this product.'
            ], 403);
        }

        try {
            if (is_numeric($productId)) {
                return new ProductResource(Product::where('id', $productId)
                    ->orWhere('sku', $productId)->firstOrFail());
            } else {
                return new ProductResource(Product::where('sku', $productId)->firstOrFail());
            }
        } catch (ModelNotFoundException $exception) {
            return response()->json([
                'error' => "A product could not be found with the id of: {$productId}"
            ], 404);
        }
    }
}
