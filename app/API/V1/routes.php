<?php

use App\API\V1\Controllers\HeartbeatController;
use App\API\V1\Controllers\OrderItemsController;
use App\API\V1\Controllers\OrdersController;
use App\API\V1\Controllers\ProductInventoryController;
use App\API\V1\Controllers\ProductsController;

Route::get('/heartbeat', HeartbeatController::class)->name('api.v1.heartbeat');

Route::get('/orders', [OrdersController::class, 'index']);
Route::get('/orders/{order}', [OrdersController::class, 'show']);
Route::put('/orders/{order}', [OrdersController::class, 'update']);

Route::get('/orders/{order}/items', [OrderItemsController::class, 'index']);
Route::get('/orders/{order}/items/{item}', [OrderItemsController::class, 'show']);
Route::put('/orders/{order}/items/{item}', [OrderItemsController::class, 'update']);

Route::get('/products', [ProductsController::class, 'index']);
Route::get('/products/{products}', [ProductsController::class, 'show']);

Route::put('/products/{product}/inventory', [ProductInventoryController::class, 'update'])->name('products.inventory.update');
