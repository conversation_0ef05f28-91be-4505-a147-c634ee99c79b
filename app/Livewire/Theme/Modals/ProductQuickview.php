<?php

namespace App\Livewire\Theme\Modals;

use App\Livewire\Theme\AddsProduct;
use App\Repositories\StoreRepository;
use Livewire\Attributes\On;
use Livewire\Component;

class ProductQuickview extends Component
{
    use ModalAttributes, AddsProduct;

    public ?int $product_id = null;

    public array $product_metadata = [];

    public ?int $selected_variant_id = null;

    public function render()
    {
        return view('theme::livewire.modals.product-quickview');
    }

    public function add()
    {
        if (is_null($this->selected_variant_id)) return;

        match(true) {
            $this->has_subscription => $this->addToSubscription($this->selected_variant_id),
            $this->has_order => $this->addToOrder($this->selected_variant_id),
            default => $this->addAndOpenCartPanel()
        };

        $this->close();
    }

    private function addAndOpenCartPanel()
    {
        $this->addToCart($this->selected_variant_id, $this->product_metadata);
        $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel');
    }

    #[On('close-modal-product-quickview')]
    public function close(): void
    {
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-product-quickview')]
    public function open(?int $product_id = null, ?array $product_metadata = []): void
    {
        $this->product_id = $product_id;
        $this->selected_variant_id = $product_id;
        $this->product_metadata = $product_metadata;

        $this->product = new StoreRepository(request())
            ->getIndividualProduct($this->product_id, column: 'id')
            ->first();

        $this->openModal();
    }
}
