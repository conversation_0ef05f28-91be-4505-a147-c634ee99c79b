<?php

namespace App\Livewire\Theme;

use App\Models\Order;
use App\Models\Pickup;
use App\Models\RecurringOrder;
use Illuminate\Support\Facades\Cookie;
use Livewire\Component;

class OrderStatusBar extends Component
{
    use FetchesCart;

    public ?Pickup $current_delivery_method = null;

    public ?Order $order = null;

    public ?RecurringOrder $subscription = null;

    protected $listeners = [
        'cartUpdated' => 'refreshCart',
    ];

    public function mount(?int $current_delivery_method_id = null): void
    {
        if (is_null($current_delivery_method_id)) {
            $current_delivery_method_id = Cookie::get('shopping_delivery_method_id');
        }
        if ( ! is_null($current_delivery_method_id)) {
            $this->current_delivery_method = Pickup::find($current_delivery_method_id);
        }
    }

    public function render()
    {
        return view('theme::livewire.order-status-bar');
    }

    public function refreshCart(): void
    {
        $this->current_delivery_method = $this->fetchShopperCart()?->cartLocation();
    }
}
