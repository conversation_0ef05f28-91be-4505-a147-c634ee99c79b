<?php

namespace App\Livewire\Theme\Authentication;

use App\Contracts\CartService;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserWasRegistered;
use App\Models\Coupon;
use App\Models\Lead;
use App\Models\User;
use App\Rules\AllowedEmailDomain;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use App\Support\FetchesGeocodedAddress;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\View\View;
use Livewire\Component;

class Registration extends Component
{
    use FetchesGeocodedAddress;

    public bool $needs_password = false;

    public string $username = '';

    public ?int $timestamp = null;

    public string $email = '';

    public string $password = '';

    public function mount(): void
    {
        $this->timestamp = time();
    }

    public function render(): View
    {
        return view('theme::livewire.authentication.registration.index');
    }

    public function submit()
    {
        if (auth()->check()) {
            return redirect()->route('customer.profile');
        }

        $validated = $this->validate([
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime],
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255'],
        ], [
            'email.indisposable' => 'A valid email address is required.',
        ]);

        $existing_user = User::where('email', $validated['email'])->first();

        if ( ! is_null($existing_user)) {
            if ($existing_user->needsPassword()) {
                $this->needs_password = true;
                return;
            }

            return $this->handleExistingUser($existing_user);
        }

        return $this->handleRegistration(collect(['email' => $this->email]));
    }

    private function handleExistingUser(User $existing_user)
    {
        flash('An account already exists. If this is you, please login to continue.');
        return redirect()->to(route('login'))->with(['email' => $existing_user->email]);
    }

    private function handleRegistration(Collection $attributes)
    {
        if (request()->hasCookie('referral_code')) {
            $attributes->put('referral_code', request()->cookie('referral_code'));
        }

        $user = User::register($attributes);

        Auth::login($user);

        event(new UserSubscribedToNewsletter($user));
        event(new UserWasRegistered($user));

        Lead::where('email', $user->email)->delete();

        $cart = app(CartService::class)
            ->create(shopper_type: User::class, shopper_id: (string) $user->id);

        $cart->applyConditionalCouponToCart(Coupon::welcomeCoupon());

        $redirect = Cookie::get('last_viewed_page', '/store');

        Cookie::queue(Cookie::forget('last_viewed_page'));

        session()->flash('open-cart-side-panel');

        return redirect()
            ->to(appendQueryParamsToUrl(url($redirect), ['firstlogin' => now()->timestamp]))
            ->with('userWasCreated', true);
    }

    public function register()
    {
        $this->validate([
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime],
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255'],
        ]);

        return $this->handleRegistration(collect(['email' => $this->email]));
    }

    public function choosePassword()
    {
        $validated = $this->validate([
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255'],
            'password' => ['required', 'min:8', 'max:255'],
        ]);

        $user = User::where('email', $validated['email'])->first();

        if ( ! $user) {
            $this->addError('email', 'The selected email address is invalid.');
            return;
        }

        if ( ! $user->needsPassword()) {
            error('A password has already been set up. Please login.');
            return redirect()->route('login');
        }

        $user->password = $validated['password'];
        $user->save();

        flash('Your password has been updated and you are now logged in.');

        Auth::login($user);

        $user->last_login = now();
        $user->save();

        if (setting('user_login_redirect')) {
            return redirect()->to(setting('user_login_redirect'));
        }

        $redirect = Cookie::get('last_viewed_page', '/store');

        Cookie::queue(Cookie::forget('last_viewed_page'));

        return redirect()->to(url($redirect));
    }
}
