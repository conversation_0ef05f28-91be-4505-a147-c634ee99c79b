<?php

namespace App\Livewire\Theme;

use App\Models\Order;
use Livewire\Component;

class OrderSidePanel extends Component
{
    use FetchesOrder;

    public ?Order $order = null;

    protected $listeners = [
        'orderUpdated' => 'refreshOrder',
    ];

    public function mount()
    {
        $this->refreshOrder();
    }

    public function refreshOrder()
    {
        $this->order = ! is_null($this->order)
            ? $this->findOrder($this->order->id)
            : $this->fetchCustomerOrder();
    }

    public function render()
    {
        return view('theme::livewire.order-side-panel');
    }

}
