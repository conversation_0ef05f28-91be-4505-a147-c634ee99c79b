<?php

namespace App\Livewire\Theme;

use App\Models\Pickup;
use App\Models\RecurringOrder;
use Livewire\Component;

class SubscriptionSidePanel extends Component
{
    use FetchesSubscription;

    public ?RecurringOrder $subscription = null;
    public ?Pickup $delivery_method = null;

    protected $listeners = [
        'subscriptionUpdated' => 'refreshSubscription',
    ];

    public function mount()
    {
        $this->refreshSubscription();
    }

    public function refreshSubscription()
    {
        /** @var RecurringOrder|null $subscription */
        $subscription = auth()->user()->recurringOrder()
            ->with(['customer', 'fulfillment'])
            ->first();

        if ( ! is_null($subscription)) {
            $subscription->load([
                'items.product.price' => fn($q) => $q->where('group_id', $subscription->pricingGroupId() ?? 0)
            ]);
        }

        $this->subscription = $subscription;
        $this->delivery_method = $this->subscription?->fulfillment;
    }

    public function render()
    {
        return view('theme::livewire.subscription-side-panel');
    }
}
