<?php

namespace App\Livewire\Theme;

use App\Models\Category;
use App\Repositories\StoreRepository;
use Livewire\Attributes\Locked;
use Livewire\Component;

class CategoryProductList extends Component
{
    #[Locked]
    public Category $category;

    public $products = [];

    public int $page = 1;

    public bool $has_more = true;

    public int $total = 0;

    public string $type = 'standard';

    public bool $show_about = false;

    public bool $add_breadcrumb_spacing = true;

    public function mount()
    {
        $products = (new StoreRepository(request()))
            ->getProductsInCategory($this->category)
            ->paginate(perPage: 8, page: $this->page);

        $this->products = collect($products->items());
        $this->page = $products->currentPage();
        $this->has_more = $products->hasMorePages();
        $this->total = $products->total();
    }

    public function render()
    {
        return view('theme::livewire.category-product-list');
    }

    public function loadMoreProducts()
    {
        if (! $this->has_more) return;

        $this->page = $this->page + 1;

        $products = (new StoreRepository(request()))
            ->getProductsInCategory($this->category)
            ->paginate(perPage: 8, page: $this->page);

        logger()->info('Fetched products:', $products->items());

        $this->has_more = $products->hasMorePages();
        $this->products = $this->products->concat($products->items());

        logger()->info('Updated product list:', $this->products->toArray());
    }

    public function placeholder(array $params = [])
    {
        return view('theme::livewire.placeholders.category-product-list', $params);
    }
}
