<?php

namespace App\Livewire\Theme;

use App\Services\StoreService;
use Livewire\Component;

class OrderItemCount extends Component
{
    use FetchesOrder;

    public int $count;

    protected $listeners = [
        'orderUpdated' => 'refreshCount',
    ];

    public function mount()
    {
       $this->refreshCount();
    }

    public function refreshCount()
    {
        $this->count = app(StoreService::class)->orderItemCount();
    }

    public function render()
    {
        return <<<'blade'
            <span>{{ $count }}</span>
        blade;
    }
}
