<?php

namespace App\Livewire\Admin;

use App\Models\Address;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Support\Str;
use Livewire\Component;

class AddressesTable extends Component
{
    public User $user;

    public function render()
    {
        $addresses = $this->user->addresses;

        if ($addresses->isEmpty() && $this->user->hasAddressAttributes()) {
            $this->user->migrateUserAddress();
            $addresses = $this->user->addresses()->get();
        }

        return view('livewire.addresses-table', compact('addresses'));
    }
}
