<?php

namespace App\Livewire\Admin\Modals;

use App\Contracts\Geocoder;
use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Address;
use App\Models\User;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use App\Services\SettingsService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Component;

class AddAddress extends Component
{
    use ModalAttributes;

    public int $user_id;

    public string $street = '';
    public string $street_2 = '';
    public string $city = '';
    public string $state = '';
    public string $postal_code = '';
    public string $instructions = '';
    public bool $is_default = false;

    protected array $rules = [
        'street' => ['required', 'string'],
        'street_2' => ['nullable', 'string'],
        'city' => ['required'],
        'state' => ['required'],
        'postal_code' => ['required'],
        'instructions' => ['nullable', 'string'],
        'is_default' => ['boolean'],
    ];

    public function render()
    {
        return view('livewire.modals.add-address');
    }

    public function submit()
    {
        $validated = $this->validate();

        try {
            $geocoded_address = app(Geocoder::class)->fromZipcode($validated['postal_code']);
        } catch (\Exception $e) {
            return [
                $this->addError('street', 'We could not locate this address.'),
                $this->addError('postal_code', 'Enter a valid postal code.')
            ];
        }

        if (! $this->deliveryIsAvailable($geocoded_address)) {
            return $this->addError('street', 'Delivery is not available at this address.');
        }

        $attributes = Arr::except($validated, ['street_2', 'instructions', 'is_default']);

        $address = Address::firstOrCreate(array_merge($attributes, ['country' => app(SettingsService::class)->farmCountry()]));

        $user = User::find($this->user_id);

        if ($validated['is_default'] ?? false) {
            $this->resetAddressDefaults($user);
        }

        $user->addresses()->attach($address->id, [
            'name' => Str::limit("{$validated['street']}, {$validated['city']}, {$validated['state']}, {$validated['postal_code']}"),
            'street_2' => $validated['street_2'] ?? '',
            'instructions' => $validated['instructions'],
            'is_default' => ($validated['is_default'] ?? false) || $user->addresses()->doesntExist()
        ]);

        flash('The address has been added to the customer!');

        return $this->redirect("/admin/users/{$user->id}/edit?tab=addresses");
    }

    public function deliveryIsAvailable(GeocodedAddress $geocoded_address): bool
    {
        return app(DeliveryMethodService::class)
            ->deliveryZones()
            ->find($geocoded_address)
            ->isNotEmpty();
    }

    private function resetAddressDefaults(User $user): void
    {
        $user->addresses()->update(['is_default' => false]);
    }

    #[On('close-modal-add-address')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-add-address')]
    public function open(int $user_id): void
    {
        $this->user_id = $user_id;
        
        $this->openModal();
    }
}
