<?php

namespace App\Livewire\Admin\Modals;

use App\Events\Subscription\SubscriptionUpdated;
use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\RecurringOrderItem;
use Livewire\Attributes\On;
use Livewire\Component;

class EditSubscriptionItem extends Component
{
    use ModalAttributes;

    public int $item_id;

    public ?int $product_id = null;

    public int $qty = 1;

    public string $type = 'recurring';

    public ?string $formatted_unit_price_override = null;

    public bool $selecting_product = false;

    protected $rules = [
        'product_id' => ['required', 'int', 'exists:products,id'],
        'qty' => ['required', 'int', 'min:1'],
        'type' => ['required', 'in:recurring,promo,addon'],
        'formatted_unit_price_override' => ['nullable', 'numeric', 'min:0'],
    ];

    protected $listeners = [
        'productSelected',
    ];

    public function render()
    {
        return view('livewire.modals.edit-subscription-item');
    }

    public function submit(): void
    {
        $validated = $this->validate($this->rules, [
            'formatted_unit_price_override.numeric' => 'The unit price must be numeric.',
            'formatted_unit_price_override.min' => 'The unit price must at least 0.',
        ]);

        $item = RecurringOrderItem::find($this->item_id)
            ?->fill([
                'product_id' => $validated['product_id'],
                'qty' => $validated['qty'],
                'type' => $validated['type'],
                'unit_price_override' => ! empty($validated['formatted_unit_price_override'])
                    ? (int) round($validated['formatted_unit_price_override'] * 100)
                    : null,
            ]);

        if (is_null($item)) {
            return;
        }

        $item->save();

        $this->setProperties($item);

        event(new SubscriptionUpdated($item->order));

        $this->dispatch('subscriptionUpdated');
        $this->close();
    }

    public function setProperties(RecurringOrderItem $item): void
    {
        $this->product_id = $item->product_id;
        $this->qty = $item->qty;
        $this->type = $item->type;
        $this->formatted_unit_price_override = ! is_null($item->unit_price_override)
            ? money($item->unit_price_override)
            : null;
    }

    #[On('close-modal-edit-subscription-item')]
    public function close(): void
    {
        $this->closeModal();
    }

    public function productSelected(int $product_id): void
    {
        $this->product_id = $product_id;
    }

    #[On('open-modal-edit-subscription-item')]
    public function open(int $item_id): void
    {
        $this->item_id = $item_id;
        $this->setProperties(RecurringOrderItem::find($this->item_id));
        $this->openModal();
    }
}
