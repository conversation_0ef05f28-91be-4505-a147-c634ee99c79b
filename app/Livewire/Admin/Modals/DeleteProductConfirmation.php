<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\On;
use Livewire\Component;

class DeleteProductConfirmation extends Component
{
    use ModalAttributes;

    public ?int $product_id = null;

    public string $subscription_action = 'remove';

    public ?int $replacement_product_id = null;

    protected $listeners = [
        'productSelected',
    ];

    public function render()
    {
        $subscriptions = RecurringOrder::query()
            ->whereHas('items', function (Builder $query) {
                $query->where('product_id', $this->product_id);
            })
            ->pluck('id');

        return view('livewire.modals.delete-product-confirmation', [
            'product' => Product::find($this->product_id),
            'subscriber_count' => $subscriptions->count(),
            'on_order_count' => $subscriptions->isNotEmpty()
                ? RecurringOrderItem::query()
                    ->where('product_id', $this->product_id)
                    ->whereIn('order_id', $subscriptions)
                    ->sum('qty')
                : 0,
        ]);
    }

    public function submit()
    {
        $this->validate([
            'subscription_action' => ['required', 'string', 'in:remove,replace'],
            'replacement_product_id' => ['nullable', 'required_if:subscription_action,replace', 'int', function (string $attribute, ?int $value, \Closure $fail) {
                if ($value === $this->product_id) {
                    $fail('The replacement product is invalid.');
                }
            }],
        ]);

        match ($this->subscription_action) {
            'replace' => $this->replace(),
            default => $this->remove()
        };

        Product::find($this->product_id)?->delete();

        flash('Product was successfully deleted'.($this->subscription_action === 'replace' ? ' and subscriptions have been updated!' : '!'));

        return $this->redirect(route('admin.products.index'));
    }

    private function replace(): void
    {
        RecurringOrderItem::query()
            ->where('product_id', $this->product_id)
            ->update([
                'product_id' => $this->replacement_product_id,
                'unit_price_override' => null
            ]);
    }

    private function remove(): void
    {
        RecurringOrderItem::query()
            ->where('product_id', $this->product_id)
            ->delete();
    }

    public function productSelected(int $product_id): void
    {
        $this->replacement_product_id = $product_id;
    }

    #[On('close-modal-delete-product-confirmation')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-delete-product-confirmation')]
    public function open(int $product_id): void
    {
        $this->product_id = $product_id;
        $this->openModal();
    }
}
