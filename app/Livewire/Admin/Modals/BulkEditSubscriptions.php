<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use Carbon\Carbon;
use Livewire\Attributes\On;
use Livewire\Component;

class BulkEditSubscriptions extends Component
{
    use ModalAttributes;

    public string $action = 'date';

    public ?string $bulk_selection_type = null;

    public array $subscription_ids = [];

    public int $subscription_count = 0;

    public ?string $delivery_date = null;

    public string $product_action = 'remove';

    public ?int $removed_product_id = null;

    public ?int $replacement_product_id = null;

    public bool $expedited_date_confirmation = false;

    public function mount(): void
    {
        $this->delivery_date = today()->addWeek()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.modals.bulk-edit-subscriptions');
    }

    public function submit()
    {
        $this->validate($this->rules());

        $this->dispatch(
            event: 'bulk-action-selected',
            action: $this->action,
            params: $this->bulkActionParams()
        );

        $this->close();
    }

    protected function rules(): array
    {
        $expedited_date_confirmation_rules = [];

        if ($this->action === 'date' && today()->diffInDays(Carbon::parse($this->delivery_date)) <= 5) {
            $expedited_date_confirmation_rules[] = 'accepted';
        }

        return $this->action === 'date'
            ? [
                'delivery_date' => ['required', 'date', 'after_or_equal:today'],
                'expedited_date_confirmation' => $expedited_date_confirmation_rules,
            ]
            : [
                'product_action' => ['required', 'in:remove,replace'],
                'removed_product_id' => ['required', 'exists:products,id'],
                'replacement_product_id' => ['nullable', 'required_if:product_action,replace', 'exists:products,id'],
            ];
    }

    protected function bulkActionParams()
    {
        return $this->action === 'date'
            ? [
                'delivery_date' => $this->delivery_date,
                'bulk_selection_type' => $this->bulk_selection_type,
                'subscription_ids' => $this->subscription_ids,
            ]
            : [
                'product_action' => $this->product_action,
                'removed_product_id' => $this->removed_product_id,
                'replacement_product_id' => $this->replacement_product_id,
                'bulk_selection_type' => $this->bulk_selection_type,
                'subscription_ids' => $this->subscription_ids,
            ];
    }

    #[On('close-modal-bulk-edit-subscriptions')]
    public function close(): void
    {
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-bulk-edit-subscriptions')]
    public function open(?string $bulk_selection_type, array $subscription_ids, int $subscription_count): void
    {
        $this->bulk_selection_type = $bulk_selection_type;
        $this->subscription_ids = $subscription_ids;
        $this->subscription_count = $subscription_count;
        $this->openModal();
    }
}
