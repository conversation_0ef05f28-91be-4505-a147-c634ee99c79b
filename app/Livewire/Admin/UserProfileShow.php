<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Illuminate\Validation\Rule;
use Livewire\Component;

class UserProfileShow extends Component
{
    use SendsAdminNotifications;

    public User $user;

    public ?string $editing = null;
    public ?string $slug = null;
    public ?string $position_title = null;
    public ?string $bio = null;
    public ?string $facebook = null;
    public ?string $twitter = null;
    public ?string $linkedin = null;
    public ?string $photo_path = null;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->slug = $user->profile->slug ?? $user->authorSlug();
        $this->position_title = $user->profile?->position_title;
        $this->bio = $user->profile?->bio;
        $this->facebook = $user->profile?->facebook;
        $this->twitter = $user->profile?->twitter;
        $this->linkedin = $user->profile?->linkedin;
        $this->photo_path = $user->profile?->photo_path;
    }

    public function render()
    {
        return view('livewire.user-profile-show');
    }

    public function saveSlug()
    {
        $validated = $this->validate([
            'slug' => [
                'string', 'max:255',
                Rule::unique('profiles', 'slug')->ignore($this->user->profile->id)
            ],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['slug' => $validated['slug']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The slug has been updated!',
        ]);
    }

    public function savePositionTitle()
    {
        $validated = $this->validate([
            'position_title' => ['string', 'max:255'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['position_title' => $validated['position_title']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The position title has been updated!',
        ]);
    }

    public function saveBio()
    {
        $validated = $this->validate([
            'bio' => ['string'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['bio' => $validated['bio']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The bio has been updated!',
        ]);
    }

    public function saveFacebook()
    {
        $validated = $this->validate([
            'facebook' => ['nullable', 'url'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['facebook' => $validated['facebook']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The facebook profile has been updated!',
        ]);
    }

    public function saveTwitter()
    {
        $validated = $this->validate([
            'twitter' => ['nullable', 'url'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['twitter' => $validated['twitter']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The twitter profile has been updated!',
        ]);
    }

    public function saveLinkedin()
    {
        $validated = $this->validate([
            'linkedin' => ['nullable', 'url'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['linkedin' => $validated['linkedin']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The linkedin profile has been updated!',
        ]);
    }

    public function savePhoto()
    {
        $validated = $this->validate([
            'photo_path' => ['nullable', 'url'],
        ]);

        $this->user->profile()->updateOrCreate(
            ['user_id' => $this->user->id],
            ['photo_path' => $validated['photo_path']]
        );

        $this->editing = null;
        $this->dispatch('profileUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Profile updated!',
            'message' => 'The profile photo has been updated!',
        ]);
    }
}
