<?php

namespace App\Livewire\Admin;

use App\Models\Product;
use Livewire\Attributes\Locked;
use Livewire\Component;

class ProductPhotos extends Component
{
    use SendsAdminNotifications;

    #[Locked]
    public int $product_id;

    public function render()
    {
        $photos = Product::find($this->product_id)->photos;

        return view('livewire.product-photos', compact('photos'));
    }

    public function updatePhotoSort($sorted)
    {
        $photo_ids_with_sort = collect($sorted)
            ->mapWithKeys(fn($item, $index) => [$item['value'] => ['sort' => $index + 1]])
            ->toArray();

        Product::query()->select('id')->find($this->product_id)
            ->photos()
            ->sync($photo_ids_with_sort);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Product updated!',
            'message' => 'The photo sort order has been updated!',
        ]);
    }

    public function add(int $photo_id)
    {
        $product = Product::query()->select('id')->find($this->product_id);

        if ($product->photos()->where('media_id', $photo_id)->exists()) {
            $this->sendAdminNotification([
                'level' => 'error',
                'title' => 'Error',
                'message' => 'The photo has already been added to the product!',
            ]);

            return;
        }


        $product->photos()
            ->attach($photo_id, ['sort' => $product->photos()->max('sort') + 1]);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Photo added!',
            'message' => 'The photo has been added!',
        ]);
    }

    public function remove(int $photo_id)
    {
        $product = Product::query()->select('id')->find($this->product_id)
            ->photos()
            ->detach($photo_id);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Photo removed!',
            'message' => 'The photo has been removed!',
        ]);
    }
}
