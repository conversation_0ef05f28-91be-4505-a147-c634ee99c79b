<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Livewire\Component;

class Reviews extends Component
{
    use HasPageWidgetSettings;
    use HasIdentifiers;
    use HasSizing;

    public function mount(int $page_id, array $widget)
    {
        $this->page_id = $page_id;
        $this->widget = $widget;

        $this->initializeWidget($widget);
    }

    protected function initializeWidget(array $widget): void
    {
        $this->initializeIdentifiers($widget['settings']);
        $this->initializeSizing($widget['settings']);
    }

    public function render()
    {
        return view('livewire.pages.widgets.reviews');
    }

    public function save()
    {
        $this->validate($this->rules());

        $this->savePageWidgetSettings([
            'name' => $this->name,
            'html_id' => $this->html_id,
            'max_width' => $this->max_width,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ]
        ]);

        $this->dispatch('widget-updated');
    }

    protected function rules()
    {
        return array_merge(
            $this->identiferRules(),
            $this->sizingRules()
        );
    }
}
