<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Illuminate\Validation\Rule;
use Livewire\Component;

class SideBySide extends Component
{
    use HasPageWidgetSettings;
    use HasIdentifiers;
    use HasSizing;
    use HasBackground;
    
    public string $image_position;
    public string $image_url = '';
    public string $text = '';

    public function mount(int $page_id, array $widget)
    {
        $this->page_id = $page_id;
        $this->widget = $widget;

        $this->initializeWidget($widget);
    }

    protected function initializeWidget(array $widget): void
    {
        $this->initializeIdentifiers($widget['settings']);
        $this->initializeSizing($widget['settings']);
        $this->initializeBackground($widget['settings']);

        $this->image_position = $widget['settings']['image_position'] ?? 'left';
        $this->image_url = $widget['settings']['image_url'] ?? '';
        $this->text = $widget['settings']['text'] ?? '';
    }

    public function render()
    {
        return view('livewire.pages.widgets.side-by-side');
    }

    public function save()
    {
        $this->validate($this->rules());
        
        $this->savePageWidgetSettings([
            'name' => $this->name,
            'html_id' => $this->html_id,
            'max_width' => $this->max_width,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ],
            'background' => [
                'color' => $this->background_color,
            ],
            'image_position' => $this->image_position,
            'image_url' => $this->image_url,
            'text' => $this->text,
        ]);

        $this->dispatch('widget-updated');
    }

    protected function rules()
    {
        return array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            $this->backgroundRules(),
            [
                'image_position' => ['required', Rule::in(['left', 'right'])],
                'image_url' => ['nullable', 'string'],
                'text' => ['nullable', 'string'],
            ]
        );
    }


}
