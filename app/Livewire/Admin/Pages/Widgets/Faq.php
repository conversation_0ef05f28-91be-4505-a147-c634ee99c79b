<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Livewire\Component;

class Faq extends Component
{
    use HasPageWidgetSettings;
    use HasIdentifiers;
    use HasSizing;

    public string $heading  = '';

    public array $faqs = [];

    protected $rules = [
        'heading' => ['nullable', 'string'],
        'faqs' => ['required', 'array'],
        'faqs.*.question' => ['required', 'string'],
        'faqs.*.answer' => ['required', 'string'],
    ];

    public function mount(int $page_id, array $widget)
    {
        $this->page_id = $page_id;
        $this->widget = $widget;

        $this->initializeWidget($widget);
    }

    protected function initializeWidget(array $widget): void
    {
        $this->initializeIdentifiers($widget['settings']);
        $this->initializeSizing($widget['settings']);

        $this->heading = $widget['settings']['heading'];
        $this->faqs = $widget['settings']['faqs'];
    }

    public function save()
    {
        $this->validate(array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            $this->rules
        ));

        $this->saveCurrentSettings();

        $this->dispatch('widget-updated');
    }

    protected function saveCurrentSettings(): void
    {
        $this->savePageWidgetSettings([
            'name' => $this->name,
            'html_id' => $this->html_id,
            'max_width' => $this->max_width,
            'padding' => [
                'top' => $this->padding_top,
                'bottom' => $this->padding_bottom,
            ],
            'heading' => $this->heading,
            'faqs' => $this->faqs,
        ]);
    }

    public function saveQuestion()
    {
        $this->validate(array_merge(
            $this->identiferRules(),
            $this->sizingRules(),
            $this->rules
        ));

        $this->saveCurrentSettings();
    }

    public function addQuestion(string $question, string $answer)
    {
        $this->faqs[] = [
            'question' => $question,
            'answer' => $answer
        ];

        $this->saveCurrentSettings();
    }

    public function deleteQuestion(int $index)
    {
        $this->faqs = array_values(array_filter(
            $this->faqs,
            fn($value, $key) => $key !== $index,
            ARRAY_FILTER_USE_BOTH
        ));

        $this->saveCurrentSettings();
    }

    public function render()
    {
        return view('livewire.pages.widgets.faq');
    }
}
