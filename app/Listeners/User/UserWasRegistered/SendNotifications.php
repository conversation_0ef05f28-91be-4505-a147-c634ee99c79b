<?php

namespace App\Listeners\User\UserWasRegistered;

use App\Events\User\UserWasRegistered;
use App\Mail\UserWelcome;
use Illuminate\Support\Facades\Mail;

class SendNotifications
{
    public function handle(UserWasRegistered $event): void
    {
        if (setting('send_customer_welcome_email', true)) {
            Mail::to($event->user->email)
                ->send(new UserWelcome($event->user->id));
        }
    }
}
