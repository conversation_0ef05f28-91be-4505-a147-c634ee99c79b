<?php

namespace App\Listeners\Subscription;

use App\Events\Subscription\SubscriptionWasSkipped;
use App\Models\Event;
use App\Models\RecurringOrder;

class RecordSubscriptionWasSkipped
{
    public function handle(SubscriptionWasSkipped $event)
    {
        Event::create([
            'user_id' => auth()->id() ?? $event->subscription->customer_id,
            'model_id' => $event->subscription->id,
            'model_type' => RecurringOrder::class,
            'event_id' => SubscriptionWasSkipped::class,
            'description' => 'The subscription was skipped',
            'metadata' => json_encode([
                'reorder_frequency' => $event->subscription->reorder_frequency,
                'old_delivery_date' => $event->old_delivery_date->format('Y-m-d H:i:s'),
                'new_delivery_date' => $event->new_delivery_date->format('Y-m-d H:i:s')
            ]),
            'created_at' => now()
        ]);
    }
}
