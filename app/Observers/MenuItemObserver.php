<?php

namespace App\Observers;

use App\Models\MenuItem;
use Illuminate\Support\Facades\Cache;

class MenuItemObserver
{
    public function saving(MenuItem $menuItem): void
    {
        Cache::tags('menu')->flush();
    }

    public function saved(MenuItem $menuItem): void
    {
        $menu = $menuItem->menu;

        if ($menuItem->menu->isSubmenu()) {
            $menu = $menuItem->menu->parent;
        }

        Cache::forget($menu->cacheKey());
    }

    public function deleting(MenuItem $menuItem): void
    {
        Cache::tags('menu')->flush();
    }

    public function deleted(MenuItem $menuItem): void
    {
        $menu = $menuItem->menu;

        if ($menuItem->menu->isSubmenu()) {
            $menu = $menuItem->menu->parent;
        }

        Cache::forget($menu->cacheKey());
    }
}
